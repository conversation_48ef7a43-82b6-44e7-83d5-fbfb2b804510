#!/usr/bin/env python3
"""
认证系统测试脚本

测试用户注册、登录、token验证等功能
"""

import requests
import json
import time
import os

# 配置
DEERFLOW_URL = "http://localhost:8000"

def test_send_verification_code():
    """测试发送验证码"""
    print("📧 测试发送验证码...")
    
    response = requests.post(f"{DEERFLOW_URL}/api/auth/send-verification", json={
        "email": "<EMAIL>"
    })
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 验证码发送成功: {result['message']}")
        return True
    else:
        print(f"   ❌ 发送失败: {response.text}")
        return False

def test_email_login():
    """测试邮箱验证码登录"""
    print("\n🔐 测试邮箱登录...")
    
    # 这里需要手动输入验证码（实际测试中）
    verification_code = input("请输入收到的验证码: ")
    
    response = requests.post(f"{DEERFLOW_URL}/api/auth/login/email", json={
        "email": "<EMAIL>",
        "verification_code": verification_code
    })
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 登录成功!")
        print(f"   用户ID: {result['user']['id']}")
        print(f"   邮箱: {result['user']['email']}")
        print(f"   剩余积分: {result['user']['remaining_credits']}")
        return result['access_token']
    else:
        print(f"   ❌ 登录失败: {response.text}")
        return None

def test_google_login():
    """测试Google登录"""
    print("\n🔐 测试Google登录...")
    
    # 这里需要真实的Google token（在实际测试中）
    google_token = input("请输入Google ID Token (可选，按回车跳过): ")
    
    if not google_token:
        print("   ⏭️ 跳过Google登录测试")
        return None
    
    response = requests.post(f"{DEERFLOW_URL}/api/auth/login/google", json={
        "google_token": google_token
    })
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ Google登录成功!")
        print(f"   用户ID: {result['user']['id']}")
        print(f"   邮箱: {result['user']['email']}")
        return result['access_token']
    else:
        print(f"   ❌ Google登录失败: {response.text}")
        return None

def test_get_user_info(token):
    """测试获取用户信息"""
    print("\n👤 测试获取用户信息...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{DEERFLOW_URL}/api/auth/me", headers=headers)
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 获取用户信息成功!")
        print(f"   用户ID: {result['id']}")
        print(f"   邮箱: {result['email']}")
        print(f"   总积分: {result['total_credits']}")
        print(f"   剩余积分: {result['remaining_credits']}")
        return True
    else:
        print(f"   ❌ 获取用户信息失败: {response.text}")
        return False

def test_record_token_usage(token):
    """测试记录token使用"""
    print("\n📊 测试记录token使用...")
    
    headers = {"Authorization": f"Bearer {token}"}
    test_data = {
        "thread_id": f"test-thread-{int(time.time())}",
        "non_reasoning_input_tokens": 100,
        "non_reasoning_output_tokens": 50,
        "reasoning_input_tokens": 200,
        "reasoning_output_tokens": 75,
        "conversation_summary": "测试对话摘要"
    }
    
    response = requests.post(
        f"{DEERFLOW_URL}/api/auth/token-usage",
        json=test_data,
        headers=headers
    )
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ token使用记录成功!")
        print(f"   对话ID: {result['chat_history_id']}")
        print(f"   总token数: {result['total_tokens']}")
        return test_data["thread_id"]
    else:
        print(f"   ❌ 记录失败: {response.text}")
        return None

def test_get_chat_history(token):
    """测试获取对话历史"""
    print("\n📜 测试获取对话历史...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{DEERFLOW_URL}/api/auth/chat-history", headers=headers)
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 获取对话历史成功!")
        print(f"   对话数量: {len(result)}")
        
        if result:
            latest_chat = result[0]
            print(f"   最新对话:")
            print(f"      Thread ID: {latest_chat['thread_id']}")
            print(f"      总token: {latest_chat['non_reasoning_input_tokens'] + latest_chat['non_reasoning_output_tokens'] + latest_chat['reasoning_input_tokens'] + latest_chat['reasoning_output_tokens']}")
            print(f"      创建时间: {latest_chat['created_at']}")
        
        return True
    else:
        print(f"   ❌ 获取对话历史失败: {response.text}")
        return False

def test_protected_api(token):
    """测试需要认证的API"""
    print("\n🔒 测试需要认证的API...")
    
    # 测试无token访问
    print("   测试无token访问...")
    response = requests.get(f"{DEERFLOW_URL}/api/auth/me")
    if response.status_code == 401:
        print("   ✅ 无token时正确返回401")
    else:
        print(f"   ⚠️ 预期401，实际返回: {response.status_code}")
    
    # 测试无效token
    print("   测试无效token...")
    headers = {"Authorization": "Bearer invalid-token"}
    response = requests.get(f"{DEERFLOW_URL}/api/auth/me", headers=headers)
    if response.status_code == 401:
        print("   ✅ 无效token时正确返回401")
    else:
        print(f"   ⚠️ 预期401，实际返回: {response.status_code}")
    
    # 测试有效token
    print("   测试有效token...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{DEERFLOW_URL}/api/auth/me", headers=headers)
    if response.status_code == 200:
        print("   ✅ 有效token正确通过认证")
        return True
    else:
        print(f"   ❌ 有效token认证失败: {response.status_code}")
        return False

def test_chat_with_auth(token):
    """测试带认证的聊天功能"""
    print("\n💬 测试带认证的聊天功能...")
    
    headers = {"Authorization": f"Bearer {token}"}
    chat_data = {
        "messages": [
            {
                "role": "user", 
                "content": "Hello, this is a test message for authentication."
            }
        ],
        "thread_id": f"auth-test-{int(time.time())}",
        "resources": [],
        "max_plan_iterations": 1,
        "max_step_num": 3,
        "max_search_results": 10,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": False
    }
    
    try:
        response = requests.post(
            f"{DEERFLOW_URL}/api/chat/stream",
            json=chat_data,
            headers=headers,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 带认证的聊天请求成功!")
            return True
        else:
            print(f"   ❌ 聊天请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ⚠️ 聊天测试异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 DeerFlow认证系统测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{DEERFLOW_URL}/docs", timeout=5)
        if response.status_code != 200:
            print("❌ DeerFlow服务器未运行，请先启动服务器")
            return
    except Exception as e:
        print(f"❌ 无法连接到DeerFlow服务器: {e}")
        print("请确保服务器正在运行: python -m uvicorn src.server.app:app --reload --host 0.0.0.0 --port 8000")
        return
    
    print("✅ DeerFlow服务器运行正常\n")
    
    # 测试发送验证码
    if not test_send_verification_code():
        print("❌ 验证码发送测试失败，请检查邮箱配置")
        return
    
    # 测试邮箱登录
    token = test_email_login()
    if not token:
        print("❌ 邮箱登录测试失败")
        return
    
    # 测试Google登录（可选）
    google_token = test_google_login()
    if google_token:
        token = google_token  # 使用Google登录的token
    
    print(f"\n🎫 使用的访问令牌: {token[:50]}...")
    
    # 测试用户信息
    test_get_user_info(token)
    
    # 测试记录token使用
    thread_id = test_record_token_usage(token)
    
    # 测试获取对话历史
    test_get_chat_history(token)
    
    # 测试认证保护
    test_protected_api(token)
    
    # 测试带认证的聊天
    test_chat_with_auth(token)
    
    print("\n🎉 认证系统测试完成!")
    print("\n📋 测试总结:")
    print("✅ 验证码发送")
    print("✅ 邮箱登录") 
    print("✅ 用户信息获取")
    print("✅ Token使用记录")
    print("✅ 对话历史查询")
    print("✅ 认证保护")
    print("✅ 带认证的API调用")

if __name__ == "__main__":
    main() 