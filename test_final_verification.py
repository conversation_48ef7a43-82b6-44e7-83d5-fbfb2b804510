#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.graph.nodes import human_feedback_node, research_team_node, _execute_agent_step
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_final_verification():
    """Final verification test that simulates the actual runtime scenario."""
    
    print("Final verification test - simulating actual runtime scenario...")
    
    # Step 1: Simulate the exact scenario from the logs
    print("\nStep 1: Simulating user choosing [PAPER_WRITING]")
    
    # This matches the plan from the actual logs
    plan_json = {
        "locale": "zh-CN",
        "has_enough_context": False,
        "thought": "用户需要一篇关于人工智能在医疗诊断中应用的学术论文，涵盖文献综述、技术分析和未来展望。目前的上下文不足以完成这篇论文，需要进行深入的信息收集。",
        "title": "人工智能在医疗诊断中应用的学术论文所需信息收集计划",
        "steps": [
            {
                "need_web_search": True,
                "title": "收集人工智能在医疗诊断中应用的文献综述信息",
                "description": "收集关于人工智能在医疗诊断中应用的现有学术研究...",
                "step_type": "research",
                "execution_res": None
            },
            {
                "need_web_search": True,
                "title": "收集人工智能在医疗诊断中的技术分析信息",
                "description": "收集用于医疗诊断的人工智能技术的详细信息...",
                "step_type": "research",
                "execution_res": None
            },
            {
                "need_web_search": True,
                "title": "收集人工智能在医疗诊断中应用的未来展望信息",
                "description": "收集关于人工智能在医疗诊断领域未来发展的趋势和展望信息...",
                "step_type": "research",
                "execution_res": None
            }
        ]
    }
    
    # Initial state that matches the actual runtime state
    initial_state = {
        "messages": [HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望")],
        "locale": "zh-CN",
        "observations": [],
        "plan_iterations": 0,
        "current_plan": json.dumps(plan_json, ensure_ascii=False),
        "final_report": "",
        "auto_accepted_plan": False,
        "enable_background_investigation": True,
        "background_investigation_results": None,
    }
    
    # Mock the interrupt to return [PAPER_WRITING]
    import unittest.mock
    with unittest.mock.patch('src.graph.nodes.interrupt', return_value="[PAPER_WRITING]"):
        result = human_feedback_node(initial_state)
    
    print(f"human_feedback_node result: {result}")
    print(f"paper_writing_mode set: {result.update.get('paper_writing_mode', 'NOT FOUND')}")
    print(f"Next node: {result.goto}")
    
    assert result.goto == "research_team", f"Expected 'research_team', got '{result.goto}'"
    assert result.update.get("paper_writing_mode") == True, f"Expected paper_writing_mode=True, got {result.update.get('paper_writing_mode')}"
    print("✅ User choice [PAPER_WRITING] correctly sets paper_writing_mode=True")
    
    # Step 2: Simulate the state after human_feedback_node
    print("\nStep 2: Simulating research_team_node with paper_writing_mode=True")
    
    plan = result.update["current_plan"]
    state_after_feedback = {
        "messages": initial_state["messages"],
        "locale": result.update["locale"],
        "observations": [],
        "plan_iterations": result.update["plan_iterations"],
        "current_plan": plan,
        "final_report": "",
        "auto_accepted_plan": False,
        "enable_background_investigation": True,
        "background_investigation_results": None,
        "paper_writing_mode": result.update["paper_writing_mode"],  # This should be True
    }
    
    print(f"State keys: {list(state_after_feedback.keys())}")
    print(f"paper_writing_mode in state: {state_after_feedback.get('paper_writing_mode')}")
    
    # Test research_team_node with incomplete research
    result2 = research_team_node(state_after_feedback)
    print(f"research_team_node result (incomplete): {result2}")
    assert result2.goto == "researcher", f"Expected 'researcher', got '{result2.goto}'"
    print("✅ research_team_node correctly routes to researcher when research incomplete")
    
    # Step 3: Simulate completing all research steps
    print("\nStep 3: Simulating completed research")
    
    # Complete all research steps
    for step in plan.steps:
        step.execution_res = f"研究结果：{step.title} 已完成"
    
    state_with_completed_research = {
        **state_after_feedback,
        "current_plan": plan,
    }
    
    print(f"State with completed research - paper_writing_mode: {state_with_completed_research.get('paper_writing_mode')}")
    
    # Test research_team_node with completed research
    result3 = research_team_node(state_with_completed_research)
    print(f"research_team_node result (completed): {result3}")
    print(f"Expected: goto='outline_writer', Actual: goto='{result3.goto}'")
    
    assert result3.goto == "outline_writer", f"Expected 'outline_writer', got '{result3.goto}'"
    print("✅ research_team_node correctly routes to outline_writer when research complete")
    
    print("\n🎉 Final verification test passed! The paper writing workflow is now working correctly!")
    print("\nWorkflow summary:")
    print("1. User chooses [PAPER_WRITING] → paper_writing_mode=True")
    print("2. research_team → researcher (execute research)")
    print("3. research_team (after completion) → outline_writer")
    print("4. outline_writer → outline_feedback → paper_writer → reporter")
    
    return True

if __name__ == "__main__":
    try:
        success = test_final_verification()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 