#!/usr/bin/env python3
"""
Test script for the paper writing functionality in DeerFlow.
This script demonstrates how to use the new outline writer and paper writer nodes.
"""

import json
from src.graph.builder import build_graph
from src.graph.types import State

def test_paper_writing_workflow():
    """Test the paper writing workflow with a sample research query."""
    
    # Create the graph
    graph = build_graph()
    
    # Initial state with a research query about AI in medical diagnosis
    initial_state = State(
        messages=[{
            "role": "user", 
            "content": "撰写一篇关于人工智能技术在医疗诊断领域应用的系统性文献综述，涵盖2018-2023年间的研究进展，重点分析当前技术成果、临床应用案例、存在的挑战以及未来发展方向。"
        }],
        locale="zh-CN",
        auto_accepted_plan=True,  # Auto-accept plan for testing
        auto_accepted_outline=True,  # Auto-accept outline for testing
        enable_background_investigation=True
    )
    
    print("🚀 Starting paper writing workflow test...")
    print(f"📝 Research Query: {initial_state['messages'][0]['content']}")
    print("=" * 80)
    
    try:
        # Run the workflow
        config = {"configurable": {"thread_id": "test_paper_writing"}}
        
        # Stream the workflow execution
        for event in graph.stream(initial_state, config=config):
            node_name = list(event.keys())[0]
            node_output = event[node_name]
            
            print(f"\n📍 Node: {node_name}")
            print("-" * 40)
            
            if node_name == "coordinator":
                print("🎯 Coordinator: Determining workflow path...")
                
            elif node_name == "background_investigator":
                print("🔍 Background Investigation: Gathering initial context...")
                
            elif node_name == "planner":
                print("📋 Planner: Creating research plan...")
                if "current_plan" in node_output:
                    plan = node_output["current_plan"]
                    if isinstance(plan, str):
                        try:
                            plan_data = json.loads(plan)
                            print(f"   Title: {plan_data.get('title', 'N/A')}")
                            print(f"   Steps: {len(plan_data.get('steps', []))}")
                        except:
                            print("   Plan created (parsing details...)")
                    else:
                        print(f"   Title: {plan.title if hasattr(plan, 'title') else 'N/A'}")
                        print(f"   Steps: {len(plan.steps) if hasattr(plan, 'steps') else 0}")
                        
            elif node_name == "research_team":
                print("👥 Research Team: Coordinating research tasks...")
                
            elif node_name == "researcher":
                print("🔬 Researcher: Conducting research...")
                
            elif node_name == "outline_writer":
                print("📝 Outline Writer: Creating paper outline...")
                if "paper_outline" in node_output:
                    try:
                        outline_data = json.loads(node_output["paper_outline"])
                        print(f"   Title: {outline_data.get('title', 'N/A')}")
                        print(f"   Sections: {len(outline_data.get('sections', []))}")
                        print(f"   Total Words: {outline_data.get('totalWords', 'N/A')}")
                    except:
                        print("   Outline created (parsing details...)")
                        
            elif node_name == "paper_writer":
                print("✍️ Paper Writer: Writing paper content...")
                if "current_section_index" in node_output:
                    section_idx = node_output["current_section_index"]
                    print(f"   Completed sections: {section_idx}")
                if "final_paper" in node_output:
                    paper_length = len(node_output["final_paper"])
                    print(f"   📄 Final paper generated ({paper_length} characters)")
                    
            elif node_name == "reporter":
                print("📊 Reporter: Finalizing output...")
                if "final_report" in node_output:
                    report_length = len(node_output["final_report"])
                    print(f"   📄 Final report ready ({report_length} characters)")
                    
            # Show any messages
            if "messages" in node_output and node_output["messages"]:
                last_message = node_output["messages"][-1]
                if hasattr(last_message, 'content'):
                    content_preview = last_message.content[:100] + "..." if len(last_message.content) > 100 else last_message.content
                    print(f"   💬 Message: {content_preview}")
        
        print("\n" + "=" * 80)
        print("✅ Paper writing workflow test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during workflow execution: {e}")
        import traceback
        traceback.print_exc()

def test_outline_generation():
    """Test just the outline generation functionality."""
    print("\n🧪 Testing outline generation...")
    
    # This would be a more focused test for outline generation
    # You can implement specific outline testing logic here
    pass

if __name__ == "__main__":
    print("🦌 DeerFlow Paper Writing Test Suite")
    print("=" * 50)
    
    # Run the main test
    test_paper_writing_workflow()
    
    # Run additional tests
    test_outline_generation()
    
    print("\n🎉 All tests completed!") 