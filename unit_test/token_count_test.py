from google import genai

client = genai.Client(api_key="AIzaSyDxffDQFk4DydQl-vAeFjYYV-gZsCkUD0M")
prompt = '''{
  "locale": "zh-CN",
  "has_enough_context": false,
  "thought": "用户要求了解deer-flow对深度研究的影响。为了全面回答这个问题，需要深入探讨deer-flow的技术细节、在不同深度研究领域的应用案例、带来的具体益处以及潜在挑战或局限性。",
  "title": "Deer-flow对深度研究的影响的深度研究",
  "task_type": "综合研究",
  "writing_style": "学术严谨",
  "target_audience": "学术研究人员",
  "word_count": "3000-5000",
  "need_outline": true,
  "steps": [
    {
      "need_search": false,
      "title": "概念分析与框架构建",
      "description": "分析deer-flow的核心技术原理和设计思想，探讨其与传统研究方法的区别与联系。构建分析框架，明确评估deer-flow影响的关键维度，例如：数据处理效率、模型构建能力、结果解释性、跨领域应用潜力等。",
      "step_type": "thinking"
    },
    {
      "need_search": true,
      "title": "Deer-flow在不同深度研究领域的应用与案例研究",
      "description": "收集和分析deer-flow在具体深度研究领域（如生物信息学、物理学、社会科学、金融建模等）的应用案例。重点关注其如何改进研究流程、解决复杂问题、产生新发现。收集相关的研究论文、技术报告、会议记录等信息。",
      "step_type": "research"
    },
    {
      "need_search": false,
      "title": "影响评估与综合分析",
      "description": "基于概念分析和案例研究，评估deer-flow对深度研究的具体影响，包括正面影响（如加速研究进程、提升研究精度、促进交叉学科融合）和潜在的挑战或局限性（如技术门槛、数据依赖、结果可解释性问题）。综合现有信息，对deer-flow在未来深度研究中的发展趋势进行预测性分析。",
      "step_type": "thinking"
    }
  ]
}'''

# Count tokens using the new client method.
total_tokens = client.models.count_tokens(
    model="gemini-2.5-flash-preview-05-20", contents=prompt
)
print("total_tokens: ", total_tokens)
# ( e.g., total_tokens: 10 )