#!/usr/bin/env python3
"""
测试修复后的文件上传功能

验证字段名修复后(files -> file)的上传是否正常工作
"""

import requests
import json
import os
import io
import time

# 配置
os.environ["RAGFLOW_API_URL"] = "http://localhost"
os.environ["RAGFLOW_API_KEY"] = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"
os.environ["RAG_PROVIDER"] = "ragflow"

DEERFLOW_URL = "http://localhost:8000"
RAGFLOW_URL = "http://localhost"
API_KEY = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"

def test_complete_workflow():
    """测试完整的工作流程"""
    print("🔧 测试修复后的RAG文件上传功能")
    print("=" * 50)
    
    # 步骤1: 创建数据集
    print("1️⃣ 创建数据集...")
    dataset_payload = {
        "name": f"Fixed Upload Test {int(time.time())}",
        "description": "测试修复后的文件上传功能",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
    }
    
    # 首先直接调用RAGFlow API创建数据集，避免DeerFlow服务器重启问题
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(f"{RAGFLOW_URL}/api/v1/datasets", json=dataset_payload, headers=headers)
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            dataset_id = result["data"]["id"]
            dataset_name = result["data"]["name"]
            print(f"✅ 数据集创建成功!")
            print(f"   ID: {dataset_id}")
            print(f"   名称: {dataset_name}")
        else:
            print(f"❌ 创建失败: {result}")
            return
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return
    
    # 步骤2: 使用DeerFlow上传文件 (测试修复后的代码)
    print(f"\n2️⃣ 通过DeerFlow上传文件...")
    
    # 创建测试文件内容
    test_content = f"""
这是一个修复测试文档 - {int(time.time())}

测试内容：
- RAGFlow API字段名修复验证
- 从 "files" 修复为 "file" 
- 应该能在RAGFlow后台看到此文档

文件信息：
- 文件格式: 纯文本
- 编码: UTF-8
- 上传时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

这个文档用于验证DeerFlow与RAGFlow的集成是否正常工作。
    """.strip()
    
    # 准备文件上传
    files = {
        'file': ('fixed_test.txt', io.BytesIO(test_content.encode('utf-8')), 'text/plain')
    }
    data = {
        'dataset_id': dataset_id
    }
    
    try:
        response = requests.post(f"{DEERFLOW_URL}/api/rag/upload", files=files, data=data, timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("success"):
                print(f"   ✅ DeerFlow上传成功!")
                
                # 检查返回的文档信息
                data_info = result.get("data", {})
                documents = data_info.get("documents", [])
                if documents:
                    for doc in documents:
                        print(f"     文档ID: {doc.get('id')}")
                        print(f"     文档名: {doc.get('name')}")
                        print(f"     文档大小: {doc.get('size')} bytes")
                else:
                    print(f"   ⚠️ 没有返回文档详情")
            else:
                print(f"   ❌ 上传失败: {result.get('message')}")
                return
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        return
    
    # 步骤3: 验证RAGFlow后台
    print(f"\n3️⃣ 验证RAGFlow后台...")
    
    try:
        response = requests.get(f"{RAGFLOW_URL}/api/v1/datasets/{dataset_id}/documents", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                documents_info = result.get("data", {})
                documents = documents_info.get("docs", [])
                
                print(f"   📋 RAGFlow后台文档数量: {len(documents)}")
                
                # 查找我们刚上传的文档
                uploaded_doc = None
                for doc in documents:
                    if "fixed_test.txt" in doc.get("name", ""):
                        uploaded_doc = doc
                        break
                
                if uploaded_doc:
                    print(f"   ✅ 成功找到上传的文档!")
                    print(f"     文档名: {uploaded_doc.get('name')}")
                    print(f"     文档ID: {uploaded_doc.get('id')}")
                    print(f"     文档大小: {uploaded_doc.get('size')} bytes")
                    print(f"     处理状态: {uploaded_doc.get('run')}")
                    print(f"     创建时间: {uploaded_doc.get('create_date')}")
                    
                    print(f"\n🎉 测试成功! 文件上传修复生效!")
                    return True
                else:
                    print(f"   ❌ 在RAGFlow后台未找到上传的文档")
                    print(f"   现有文档:")
                    for doc in documents:
                        print(f"     - {doc.get('name')}")
            else:
                print(f"   ❌ RAGFlow API错误: {result}")
        else:
            print(f"   ❌ 查询失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 验证异常: {e}")
    
    return False

def test_postman_instructions():
    """提供Postman测试说明"""
    print(f"\n📋 Postman测试步骤:")
    print(f"   1. 创建数据集 (可选，也可以使用现有的)")
    print(f"      POST {DEERFLOW_URL}/api/rag/datasets")
    print(f"      Body: {{'name': 'My Test Dataset', 'description': 'Test'}}")
    print(f"")
    print(f"   2. 上传文件 (重点测试)")
    print(f"      POST {DEERFLOW_URL}/api/rag/upload")
    print(f"      Body: form-data")
    print(f"        - file: [选择您的PDF/文档文件]")
    print(f"        - dataset_id: [数据集ID]")
    print(f"")
    print(f"   3. 验证上传结果")
    print(f"      - 检查API响应是否包含文档信息")
    print(f"      - 登录RAGFlow后台查看对应数据集")
    print(f"      - 确认文件已出现在文档列表中")

if __name__ == "__main__":
    success = test_complete_workflow()
    
    if success:
        print(f"\n✅ 修复验证成功!")
        print(f"问题解决方案总结:")
        print(f"- 问题: RAGFlow API字段名应为 'file' 而不是 'files'")
        print(f"- 修复: 在 src/rag/ragflow.py 中将字段名改为 'file'")
        print(f"- 结果: 文件现在能正确上传到RAGFlow后台")
    else:
        print(f"\n❌ 还需要重启DeerFlow服务器来加载修复的代码")
        print(f"请执行: python -m uvicorn src.server.app:app --reload --host 0.0.0.0 --port 8000")
    
    test_postman_instructions() 