#!/usr/bin/env python3
"""
测试checkpoint恢复功能的脚本
"""

import asyncio
import logging
import sys
import os
from uuid import uuid4

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.graph.builder import build_graph_with_memory
from langchain_core.messages import HumanMessage

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_checkpoint_recovery():
    """测试checkpoint恢复功能"""
    
    # 构建带有checkpoint的graph
    graph = build_graph_with_memory()
    
    # 创建测试配置
    thread_id = str(uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    
    # 准备测试输入
    messages = [
        HumanMessage(content="请帮我研究一下人工智能的发展历史")
    ]
    
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": True,
        "enable_background_investigation": True,
        "user_id": 1,
        "thread_id": thread_id,
    }
    
    logger.info("开始执行graph...")
    
    # 模拟执行几个步骤后中断
    step_count = 0
    max_steps_before_interrupt = 3
    
    try:
        async for agent, _, event_data in graph.astream(
            input_,
            config=config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            step_count += 1
            logger.info(f"步骤 {step_count}: 执行节点 {agent}")
            logger.info(f"事件数据: {event_data}")
            
            # 模拟在第3步后中断
            if step_count >= max_steps_before_interrupt:
                logger.info(f"模拟中断，已执行 {step_count} 步")
                break
                
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
    
    # 获取当前checkpoint状态
    logger.info("获取当前checkpoint状态...")
    current_state = graph.get_state(config)
    logger.info(f"当前状态: next={current_state.next}")
    logger.info(f"Checkpoint ID: {current_state.config.get('configurable', {}).get('checkpoint_id')}")
    logger.info(f"状态值: {list(current_state.values.keys())}")
    
    # 测试从checkpoint恢复执行
    if current_state.next:  # 如果还有下一步要执行
        logger.info("从checkpoint恢复执行...")
        
        resume_step_count = 0
        try:
            # 从checkpoint恢复执行，传入None作为input
            async for agent, _, event_data in graph.astream(
                None,  # 从checkpoint恢复时传入None
                config=current_state.config,  # 使用checkpoint配置
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                resume_step_count += 1
                logger.info(f"恢复步骤 {resume_step_count}: 执行节点 {agent}")
                logger.info(f"恢复事件数据: {event_data}")
                
                # 限制恢复执行的步数以避免无限循环
                if resume_step_count >= 5:
                    logger.info("达到恢复执行步数限制，停止")
                    break
                    
        except Exception as e:
            logger.error(f"恢复执行过程中出错: {e}")
    else:
        logger.info("没有下一步要执行，graph已完成")
    
    # 获取最终状态
    final_state = graph.get_state(config)
    logger.info(f"最终状态: next={final_state.next}")
    logger.info(f"最终Checkpoint ID: {final_state.config.get('configurable', {}).get('checkpoint_id')}")

async def test_state_history():
    """测试状态历史功能"""
    
    graph = build_graph_with_memory()
    thread_id = str(uuid4())
    config = {"configurable": {"thread_id": thread_id}}
    
    # 简单执行一个完整的流程
    messages = [HumanMessage(content="简单测试")]
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": True,
        "enable_background_investigation": True,
        "user_id": 1,
        "thread_id": thread_id,
    }
    
    logger.info("执行完整流程...")
    step_count = 0
    try:
        async for agent, _, event_data in graph.astream(
            input_,
            config=config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            step_count += 1
            logger.info(f"步骤 {step_count}: {agent}")
            if step_count >= 2:  # 限制步数
                break
    except Exception as e:
        logger.error(f"执行出错: {e}")
    
    # 获取状态历史
    logger.info("获取状态历史...")
    try:
        history = list(graph.get_state_history(config))
        logger.info(f"找到 {len(history)} 个历史状态:")
        
        for i, state in enumerate(history):
            logger.info(f"  状态 {i}: next={state.next}, checkpoint_id={state.config.get('configurable', {}).get('checkpoint_id')}")
            
    except Exception as e:
        logger.error(f"获取状态历史出错: {e}")

if __name__ == "__main__":
    print("测试checkpoint恢复功能...")
    
    # 运行测试
    asyncio.run(test_checkpoint_recovery())
    
    print("\n" + "="*50 + "\n")
    
    print("测试状态历史功能...")
    asyncio.run(test_state_history())
    
    print("测试完成!")
