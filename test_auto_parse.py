#!/usr/bin/env python3
"""
测试自动解析功能

验证在上传文档后自动启动解析的功能
"""

import requests
import json
import os
import io
import time

# 配置
os.environ["RAGFLOW_API_URL"] = "http://localhost"
os.environ["RAGFLOW_API_KEY"] = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"
os.environ["RAG_PROVIDER"] = "ragflow"

DEERFLOW_URL = "http://localhost:8000"
RAGFLOW_URL = "http://localhost"
API_KEY = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"

def create_test_dataset():
    """创建测试数据集"""
    print("📁 创建测试数据集...")
    
    dataset_payload = {
        "name": f"Auto Parse Test {int(time.time())}",
        "description": "测试自动解析功能的数据集"
    }
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(f"{RAGFLOW_URL}/api/v1/datasets", json=dataset_payload, headers=headers)
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            dataset_id = result["data"]["id"]
            dataset_name = result["data"]["name"]
            print(f"✅ 数据集创建成功!")
            print(f"   ID: {dataset_id}")
            print(f"   名称: {dataset_name}")
            return dataset_id
        else:
            print(f"❌ 创建失败: {result}")
            return None
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return None

def test_auto_parse_upload(dataset_id, auto_parse=True):
    """测试带自动解析的文档上传"""
    print(f"\n📤 测试文档上传 (auto_parse={auto_parse})...")
    
    # 创建测试文件内容
    test_content = f"""
自动解析测试文档 - {int(time.time())}

这是一个用于测试RAGFlow自动解析功能的文档。

主要内容：
1. 文档上传测试
2. 自动解析启动验证
3. 解析状态检查

测试场景：
- 验证文档能成功上传到RAGFlow
- 确认上传后自动触发解析
- 检查解析状态和结果

文档信息：
- 文件类型: 纯文本
- 编码: UTF-8
- 上传时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 自动解析: {"启用" if auto_parse else "禁用"}

这个文档应该被RAGFlow正确解析并建立索引，以便后续的语义检索使用。
    """.strip()
    
    # 准备文件上传
    files = {
        'file': ('auto_parse_test.txt', io.BytesIO(test_content.encode('utf-8')), 'text/plain')
    }
    data = {
        'dataset_id': dataset_id,
        'auto_parse': str(auto_parse).lower()  # FastAPI Form expects string
    }
    
    try:
        response = requests.post(f"{DEERFLOW_URL}/api/rag/upload", files=files, data=data, timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("success"):
                print(f"   ✅ 上传成功!")
                
                # 检查自动解析信息
                data_info = result.get("data", {})
                if data_info.get("parse_started") is not None:
                    if data_info.get("parse_started"):
                        print(f"   🔄 自动解析已启动!")
                        parsed_ids = data_info.get("parsed_document_ids", [])
                        if parsed_ids:
                            print(f"      解析的文档ID: {parsed_ids}")
                            return parsed_ids[0] if parsed_ids else None
                    else:
                        print(f"   ⚠️ 自动解析未启动")
                        parse_error = data_info.get("parse_error")
                        if parse_error:
                            print(f"      错误信息: {parse_error}")
                else:
                    print(f"   ℹ️ 未包含解析状态信息 (可能因为auto_parse=False)")
                
                # 返回文档ID用于后续检查
                documents = data_info.get("documents", [])
                if documents:
                    return documents[0].get('id')
                    
            else:
                print(f"   ❌ 上传失败: {result.get('message')}")
                return None
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        return None

def check_document_status(dataset_id, document_id):
    """检查文档解析状态"""
    print(f"\n🔍 检查文档解析状态...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{RAGFLOW_URL}/api/v1/datasets/{dataset_id}/documents", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                documents_info = result.get("data", {})
                documents = documents_info.get("docs", [])
                
                # 查找目标文档
                target_doc = None
                for doc in documents:
                    if doc.get("id") == document_id:
                        target_doc = doc
                        break
                
                if target_doc:
                    print(f"   📄 文档信息:")
                    print(f"      名称: {target_doc.get('name')}")
                    print(f"      状态: {target_doc.get('run')}")
                    print(f"      令牌数: {target_doc.get('token_count', 0)}")
                    print(f"      分块数: {target_doc.get('chunk_count', 0)}")
                    print(f"      创建时间: {target_doc.get('create_date')}")
                    print(f"      更新时间: {target_doc.get('update_date')}")
                    
                    # 检查解析状态
                    run_status = target_doc.get('run', '')
                    if run_status == 'DONE':  # 解析成功
                        print(f"   ✅ 解析成功!")
                        return True
                    elif run_status == 'RUNNING':  # 解析中
                        print(f"   🔄 解析进行中...")
                        return False
                    elif run_status == 'FAIL':  # 解析失败
                        print(f"   ❌ 解析失败!")
                        return False
                    elif run_status == 'UNSTART':  # 未开始
                        print(f"   ⏳ 解析未开始...")
                        return False
                    else:
                        print(f"   ⚠️ 未知解析状态: {run_status}")
                        return False
                else:
                    print(f"   ❌ 未找到文档 ID: {document_id}")
                    return False
            else:
                print(f"   ❌ API错误: {result}")
                return False
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查异常: {e}")
        return False

def test_retrieval(dataset_id):
    """测试解析后的检索功能"""
    print(f"\n🔍 测试文档检索...")
    
    try:
        # 通过DeerFlow API进行检索测试
        response = requests.post(
            f"{DEERFLOW_URL}/api/chat/stream",
            json={
                "messages": [
                    {
                        "role": "user",
                        "content": "自动解析测试文档的主要内容是什么？"
                    }
                ],
                "resources": [
                    {
                        "uri": f"rag://dataset/{dataset_id}",
                        "title": "Auto Parse Test Dataset",
                        "description": "测试自动解析功能的数据集"
                    }
                ],
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            # 读取流式响应
            content = ""
            for line in response.text.split('\n'):
                if line.startswith('data: '):
                    content += line[6:]  # Remove 'data: ' prefix
            
            print(f"   ✅ 检索测试成功!")
            print(f"   📝 检索结果摘要: {content[:200]}...")
            return True
        else:
            print(f"   ❌ 检索失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 检索异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 RAGFlow自动解析功能测试")
    print("=" * 50)
    
    # 步骤1: 创建数据集
    dataset_id = create_test_dataset()
    if not dataset_id:
        print("❌ 数据集创建失败，测试终止")
        return
    
    # 步骤2: 测试启用自动解析的上传
    print(f"\n🧪 测试场景1: 启用自动解析")
    document_id = test_auto_parse_upload(dataset_id, auto_parse=True)
    
    if document_id:
        # 等待一下让解析开始
        print(f"\n⏳ 等待5秒让解析开始...")
        time.sleep(5)
        
        # 检查解析状态（可能需要等待）
        max_wait = 30  # 最多等待30秒
        wait_interval = 5
        
        for i in range(0, max_wait, wait_interval):
            print(f"\n📊 检查解析进度 ({i+wait_interval}/{max_wait}s)...")
            parse_complete = check_document_status(dataset_id, document_id)
            
            if parse_complete:
                print(f"\n🎉 解析完成! 开始检索测试...")
                # 测试检索
                test_retrieval(dataset_id)
                break
            else:
                if i + wait_interval < max_wait:
                    print(f"   ⏳ 继续等待解析完成...")
                    time.sleep(wait_interval)
                else:
                    print(f"   ⚠️ 解析超时，但这可能是正常的（大文档需要更长时间）")
    
    # 步骤3: 测试禁用自动解析的上传
    print(f"\n🧪 测试场景2: 禁用自动解析")
    document_id2 = test_auto_parse_upload(dataset_id, auto_parse=False)
    
    if document_id2:
        print(f"   ℹ️ 禁用自动解析时，需要手动调用解析API")
        print(f"   可以使用: POST /api/rag/documents/parse")
        print(f"   文档ID: {document_id2}")
    
    print(f"\n✅ 自动解析功能测试完成!")
    print(f"📋 测试总结:")
    print(f"   - 数据集ID: {dataset_id}")
    print(f"   - 测试了自动解析启用/禁用场景")
    print(f"   - 验证了解析状态检查")
    print(f"   - 测试了解析后的文档检索")

if __name__ == "__main__":
    main() 