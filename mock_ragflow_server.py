#!/usr/bin/env python3
"""
Mock RAGFlow Server for Testing

This creates a simple mock server that mimics RAGFlow API endpoints
for testing purposes when the real demo server is not accessible.
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import json
import uuid
from datetime import datetime

app = FastAPI(title="Mock RAGFlow Server", version="1.0.0")

# In-memory storage for mock data
datasets = {}
documents = {}


class CreateDatasetRequest(BaseModel):
    name: str
    description: str = ""
    language: str = "English"
    embedding_model: str = "BAAI/bge-large-en-v1.5@BAAI"
    permission: str = "me"
    document_count: int = 0
    chunk_count: int = 0
    parse_method: str = "naive"
    parser_config: Dict[str, Any] = {}


class DocumentParseRequest(BaseModel):
    document_ids: List[str]


class RetrievalRequest(BaseModel):
    question: str
    dataset_ids: List[str] = []
    document_ids: List[str] = []
    page_size: int = 10


@app.get("/api/v1/datasets")
async def list_datasets(name: Optional[str] = None):
    """List all datasets."""
    filtered_datasets = []
    for dataset in datasets.values():
        if name is None or name.lower() in dataset["name"].lower():
            filtered_datasets.append(dataset)
    
    return {
        "retcode": 0,
        "retmsg": "success",
        "data": filtered_datasets
    }


@app.post("/api/v1/datasets")
async def create_dataset(request: CreateDatasetRequest):
    """Create a new dataset."""
    dataset_id = str(uuid.uuid4())
    
    dataset = {
        "id": dataset_id,
        "name": request.name,
        "description": request.description,
        "language": request.language,
        "embedding_model": request.embedding_model,
        "permission": request.permission,
        "document_count": 0,
        "chunk_count": 0,
        "parse_method": request.parse_method,
        "parser_config": request.parser_config,
        "create_time": datetime.now().isoformat(),
        "status": "1"
    }
    
    datasets[dataset_id] = dataset
    
    return {
        "retcode": 0,
        "retmsg": "success",
        "data": dataset
    }


@app.post("/api/v1/datasets/{dataset_id}/documents")
async def upload_documents(dataset_id: str, files: List[UploadFile] = File(...)):
    """Upload documents to a dataset."""
    if dataset_id not in datasets:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    uploaded_docs = []
    
    for file in files:
        doc_id = str(uuid.uuid4())
        content = await file.read()
        
        # Handle different file types
        content_text = ""
        if file.content_type and file.content_type.startswith('text/'):
            # Text files
            content_text = content.decode('utf-8', errors='ignore')
        elif file.filename and file.filename.lower().endswith(('.txt', '.md')):
            # Text files by extension
            content_text = content.decode('utf-8', errors='ignore')
        elif file.content_type == 'application/pdf':
            # PDF files - simulate content extraction
            content_text = f"[PDF Content] This is a simulated extraction from PDF file: {file.filename}. " \
                          f"The file contains {len(content)} bytes of PDF data. " \
                          f"In a real implementation, this would be extracted using a PDF parser."
        elif file.filename and file.filename.lower().endswith('.pdf'):
            # PDF by extension
            content_text = f"[PDF Content] This is a simulated extraction from PDF file: {file.filename}. " \
                          f"The file contains {len(content)} bytes of PDF data. " \
                          f"In a real implementation, this would be extracted using a PDF parser."
        elif file.content_type and 'word' in file.content_type.lower():
            # Word documents
            content_text = f"[Word Document] This is a simulated extraction from Word file: {file.filename}. " \
                          f"The file contains {len(content)} bytes of document data. " \
                          f"In a real implementation, this would be extracted using a Word parser."
        else:
            # Unknown or binary files
            content_text = f"[Binary File] File: {file.filename}, Type: {file.content_type}, Size: {len(content)} bytes. " \
                          f"This file type is not directly readable as text."
        
        document = {
            "id": doc_id,
            "name": file.filename,
            "size": len(content),
            "type": file.content_type or "application/octet-stream",
            "dataset_id": dataset_id,
            "status": "1",
            "run": "UNSTART",
            "progress": 0,
            "chunk_count": 0,
            "token_count": 0,
            "create_time": datetime.now().isoformat(),
            "content": content_text,
            "raw_content": content  # Store raw bytes for potential future use
        }
        
        documents[doc_id] = document
        uploaded_docs.append({
            "id": doc_id,
            "name": file.filename,
            "status": "uploaded",
            "size": len(content),
            "type": file.content_type or "application/octet-stream"
        })
        
        # Update dataset document count
        datasets[dataset_id]["document_count"] += 1
    
    return {
        "retcode": 0,
        "retmsg": "success",
        "data": uploaded_docs
    }


@app.get("/api/v1/datasets/{dataset_id}/documents")
async def list_documents(
    dataset_id: str, 
    keywords: Optional[str] = None, 
    page: int = 1, 
    page_size: int = 30
):
    """List documents in a dataset."""
    if dataset_id not in datasets:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    # Filter documents by dataset_id
    dataset_docs = []
    for doc in documents.values():
        if doc["dataset_id"] == dataset_id:
            if keywords is None or keywords.lower() in doc["name"].lower():
                dataset_docs.append({
                    "id": doc["id"],
                    "name": doc["name"],
                    "size": doc["size"],
                    "run": doc["run"],
                    "status": doc["status"],
                    "chunk_count": doc["chunk_count"],
                    "token_count": doc["token_count"],
                    "create_time": doc["create_time"]
                })
    
    # Simple pagination
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_docs = dataset_docs[start_idx:end_idx]
    
    return {
        "retcode": 0,
        "retmsg": "success",
        "data": paginated_docs
    }


@app.post("/api/v1/datasets/{dataset_id}/documents/parse")
async def parse_documents(dataset_id: str, request: DocumentParseRequest):
    """Start parsing documents."""
    if dataset_id not in datasets:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    parsed_count = 0
    for doc_id in request.document_ids:
        if doc_id in documents:
            # Simulate parsing
            documents[doc_id]["run"] = "RUNNING"
            documents[doc_id]["progress"] = 50
            
            # Simulate completion after a moment
            import time
            # In real implementation, this would be async
            documents[doc_id]["run"] = "DONE"
            documents[doc_id]["progress"] = 100
            documents[doc_id]["chunk_count"] = len(documents[doc_id]["content"].split()) // 100
            documents[doc_id]["token_count"] = len(documents[doc_id]["content"].split())
            
            parsed_count += 1
    
    return {
        "retcode": 0,
        "retmsg": f"Started parsing {parsed_count} documents",
        "data": {"parsed_count": parsed_count}
    }


@app.delete("/api/v1/datasets/{dataset_id}/documents")
async def delete_documents(dataset_id: str, document_ids: List[str]):
    """Delete documents from a dataset."""
    if dataset_id not in datasets:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    deleted_count = 0
    for doc_id in document_ids:
        if doc_id in documents and documents[doc_id]["dataset_id"] == dataset_id:
            del documents[doc_id]
            deleted_count += 1
            datasets[dataset_id]["document_count"] -= 1
    
    return {
        "retcode": 0,
        "retmsg": f"Deleted {deleted_count} documents",
        "data": {"deleted_count": deleted_count}
    }


@app.post("/api/v1/retrieval")
async def retrieval(request: RetrievalRequest):
    """Retrieve relevant documents."""
    # Mock retrieval - return some relevant chunks
    chunks = []
    doc_aggs = []
    
    for dataset_id in request.dataset_ids:
        for doc in documents.values():
            if doc["dataset_id"] == dataset_id and doc["run"] == "DONE":
                # Simple keyword matching
                if any(word.lower() in doc["content"].lower() for word in request.question.split()):
                    doc_aggs.append({
                        "doc_id": doc["id"],
                        "doc_name": doc["name"]
                    })
                    
                    # Split content into chunks and find relevant ones
                    content_words = doc["content"].split()
                    for i in range(0, len(content_words), 100):
                        chunk_content = " ".join(content_words[i:i+100])
                        if any(word.lower() in chunk_content.lower() for word in request.question.split()):
                            chunks.append({
                                "content": chunk_content,
                                "document_id": doc["id"],
                                "similarity": 0.8  # Mock similarity score
                            })
    
    return {
        "retcode": 0,
        "retmsg": "success",
        "data": {
            "chunks": chunks[:request.page_size],
            "doc_aggs": doc_aggs
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "datasets": len(datasets), "documents": len(documents)}


if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Mock RAGFlow Server on http://localhost:9380")
    print("This server mimics RAGFlow API for testing purposes.")
    uvicorn.run(app, host="0.0.0.0", port=9380) 