#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_direct_llm_call():
    """Test direct LLM call with exact same setup as outline_writer_node."""
    
    # Create the exact same plan and state as the failing scenario
    plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="人工智能在医疗诊断中应用的学术论文信息收集计划",
        thought="用户希望撰写一篇关于人工智能在医疗诊断中应用的学术论文，需要收集文献综述、技术分析和未来展望所需的信息。",
        steps=[
            Step(
                need_web_search=True,
                title="收集人工智能在医疗诊断应用的文献综述信息",
                description="收集关于人工智能在医疗诊断领域应用的现有学术文献、研究论文和综述。",
                step_type=StepType.RESEARCH,
                execution_res=None
            ),
            Step(
                need_web_search=True,
                title="分析人工智能在医疗诊断中的技术细节和当前发展",
                description="收集关于在医疗诊断中使用的具体AI技术的信息。",
                step_type=StepType.RESEARCH,
                execution_res=None
            ),
            Step(
                need_web_search=True,
                title="探讨人工智能在医疗诊断中的未来展望和影响",
                description="收集关于人工智能在医疗诊断领域未来发展趋势的信息。",
                step_type=StepType.RESEARCH,
                execution_res=None
            )
        ]
    )
    
    state = {
        "messages": [
            HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望"),
            AIMessage(content='{"locale": "zh-CN", "has_enough_context": false, "title": "人工智能在医疗诊断中应用的学术论文信息收集计划"}', name="planner")
        ],
        "locale": "zh-CN",
        "observations": [],
        "plan_iterations": 1,
        "current_plan": plan,
        "final_report": "",
        "auto_accepted_plan": False,
        "enable_background_investigation": True
    }
    
    try:
        # Replicate the exact same logic as outline_writer_node
        current_plan = state.get("current_plan")
        research_results = ""
        research_thinking = ""
        
        # Collect research results from completed steps
        if current_plan and current_plan.steps:
            research_results_list = []
            for step in current_plan.steps:
                if step.execution_res:
                    research_results_list.append({
                        "title": step.title,
                        "description": step.description,
                        "result": step.execution_res
                    })
            research_results = json.dumps(research_results_list, ensure_ascii=False)
            print(f"Found {len(research_results_list)} research results")
        else:
            print("No current plan or no steps found")
            research_results = json.dumps([], ensure_ascii=False)
        
        # Get user query
        user_query = ""
        if state["messages"]:
            for msg in state["messages"]:
                if hasattr(msg, 'role') and msg.role == "user":
                    user_query = msg.content
                    break
                elif hasattr(msg, 'content'):
                    user_query = msg.content
                    break
            if not user_query:
                user_query = state["messages"][0].content if state["messages"] else ""
        
        print(f"User query: {user_query}")
        
        # Apply template
        template_state = {
            **state,
            "user_query": user_query,
            "current_plan": json.dumps(current_plan.model_dump() if current_plan else {}, ensure_ascii=False),
            "research_thinking": research_thinking,
            "research_results": research_results,
            "locale": state.get("locale", "en-US")
        }
        
        print(f"Template state keys: {list(template_state.keys())}")
        
        messages = apply_prompt_template("outline_writer", template_state, Configuration())
        
        print(f"Generated {len(messages)} messages")
        print(f"System prompt length: {len(messages[0]['content'])}")
        
        # Get LLM exactly like outline_writer_node
        if AGENT_LLM_MAP.get("outline_writer", "basic") == "basic":
            llm = get_llm_by_type("basic")
        else:
            llm = get_llm_by_type(AGENT_LLM_MAP.get("outline_writer", "basic"))
        
        print(f"LLM object: {llm}")
        print(f"Number of messages: {len(messages)}")
        
        # Direct LLM call
        print("Making direct LLM call...")
        response = llm.invoke(messages)
        
        print(f"LLM response object: {response}")
        print(f"Response type: {type(response)}")
        print(f"Response content length: {len(response.content)}")
        print(f"Response content: {response.content}")
        
        if hasattr(response, 'response_metadata'):
            print(f"Response metadata: {response.response_metadata}")
        
        if not response.content.strip():
            print("ERROR: Direct LLM call returned empty response!")
            return False
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_direct_llm_call()
    sys.exit(0 if success else 1) 