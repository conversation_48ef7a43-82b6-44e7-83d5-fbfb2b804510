# Messages数据库存储功能实现指南

## 功能概述

该功能在每个节点执行时自动将state中的messages以JSON格式存储到MySQL数据库中，以提供历史记录查询功能。

## 实现的修改

### 1. 数据库模型修改 (`src/database/models.py`)

- 在`ChatHistory`模型中添加了`messages`字段
- 字段类型：`JSON`，可以存储复杂的消息结构
- 支持存储LangChain消息对象和字典格式的消息

```python
# 消息历史记录 - JSON格式存储
messages = Column(JSON, nullable=True, comment="消息历史记录JSON格式")
```

### 2. 节点更新功能 (`src/graph/nodes.py`)

新增了通用函数`update_messages_to_database(thread_id, agent, content, msg_type)`：

- **功能**：将单条消息追加到数据库的messages字段中
- **参数说明**：
  - `thread_id`: 线程ID，用于查找对应的聊天记录
  - `agent`: 代理名称（如 "planner", "researcher"等）
  - `content`: 消息内容
  - `msg_type`: 消息类型（如 "human", "ai", "system"等）
- **追加机制**：每次调用都会将新消息追加到现有messages列表的末尾
- **错误处理**：包含完善的异常处理和日志记录

### 3. 节点集成

已在以下节点中集成了messages更新功能：

- `background_investigation_node`
- `planner_node`
- `human_feedback_node`
- `coordinator_node`
- `reporter_node`
- `research_team_node`
- `researcher_node`
- `coder_node`
- `thinking_node`
- `style_control_node`
- `outline_writer_node`
- `outline_feedback_node`
- `paper_writer_node`
- `references_writer_node`

## 部署步骤

### 1. 数据库迁移

首先运行数据库迁移脚本来添加`messages`字段：

```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行迁移脚本
python scripts/add_messages_column.py
```

### 2. 测试功能

运行测试脚本验证功能正常：

```bash
# 运行测试
python test_messages_update.py
```

### 3. 重启服务

重启您的应用服务以应用更改：

```bash
# 如果使用Docker
docker-compose restart

# 或者直接重启Python应用
python server.py
```

## 使用效果

### 存储的消息格式

数据库中的messages字段将存储如下格式的JSON：

```json
[
  {
    "type": "human",
    "content": "用户输入的消息内容",
    "agent": "user"
  },
  {
    "type": "ai", 
    "content": "AI回复的消息内容",
    "agent": "planner"
  },
  {
    "type": "ai",
    "content": "研究结果内容",
    "agent": "researcher"
  },
  {
    "type": "system",
    "content": "系统消息内容",
    "agent": "system"
  }
]
```

### 查询历史记录

您可以通过现有的API端点查询包含messages的历史记录：

```python
# 通过AuthService查询
auth_service = AuthService(db)
chat_history = auth_service.get_chat_history_by_thread(user_id, thread_id)

if chat_history and chat_history.messages:
    print(f"找到 {len(chat_history.messages)} 条消息")
    for msg in chat_history.messages:
        print(f"类型: {msg.get('type')}, 内容: {msg.get('content')[:100]}...")
```

## 性能考虑

1. **存储优化**：JSON字段支持压缩存储，对性能影响较小
2. **索引**：thread_id字段已建立索引，查询性能良好  
3. **批量操作**：每个节点只进行一次数据库更新，避免频繁IO

## 监控和日志

- 每次更新都会产生详细的日志记录
- 日志包含节点名称、消息数量、thread_id等信息
- 错误情况会详细记录到日志中

## 故障排除

### 常见问题

1. **数据库连接错误**
   ```
   解决方案：检查数据库配置和连接
   ```

2. **JSON序列化错误**
   ```
   解决方案：检查messages中是否包含不可序列化的对象
   ```

3. **thread_id不存在**
   ```
   解决方案：确保ChatHistory记录已创建
   ```

### 调试方法

启用DEBUG日志查看详细信息：

```python
import logging
logging.getLogger('src.graph.nodes').setLevel(logging.DEBUG)
```

## 扩展功能

### 自定义消息过滤

您可以修改`update_messages_to_database`函数来过滤特定类型的消息：

```python
def update_messages_to_database(state: State, node_name: str, filter_types=None):
    # 添加消息类型过滤逻辑
    if filter_types:
        filtered_messages = [msg for msg in messages if msg.get('type') in filter_types]
    # ...
```

### 消息压缩

对于大量消息，可以考虑压缩存储：

```python
import json
import gzip
import base64

def compress_messages(messages):
    json_str = json.dumps(messages, ensure_ascii=False)
    compressed = gzip.compress(json_str.encode('utf-8'))
    return base64.b64encode(compressed).decode('utf-8')
```

## 安全注意事项

1. **敏感信息**：确保不要在messages中存储敏感信息（API密钥、密码等）
2. **数据量控制**：考虑对messages数量进行限制，避免存储过大的数据
3. **访问控制**：确保只有授权用户能访问自己的消息历史

## 版本兼容性

- 与现有代码完全兼容
- 新字段为可选字段，不影响现有功能
- 可以逐步迁移现有数据

这个功能提供了完整的消息历史记录存储和查询能力，为您的应用增加了强大的历史记录功能。 