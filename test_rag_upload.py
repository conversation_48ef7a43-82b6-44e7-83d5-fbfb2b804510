#!/usr/bin/env python3
"""
Test RAG Upload Functionality

This script tests the RAGFlow integration in DeerFlow by:
1. Creating a dataset
2. Uploading sample documents
3. Starting document parsing
4. Verifying the upload and retrieval functionality
"""

import requests
import json
import time
import os


# DeerFlow server configuration
DEERFLOW_API_URL = "http://localhost:8000"

# Set environment variables for mock server
os.environ["RAGFLOW_API_URL"] = "http://localhost:9380"
os.environ["RAGFLOW_API_KEY"] = "mock-api-key-for-testing"


def test_rag_config():
    """Test RAG configuration endpoint."""
    print("🔧 Testing RAG configuration...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/config"
    response = requests.get(url)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ RAG Provider: {result.get('provider')}")
        return True
    else:
        print(f"❌ Config test failed: {response.status_code} - {response.text}")
        return False


def test_list_resources():
    """Test listing RAG resources."""
    print("📋 Testing resource listing...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/resources"
    response = requests.get(url)
    
    if response.status_code == 200:
        result = response.json()
        resources = result.get("resources", [])
        print(f"✅ Found {len(resources)} existing resources")
        for resource in resources[:3]:  # Show first 3
            print(f"   - {resource.get('title')}: {resource.get('description')}")
        return True
    else:
        print(f"❌ Resource listing failed: {response.status_code} - {response.text}")
        return False


def test_create_dataset():
    """Test creating a new dataset."""
    print("🆕 Testing dataset creation...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/datasets"
    payload = {
        "name": f"DeerFlow Test Dataset {int(time.time())}",
        "description": "A test dataset created by automated testing script",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            dataset = result.get("dataset", {})
            dataset_id = dataset.get("id")
            print(f"✅ Dataset created successfully!")
            print(f"   Dataset ID: {dataset_id}")
            print(f"   Dataset Name: {dataset.get('name')}")
            return dataset_id
        else:
            print(f"❌ Dataset creation failed: {result.get('message')}")
            return None
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return None


def test_upload_documents(dataset_id):
    """Test uploading documents to the dataset."""
    print("📄 Testing document upload...")
    
    # Create sample documents with various content types
    sample_docs = [
        {
            "name": "ai_research.txt",
            "content": """
Artificial Intelligence Research Overview

Artificial Intelligence (AI) has emerged as one of the most transformative technologies of the 21st century. 
This document provides an overview of current AI research trends and applications.

## Machine Learning Fundamentals
Machine learning is a subset of AI that enables computers to learn and make decisions from data without 
being explicitly programmed. Key approaches include:

1. Supervised Learning: Learning from labeled data
2. Unsupervised Learning: Finding patterns in unlabeled data  
3. Reinforcement Learning: Learning through interaction with environment

## Natural Language Processing
Natural Language Processing (NLP) focuses on enabling computers to understand, interpret, and generate 
human language. Recent advances include:
- Large Language Models (LLMs)
- Transformer architectures
- Pre-trained models like GPT and BERT

## Computer Vision
Computer vision enables machines to interpret and understand visual information. Applications include:
- Image classification
- Object detection
- Facial recognition
- Autonomous vehicles

## Applications and Impact
AI is being applied across various domains:
- Healthcare: Medical diagnosis and drug discovery
- Finance: Fraud detection and algorithmic trading
- Transportation: Autonomous vehicles and traffic optimization
- Education: Personalized learning and intelligent tutoring systems

The rapid advancement of AI technologies continues to reshape industries and society.
            """.strip()
        },
        {
            "name": "deerflow_overview.md",
            "content": """
# DeerFlow: AI-Powered Research Assistant

DeerFlow is an advanced AI research assistant designed to help researchers, academics, and professionals 
conduct comprehensive research efficiently.

## Key Features

### 1. Intelligent Research Planning
- Automatic research plan generation
- Step-by-step investigation workflows
- Adaptive planning based on initial findings

### 2. Multi-Source Information Gathering
- Web search integration
- Academic paper retrieval
- Document analysis capabilities

### 3. RAG Integration
- Support for multiple RAG providers
- Document upload and management
- Semantic search across personal knowledge bases

### 4. Report Generation
- Automated research report creation
- Citation management
- Multiple output formats (papers, presentations, podcasts)

### 5. Persistent Storage
- Session persistence across interactions
- SQLite and PostgreSQL support
- Background task execution

## Architecture

DeerFlow is built using:
- **Backend**: FastAPI with LangGraph for workflow orchestration
- **Frontend**: React with TypeScript
- **AI Models**: Support for multiple LLM providers
- **Storage**: Configurable persistence layers
- **RAG**: Integration with RAGFlow and other providers

## Use Cases

1. **Academic Research**: Literature reviews, hypothesis formation
2. **Business Intelligence**: Market research, competitive analysis  
3. **Technical Documentation**: API documentation, technical guides
4. **Content Creation**: Blog posts, presentations, reports
            """.strip()
        },
        {
            "name": "technical_specs.txt",
            "content": """
Technical Specifications: DeerFlow System

This document outlines the technical specifications and requirements for the DeerFlow research system.

SYSTEM REQUIREMENTS
- Python 3.9+
- Node.js 18+
- FastAPI framework
- React frontend
- SQLite/PostgreSQL database

API ENDPOINTS
The system provides REST API endpoints for:
- Chat streaming: /api/chat/stream
- RAG operations: /api/rag/*
- Paper generation: /api/paper/{thread_id}
- TTS services: /api/tts
- MCP server integration: /api/mcp/*

CONFIGURATION
Environment variables:
- RAGFLOW_API_URL: RAGFlow server endpoint
- RAGFLOW_API_KEY: Authentication token
- DATABASE_URL: Database connection string
- BASIC_MODEL configurations for LLM integration

DEPLOYMENT
Supported deployment methods:
- Docker containers
- Direct Python execution
- Cloud platforms (AWS, GCP, Azure)

SECURITY CONSIDERATIONS
- API key management
- SSL/TLS encryption
- Input validation and sanitization
- Rate limiting and abuse prevention
            """.strip()
        }
    ]
    
    # Convert content to bytes and prepare files
    files = []
    for doc in sample_docs:
        content_bytes = doc["content"].encode('utf-8')
        files.append({
            "name": doc["name"],
            "content": list(content_bytes)  # Convert to list for JSON serialization
        })
    
    url = f"{DEERFLOW_API_URL}/api/rag/upload"
    payload = {
        "dataset_id": dataset_id,
        "files": files
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print("✅ Documents uploaded successfully!")
            data = result.get("data", {})
            
            # Handle different response formats
            document_ids = []
            if isinstance(data, dict) and "documents" in data:
                # New format: data contains a "documents" field
                documents = data["documents"]
            else:
                # Fallback to treating data as the documents list
                documents = data if isinstance(data, list) else []
            
            for i, doc_info in enumerate(documents):
                if isinstance(doc_info, dict):
                    doc_id = doc_info.get('id')
                    doc_name = doc_info.get('name')
                    print(f"   Document {i+1}: {doc_name} (ID: {doc_id})")
                    document_ids.append(doc_id)
                else:
                    # Handle case where doc_info is not a dict
                    print(f"   Document {i+1}: {doc_info}")
                    document_ids.append(str(doc_info))
            
            return document_ids
        else:
            print(f"❌ Failed to upload documents: {result.get('message')}")
            return []
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return []


def test_list_documents(dataset_id):
    """Test listing documents in the dataset."""
    print("📋 Testing document listing...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/datasets/{dataset_id}/documents"
    params = {"page": 1, "page_size": 10}
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        result = response.json()
        documents = result.get("documents", [])
        print(f"✅ Found {len(documents)} documents:")
        for doc in documents:
            print(f"   - {doc.get('name')} (Status: {doc.get('run', 'Unknown')}, ID: {doc.get('id')})")
        return documents
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return []


def test_parse_documents(dataset_id, document_ids):
    """Test starting document parsing."""
    print("⚙️ Testing document parsing...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/documents/parse"
    payload = {
        "dataset_id": dataset_id,
        "document_ids": document_ids
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print("✅ Document parsing started successfully!")
            print("   Note: Parsing may take a few moments to complete.")
            return True
        else:
            print(f"❌ Failed to start parsing: {result.get('message')}")
            return False
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return False


def wait_for_parsing(dataset_id, max_wait_time=60):
    """Wait for document parsing to complete."""
    print("⏳ Waiting for parsing to complete...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        documents = test_list_documents(dataset_id)
        
        if documents:
            # Check if all documents are processed
            all_done = all(
                doc.get("run") in ["DONE", "FAIL", "CANCEL"] 
                for doc in documents
            )
            
            if all_done:
                success_count = sum(1 for doc in documents if doc.get("run") == "DONE")
                print(f"✅ Parsing completed! {success_count}/{len(documents)} documents processed successfully.")
                return True
        
        print("   Still processing...")
        time.sleep(5)
    
    print(f"⏰ Timeout after {max_wait_time} seconds")
    return False


def main():
    """Main test function."""
    print("🚀 DeerFlow RAG Upload Test (Using Mock Server)")
    print("=" * 60)
    
    # Step 1: Test RAG configuration
    if not test_rag_config():
        print("❌ Configuration test failed. Exiting.")
        return
    
    print()
    
    # Step 2: Test resource listing
    if not test_list_resources():
        print("❌ Resource listing failed. Exiting.")
        return
    
    print()
    
    # Step 3: Create dataset
    dataset_id = test_create_dataset()
    if not dataset_id:
        print("❌ Dataset creation failed. Exiting.")
        return
    
    print()
    
    # Step 4: Upload documents
    document_ids = test_upload_documents(dataset_id)
    if not document_ids:
        print("❌ Document upload failed. Exiting.")
        return
    
    print()
    
    # Step 5: List documents
    test_list_documents(dataset_id)
    print()
    
    # Step 6: Start parsing
    if test_parse_documents(dataset_id, document_ids):
        print()
        # Step 7: Wait for parsing (optional)
        wait_for_parsing(dataset_id, max_wait_time=30)  # Reduced time for mock server
    
    print()
    print("🎉 RAG upload test completed!")
    print(f"📋 Dataset ID for future reference: {dataset_id}")
    print()
    print("Next steps:")
    print("1. Check document parsing status")
    print("2. Use the dataset in research workflows")
    print("3. Test document retrieval via chat interface")


if __name__ == "__main__":
    main() 