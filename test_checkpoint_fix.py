#!/usr/bin/env python3
"""
测试checkpoint修复的脚本
"""

import asyncio
import logging
import sys
import os
from uuid import uuid4

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.server.background_tasks import BackgroundTaskManager
from src.rag.retriever import Resource

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_checkpoint_recovery():
    """测试checkpoint恢复功能"""
    
    # 创建后台任务管理器
    background_manager = BackgroundTaskManager()
    
    # 测试参数
    task_id = str(uuid4())
    thread_id = str(uuid4())
    user_id = 1
    resources = []
    
    logger.info(f"测试task_id: {task_id}")
    logger.info(f"测试thread_id: {thread_id}")
    
    # 模拟一个有效的checkpoint配置
    checkpoint_config = {
        "configurable": {
            "thread_id": thread_id,
            "checkpoint_ns": "",
            "checkpoint_id": "test-checkpoint-id"
        }
    }
    
    logger.info("测试从checkpoint恢复执行...")
    
    try:
        # 测试从checkpoint恢复执行
        success = await background_manager.start_background_execution_from_checkpoint(
            task_id=task_id,
            thread_id=thread_id,
            user_id=user_id,
            resources=resources,
            max_plan_iterations=3,
            max_step_num=20,
            max_search_results=10,
            auto_accepted_plan=True,
            interrupt_feedback="",
            mcp_settings={},
            enable_background_investigation=True,
            checkpoint_config=checkpoint_config,
        )
        
        logger.info(f"启动结果: {success}")
        
        # 等待一段时间让任务执行
        await asyncio.sleep(5)
        
        # 检查任务状态
        status = background_manager.get_task_status(task_id)
        logger.info(f"任务状态: {status}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_normal_execution():
    """测试正常执行功能"""
    
    background_manager = BackgroundTaskManager()
    
    task_id = str(uuid4())
    thread_id = str(uuid4())
    user_id = 1
    
    logger.info(f"测试正常执行，task_id: {task_id}")
    
    # 模拟消息
    messages = [
        {"role": "user", "content": "简单测试"}
    ]
    
    try:
        success = await background_manager.start_background_execution(
            task_id=task_id,
            messages=messages,
            thread_id=thread_id,
            user_id=user_id,
            resources=[],
            max_plan_iterations=3,
            max_step_num=20,
            max_search_results=10,
            auto_accepted_plan=True,
            interrupt_feedback="",
            mcp_settings={},
            enable_background_investigation=True,
        )
        
        logger.info(f"正常执行启动结果: {success}")
        
        # 等待一段时间
        await asyncio.sleep(3)
        
        # 检查状态
        status = background_manager.get_task_status(task_id)
        logger.info(f"正常执行任务状态: {status}")
        
    except Exception as e:
        logger.error(f"正常执行测试失败: {e}")

if __name__ == "__main__":
    print("测试checkpoint修复...")
    
    # 测试checkpoint恢复
    asyncio.run(test_checkpoint_recovery())
    
    print("\n" + "="*50 + "\n")
    
    # 测试正常执行
    asyncio.run(test_normal_execution())
    
    print("测试完成!")
