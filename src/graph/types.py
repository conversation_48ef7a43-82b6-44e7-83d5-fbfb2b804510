# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langgraph.graph import MessagesState

from src.prompts.planner_model import Plan
from src.rag import Resource


class State(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    locale: str = "en-US"
    observations: list[str] = []
    resources: list[Resource] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    raw_plan: dict | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
    style_control_result: str = None
    user_id: int = None
    thread_id: str = None

    # Paper Writing Variables
    paper_writing_mode: bool = False
    paper_outline: str = None
    approved_outline: str = None
    current_section_index: int = 0
    paper_sections: list[str] = []
    final_paper: str = ""
    auto_accepted_outline: bool = False
    current_citation_number: int = 1
    paper_citations: list[dict] = []
    section_references: list[str] = []