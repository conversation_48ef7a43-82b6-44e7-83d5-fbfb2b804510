# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
import uuid
from typing import Annotated, Literal


from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.types import Command, interrupt

from src.config.agents import AGENT_LLM_MAP
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.prompts.planner_model import Plan, Step, StepType, NewPlanResponse
from src.prompts.template import apply_prompt_template
from src.tools import (
    crawl_tool,
    get_web_search_tool,
    get_retriever_tool,
    python_repl_tool,
)
from src.tools.search import LoggedTavilySearch
from src.utils.json_utils import repair_json_output
from .types import State
from ..database.models import ChatHistory
from ..auth.service import AuthService
from ..config import SELECTED_SEARCH_ENGINE, SearchEngine

from ..utils import file_utils

logger = logging.getLogger(__name__)


def update_messages_to_database(thread_id: str, agent: str, content: str, msg_type: str):
    """
    将单条消息追加到数据库的messages字段中
    
    Args:
        thread_id: 线程ID，用于查找对应的聊天记录
        agent: 代理名称（如 "planner", "researcher"等）
        content: 消息内容
        msg_type: 消息类型（如 "human", "ai", "system"等）
    """
    try:
        from src.database.database import SessionLocal
        
        if not thread_id:
            logger.warning(f"Agent {agent}: No thread_id provided, skipping message update")
            return
            
        if not content or not content.strip():
            logger.warning(f"Agent {agent}: Empty content provided, skipping message update")
            return
        
        # 创建新消息对象
        new_message = {
            "type": msg_type,
            "content": content,
            "agent": agent
        }
        
        # 更新数据库
        db = SessionLocal()
        try:
            chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
            if chat_history:
                # 获取现有messages，如果为空则初始化为空列表
                existing_messages = chat_history.messages if chat_history.messages else []
                
                # 追加新消息到列表末尾
                existing_messages.append(new_message)
                
                # 重要：创建新的列表对象，确保SQLAlchemy检测到变化
                chat_history.messages = list(existing_messages)
                
                # 手动标记字段已修改（确保SQLAlchemy知道JSON字段已变化）
                from sqlalchemy.orm.attributes import flag_modified
                flag_modified(chat_history, 'messages')
                
                db.flush()  # 先flush确保SQLAlchemy知道有变化
                
                db.commit()
                db.refresh(chat_history)

                logger.info(f"Agent {agent}: Appended message (type: {msg_type}) to database for thread {thread_id}. Total messages: {len(existing_messages)}")
                
                # 验证数据库中的实际数据
                verification_chat = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
                if verification_chat and verification_chat.messages:
                    logger.debug(f"Agent {agent}: Database verification - actual messages count: {len(verification_chat.messages)}")
                else:
                    logger.warning(f"Agent {agent}: Database verification failed - no messages found")
                    
            else:
                logger.warning(f"Agent {agent}: No chat history found for thread_id {thread_id}")
                # 创建新的chat_history
                new_chat_history = ChatHistory(
                    thread_id=thread_id,
                    user_id=1,
                    messages=[new_message]
                )
                db.add(new_chat_history)
                db.commit()
                db.refresh(new_chat_history)
                logger.info(f"Agent {agent}: Created new chat history with first message for thread {thread_id}")
        except Exception as e:
            logger.error(f"Agent {agent}: Database update failed: {e}")
            db.rollback()
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Agent {agent}: Failed to update message to database: {e}")


@tool
def handoff_to_planner(
    task_title: Annotated[str, "The title of the task to be handed off."],
    locale: Annotated[str, "The user's detected language locale (e.g., en-US, zh-CN)."],
):
    """Handoff to planner agent to do plan."""
    # This tool is not returning anything: we're just using it
    # as a way for LLM to signal that it needs to hand off to planner agent
    return


def background_investigation_node(
    state: State, config: RunnableConfig
) -> Command[Literal["planner"]]:
    logger.info("background investigation node is running.")
    configurable = Configuration.from_runnable_config(config)
    query = state["messages"][-1].content
    if SELECTED_SEARCH_ENGINE == SearchEngine.TAVILY.value:
        searched_content = LoggedTavilySearch(
            max_results=configurable.max_search_results
        ).invoke(query)
        background_investigation_results = None
        if isinstance(searched_content, list):
            background_investigation_results = [
                {"title": elem["title"], "content": elem["content"]}
                for elem in searched_content
            ]
        else:
            logger.error(
                f"Tavily search returned malformed response: {searched_content}"
            )
    else:
        background_investigation_results = get_web_search_tool(
            configurable.max_search_results
        ).invoke(query)
    # 记录背景调研结果到数据库
    thread_id = state.get("thread_id")
    if thread_id and background_investigation_results:
        update_messages_to_database(
            thread_id=thread_id,
            agent="background_investigator", 
            content=json.dumps(background_investigation_results, ensure_ascii=False),
            msg_type="assistant"
        )
    
    return Command(
        update={
            "background_investigation_results": json.dumps(
                background_investigation_results, ensure_ascii=False
            )
        },
        goto="planner",
    )


def planner_node(
    state: State, config: RunnableConfig
) -> Command[Literal["human_feedback", "reporter"]]:
    """Planner node that generate the full plan."""
    logger.info("Planner generating full plan")
    configurable = Configuration.from_runnable_config(config)
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    messages = apply_prompt_template("planner", state, configurable)

    if (
        plan_iterations == 0
        and state.get("enable_background_investigation")
        and state.get("background_investigation_results")
    ):
        messages += [
            {
                "role": "user",
                "content": (
                    "background investigation results of user query:\n"
                    + state["background_investigation_results"]
                    + "\n"
                ),
            }
        ]

    if AGENT_LLM_MAP["planner"] == "basic":
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"]).with_structured_output(
            NewPlanResponse,
            method="json_mode",
        )
    else:
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"])

    # if the plan iterations is greater than the max plan iterations, return the reporter node
    if plan_iterations >= configurable.max_plan_iterations:
        return Command(goto="reporter")

    full_response = ""
    structured_response = None
    if AGENT_LLM_MAP["planner"] == "basic":
        structured_response = llm.invoke(messages)
        full_response = structured_response.model_dump_json(indent=4, exclude_none=True)
    else:
        response = llm.stream(messages)
        for chunk in response:
            full_response += chunk.content
    logger.info(f"Current state messages: {state['messages']}")
    logger.info(f"Planner response: {full_response}")

    def parse_new_plan_format(plan_data: dict) -> Plan:
        """Parse the new JSON format into Plan structure."""
        task_summary = plan_data.get("task_summary", {})
        execution_plan = plan_data.get("execution_plan", {})
        
        # Extract basic information
        locale = state.get("locale", "en-US")
        title = task_summary.get("title", "")
        task_type = task_summary.get("type", "")
        writing_style = task_summary.get("style", "")
        target_audience = task_summary.get("target_audience", "")
        word_count = task_summary.get("word_count", "")
        need_outline = task_summary.get("if_outline", False)
        
        # Convert execution phases to steps
        steps = []
        
        # Add thinking phase steps
        thinking_phase = execution_plan.get("thinking_phase", [])
        for task in thinking_phase:
            steps.append(Step(
                need_search=False,
                title=task.get("description", ""),
                description=task.get("description", ""),
                step_type=StepType.THINKING
            ))
        
        # Add research phase steps
        research_phase = execution_plan.get("research_phase", [])
        for task in research_phase:
            steps.append(Step(
                need_search=True,
                title=task.get("description", ""),
                description=task.get("description", ""),
                step_type=StepType.RESEARCH
            ))
        
        # Add writing phase steps
        writing_phase = execution_plan.get("writing_phase", [])
        for task in writing_phase:
            steps.append(Step(
                need_search=False,
                title=task.get("description", ""),
                description=task.get("description", ""),
                step_type=StepType.PROCESSING
            ))
        
        # Determine if we have enough context (if no research steps, we have enough)
        # has_enough_context = len(research_phase) == 0
        has_enough_context = len(steps) == 0
        
        return Plan(
            locale=locale,
            has_enough_context=has_enough_context,
            thought=f"Generated plan for {task_type} task",
            title=title,
            task_type=task_type,
            writing_style=writing_style,
            target_audience=target_audience,
            word_count=word_count,
            need_outline=need_outline,
            steps=steps
        )

    try:
        # Handle structured response from basic LLM
        if structured_response is not None:
            # Convert structured response to Plan format
            new_plan = parse_new_plan_format(structured_response.model_dump())
            curr_plan = new_plan.model_dump()
        else:
            # Handle streaming response - parse JSON
            curr_plan = json.loads(repair_json_output(full_response))
            # Check if this is the new format (has task_summary and execution_plan)
            if "task_summary" in curr_plan and "execution_plan" in curr_plan:
                # Parse new format
                new_plan = parse_new_plan_format(curr_plan)
                # Convert back to dict for has_enough_context check
                curr_plan = new_plan.model_dump()
            else:
                # Old format - validate directly
                new_plan = Plan.model_validate(curr_plan)
            
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")
    except Exception as e:
        logger.error(f"Error parsing plan: {e}")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")
            
    if curr_plan.get("has_enough_context"):
        logger.info("Planner response has enough context.")
        
        # 记录planner输出到数据库
        thread_id = state.get("thread_id")
        if thread_id:
            update_messages_to_database(
                thread_id=thread_id,
                agent="planner",
                content=full_response,
                msg_type="assistant"
            )
        
        # Preserve paper_writing_mode state if it exists
        update_dict = {
            "messages": [AIMessage(content=full_response, name="planner")],
            "current_plan": new_plan.model_dump(),
            "raw_plan": full_response,
        }
        if state.get("paper_writing_mode"):
            update_dict["paper_writing_mode"] = True
            logger.info("Preserving paper_writing_mode=True in planner node")
        
        return Command(
            update=update_dict,
            goto="reporter",
        )
    # 记录planner输出到数据库
    thread_id = state.get("thread_id")
    if thread_id:
        update_messages_to_database(
            thread_id=thread_id,
            agent="planner",
            content=full_response,
            msg_type="assistant"
        )
    
    # Preserve paper_writing_mode state if it exists
    update_dict = {
        "messages": [AIMessage(content=full_response, name="planner")],
        "current_plan": new_plan.model_dump(),
        "raw_plan": full_response,
    }
    if state.get("paper_writing_mode"):
        update_dict["paper_writing_mode"] = True
        logger.info("Preserving paper_writing_mode=True in planner node (human_feedback path)")
    
    return Command(
        update=update_dict,
        goto="human_feedback",
    )


def human_feedback_node(
        state,
) -> Command[Literal["planner", "research_team", "reporter", "outline_writer", "__end__"]]:
    current_plan = state.get("current_plan", "")
    # check if the plan is auto accepted
    auto_accepted_plan = state.get("auto_accepted_plan", False)
    if not auto_accepted_plan:
        feedback = interrupt("Please Review the Plan. Options: [ACCEPTED] to proceed, [EDIT_PLAN] to modify plan.")

        # if the feedback is not accepted, return the planner node
        if feedback and str(feedback).upper().startswith("[EDIT_PLAN]"):
            # 记录human_feedback输出到数据库
            thread_id = state.get("thread_id")
            if thread_id:
                # feedback去除前缀[EDIT_PLAN]
                feedback_content = feedback.replace("[edit_plan]", "").strip()
                update_messages_to_database(
                    thread_id=thread_id,
                    agent="ThinkWriter",
                    content=feedback_content,
                    msg_type="user"
                )
            return Command(
                update={
                    "messages": [
                        HumanMessage(content=feedback, name="feedback"),
                    ],
                },
                goto="planner",
            )
        elif feedback and str(feedback).upper().startswith("[ACCEPTED]"):
            # 记录human_feedback输出到数据库
            thread_id = state.get("thread_id")
            if thread_id:
                # feedback去除前缀[ACCEPTED]
                feedback_content = feedback.replace("[accepted]", "").strip()
                update_messages_to_database(
                    thread_id=thread_id,
                    agent="ThinkWriter",
                    content=feedback_content,
                    msg_type="user"
                )
            logger.info("Plan is accepted by user.")
        else:
            raise TypeError(f"Interrupt value of {feedback} is not supported.")

    # if the plan is accepted, run the following node
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    goto = "research_team"
    try:
        # current_plan = repair_json_output(current_plan)
        # increment the plan iterations
        plan_iterations += 1
        # parse the plan
        # new_plan = json.loads(current_plan)
        new_plan = current_plan
        # 如果has_enough_context为True，或者steps只有一个且step_type为PROCESSING，则直接跳转到reporter(非大纲的写作任务)
        if new_plan["has_enough_context"] or (new_plan["steps"] and len(new_plan["steps"]) == 1 and new_plan["steps"][0]["step_type"] == StepType.PROCESSING):
        # if new_plan.get("has_enough_context"):
            goto = "reporter"
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")

    # Check if need_outline is true to determine if this should be paper writing workflow
    plan_obj = Plan.model_validate(new_plan)
    paper_writing_mode = plan_obj.need_outline

    if paper_writing_mode:
        logger.info("Plan requires outline - enabling paper writing workflow.")
    else:
        logger.info("Plan does not require outline - using standard research workflow.")

    update_dict = {
        "current_plan": plan_obj,
        "plan_iterations": plan_iterations,
        "locale": new_plan.get("locale", state.get("locale", "en-US")),
        "paper_writing_mode": paper_writing_mode,
    }

    return Command(
        update=update_dict,
        goto=goto,
    )


def coordinator_node(
    state: State, config: RunnableConfig
) -> Command[Literal["planner", "background_investigator", "__end__"]]:
    """Coordinator node that communicate with customers."""
    logger.info("Coordinator talking.")
    configurable = Configuration.from_runnable_config(config)
    messages = apply_prompt_template("coordinator", state)
    response = (
        get_llm_by_type(AGENT_LLM_MAP["coordinator"])
        .bind_tools([handoff_to_planner])
        .invoke(messages)
    )
    logger.info(f"Current state messages: {state['messages']}")

    # coordinator_node需要单独记录user信息
    thread_id = state.get("thread_id")
    # TODO 仅限测试环境
    if not thread_id:
        thread_id = str(uuid.uuid4())
        state["thread_id"] = thread_id
    if thread_id and len(state["messages"]) > 0:
        update_messages_to_database(
            thread_id=thread_id,
            agent="ThinkWriter",
            content=state["messages"][-1].content,
            msg_type="user"
        )

    goto = "__end__"
    locale = state.get("locale", "en-US")  # Default locale if not specified

    if len(response.tool_calls) > 0:
        goto = "planner"
        if state.get("enable_background_investigation"):
            # if the search_before_planning is True, add the web search tool to the planner agent
            goto = "background_investigator"
        try:
            for tool_call in response.tool_calls:
                if tool_call.get("name", "") != "handoff_to_planner":
                    continue
                if tool_locale := tool_call.get("args", {}).get("locale"):
                    locale = tool_locale
                    break
        except Exception as e:
            logger.error(f"Error processing tool calls: {e}")
    else:
        logger.warning(
            "Coordinator response contains no tool calls. Terminating workflow execution."
        )
        logger.debug(f"Coordinator response: {response}")

    # 记录coordinator输出到数据库，如果进行工具调用则不存储这段话
    # thread_id = state.get("thread_id")
    if thread_id and response.content and len(response.tool_calls) == 0:
        update_messages_to_database(
            thread_id=thread_id,
            agent="coordinator",
            content=response.content,
            msg_type="assistant"
        )
    
    return Command(
        update={"locale": locale, "thread_id": thread_id, "resources": configurable.resources, "messages": [AIMessage(content=response.content)]},
        goto=goto,
    )


def reporter_node(state: State):
    """Reporter node that write a final report or final paper."""
    logger.info("📰 Reporter node started - checking for final paper or generating research report")
    
    # Check if we have a final paper from paper writing workflow
    final_paper = state.get("final_paper", "")
    if final_paper:
        logger.info(f"📄 Final paper found in state, length: {len(final_paper)} characters")
        logger.info(f"📄 Final paper preview: {final_paper[:200]}...")
        logger.info("📄 Using RunnablePassthrough for final paper (no LLM processing)")

        # 创建结果文档id
        result_document_id = str(uuid.uuid4())
        file_name = result_document_id + ".md"

        user_id = state.get("user_id", 0)
        thread_id = state.get("thread_id", "")

        # 上传文件到cloudflare
        file_url = file_utils.upload_file_to_storage(final_paper, file_name, user_id)
        
        return save_final_paper(final_paper, result_document_id, file_url, thread_id)
    
    # processing阶段
    current_plan = state.get("current_plan")
    current_plan_step = ""
    if current_plan and current_plan.steps:
        for step in current_plan.steps:
            if step.step_type == StepType.PROCESSING:
                current_plan_step = step.description
                break
    # style_control阶段
    style_control_result = state.get("style_control_result", "")
    if style_control_result:
        logger.info(f"📄 Style control found in state, length: {len(style_control_result)} characters")
        logger.info(f"📄 Style control preview: {style_control_result[:200]}...")
        logger.info("📄 Using RunnablePassthrough for final paper (no LLM processing)")

    # Otherwise, generate a standard research report using LLM
    logger.info("📰 No final paper found, generating standard research report using LLM")
    current_plan = state.get("current_plan")
    input_ = {
        # "messages": [
        #     HumanMessage(
        #         f"# Research Requirements\n\n## Task\n\n{current_plan.title}\n\n## Description\n\n{current_plan.thought}"
        #     )
        # ],
        "messages": [],
        "locale": state.get("locale", "en-US"),
        "style_control_result": style_control_result,
        "current_plan_step": current_plan_step
    }
    invoke_messages = apply_prompt_template("reporter", input_)
    observations = state.get("observations", [])

    # # Add a reminder about the new report format, citation style, and table usage
    # invoke_messages.append(
    #     HumanMessage(
    #         content="IMPORTANT: Structure your report according to the format in the prompt. Remember to include:\n\n1. Key Points - A bulleted list of the most important findings\n2. Overview - A brief introduction to the topic\n3. Detailed Analysis - Organized into logical sections\n4. Survey Note (optional) - For more comprehensive reports\n5. Key Citations - List all references at the end\n\nFor citations, DO NOT include inline citations in the text. Instead, place all citations in the 'Key Citations' section at the end using the format: `- [Source Title](URL)`. Include an empty line between each citation for better readability.\n\nPRIORITIZE USING MARKDOWN TABLES for data presentation and comparison. Use tables whenever presenting comparative data, statistics, features, or options. Structure tables with clear headers and aligned columns. Example table format:\n\n| Feature | Description | Pros | Cons |\n|---------|-------------|------|------|\n| Feature 1 | Description 1 | Pros 1 | Cons 1 |\n| Feature 2 | Description 2 | Pros 2 | Cons 2 |",
    #         name="system",
    #     )
    # )

    # for observation in observations:
    #     invoke_messages.append(
    #         HumanMessage(
    #             content=f"Below are some observations for the research task:\n\n{observation}",
    #             name="observation",
    #         )
    #     )

    logger.debug(f"Current invoke messages: {invoke_messages}")
    response = get_llm_by_type(AGENT_LLM_MAP["reporter"]).invoke(invoke_messages)
    response_content = response.content
    logger.info(f"reporter response: {response_content}")

    # 记录reporter输出到数据库
    thread_id = state.get("thread_id", "")
    if thread_id:
        update_messages_to_database(
            thread_id=thread_id,
            agent="reporter",
            content=response_content,
            msg_type="assistant"
        )

    # 创建结果文档id, 普通报告(非论文)
    result_document_id = str(uuid.uuid4())
    file_name = result_document_id + ".md"

    user_id = state.get("user_id", 0)

    # 上传文件到cloudflare
    file_url = file_utils.upload_file_to_storage(response_content, file_name, user_id, thread_id)
    
    return save_final_paper(response_content, result_document_id, file_url, thread_id)


def save_final_paper(final_paper: str, result_document_id: str, file_url: str, thread_id: str):
    # 正确创建数据库会话
    from src.database.database import SessionLocal
    from src.database.models import ResultDocument
    
    db = SessionLocal()
    try:
        # 创建结果文档
        result_document = ResultDocument(
            document_id=result_document_id,
            thread_id=thread_id,
            # version=1,
            url=file_url
        )
        db.add(result_document)

        # 更新对话历史记录
        chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
        if chat_history:
            chat_history.result_document_id = result_document_id
            db.commit()
            db.refresh(chat_history)

        db.commit()
        db.refresh(result_document)
    except Exception as e:
        logger.error(f"创建结果文档失败: {e}")
        db.rollback()
    finally:
        db.close()
    
    return {
        "final_report": final_paper,
        "messages": [AIMessage(content=final_paper, name="reporter")]
    }


def research_team_node(
    state: State,
) -> Command[Literal["planner", "researcher", "coder", "thinking", "outline_writer", "style_control"]]:
    """Research team node that collaborates on tasks."""
    logger.info("Research team is collaborating on tasks.")
    current_plan = state.get("current_plan")
    paper_writing_mode = state.get("paper_writing_mode", False)
    
    # Enhanced debugging for paper_writing_mode
    logger.info(f"Research team node - paper_writing_mode: {paper_writing_mode}")
    logger.info(f"Research team node - current_plan exists: {current_plan is not None}")
    logger.info(f"Research team node - state keys: {list(state.keys())}")
    
    if not current_plan or not current_plan.steps:
        logger.debug("No current plan or no steps, going to planner")
        return Command(goto="planner")
    
    # Check if all research steps are completed
    completed_steps = [step for step in current_plan.steps if step.execution_res]
    total_steps = len(current_plan.steps)
    logger.info(f"Research progress: {len(completed_steps)}/{total_steps} steps completed")
    
    if all(step.execution_res for step in current_plan.steps):
        if paper_writing_mode:
            logger.info("All research completed in paper writing mode, going to outline writer")
            return Command(goto="outline_writer")
        else:
            logger.info("All research completed in normal mode, going to planner")
            return Command(goto="planner")
    
    # Find the next unexecuted step
    for step in current_plan.steps:
        if not step.execution_res:
            break
    
    if step.step_type and step.step_type == StepType.RESEARCH:
        return Command(goto="researcher")
    if step.step_type and step.step_type == StepType.PROCESSING:
        return Command(goto="style_control")
    if step.step_type and step.step_type == StepType.THINKING:
        return Command(goto="thinking")
    
    # If no valid step type found, go back to planner
    return Command(goto="planner")


async def _execute_agent_step(
    state: State, agent, agent_name: str
) -> Command[Literal["research_team"]]:
    """Helper function to execute a step using the specified agent."""
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])

    # Find the first unexecuted step
    current_step = None
    completed_steps = []
    thinking_result = ""
    for step in current_plan.steps:
        if not step.execution_res:
            current_step = step
            break
        else:
            if step.step_type == StepType.THINKING:
                thinking_result = step.execution_res
            completed_steps.append(step)

    if not current_step:
        logger.warning("No unexecuted step found")
        return Command(goto="research_team")

    logger.info(f"Executing step: {current_step.title}, agent: {agent_name}")

    # Format completed steps information
    completed_steps_info = ""
    if completed_steps:
        completed_steps_info = "# Existing Research Findings\n\n"
        for i, step in enumerate(completed_steps):
            if step.step_type == StepType.THINKING:
                completed_steps_info += f"## ThinkWriter思考内容如下: {i + 1}: {step.title}\n\n"
                completed_steps_info += f"<thinking>\n{step.execution_res}\n</thinking>\n\n"
            elif step.step_type == StepType.RESEARCH:
                completed_steps_info += f"## Existing Research {i + 1}: {step.title}\n\n"
                completed_steps_info += f"<research>\n{step.execution_res}\n</research>\n\n"
            elif step.step_type == StepType.PROCESSING:
                completed_steps_info += f"## Existing Processing {i + 1}: {step.title}\n\n"
                completed_steps_info += f"<processing>\n{step.execution_res}\n</processing>\n\n"

    # Prepare the input for the agent with completed steps info
    # Note: We no longer need to set locale here since it's handled in create_agent_with_locale
    # agent_input = {
    #     **state,  # Include all original state
    #     "messages": [
    #         HumanMessage(
    #             content=f"{completed_steps_info}# Current Task\n\n## Title\n\n{current_step.title}\n\n## Description\n\n{current_step.description}\n\n## Locale\n\n{state.get('locale', 'en-US')}"
    #         )
    #     ]
    # }
    if thinking_result:
        agent_input = {
            **state,
            "messages": [
                HumanMessage(
                    content = f"ThinkWriter思考内容如下:\n\n{thinking_result}",
                )
            ],
        }
    else:
        agent_input = {
            **state,
            "messages": []
        }

    # Ensure locale is explicitly available for researcher agent too
    if agent_name == "researcher":
        logger.debug(f"Locale from state for researcher: {state.get('locale', 'en-US')}")
        if state.get("resources"):
            resources_info = "**The user mentioned the following resource files:**\n\n"
            for resource in state.get("resources"):
                resources_info += f"- {resource.title} ({resource.description})\n"

            agent_input["messages"].append(
                HumanMessage(
                    content=resources_info
                    + "\n\n"
                    + "You MUST use the **local_search_tool** to retrieve the information from the resource files.",
                )
            )

        # agent_input["messages"].append(
        #     HumanMessage(
        #         content="IMPORTANT: DO NOT include inline citations in the text. Instead, track all sources and include a References section at the end using link reference format. Include an empty line between each citation for better readability. Use this format for each reference:\n- [Source Title](URL) - Published: YYYY-MM-DD (if available)\n\n- [Another Source](URL) - Published: YYYY-MM-DD (if available)\n\nWhen collecting information from sources, try to include publication dates, timestamps, or any time-related information when available. This helps establish the recency and relevance of the information.",
        #         name="system",
        #     )
        # )
        agent_input["messages"].append(
            HumanMessage(
                content="再进行研究前你必须先进行搜索，调用之前提到的工具。\n\nWhen collecting information from sources, try to include publication dates, timestamps, or any time-related information when available. This helps establish the recency and relevance of the information.",
            )
        )

    # Invoke the agent
    default_recursion_limit = 25
    try:
        env_value_str = os.getenv("AGENT_RECURSION_LIMIT", str(default_recursion_limit))
        parsed_limit = int(env_value_str)

        if parsed_limit > 0:
            recursion_limit = parsed_limit
            logger.info(f"Recursion limit set to: {recursion_limit}")
        else:
            logger.warning(
                f"AGENT_RECURSION_LIMIT value '{env_value_str}' (parsed as {parsed_limit}) is not positive. "
                f"Using default value {default_recursion_limit}."
            )
            recursion_limit = default_recursion_limit
    except ValueError:
        raw_env_value = os.getenv("AGENT_RECURSION_LIMIT")
        logger.warning(
            f"Invalid AGENT_RECURSION_LIMIT value: '{raw_env_value}'. "
            f"Using default value {default_recursion_limit}."
        )
        recursion_limit = default_recursion_limit

    logger.info(f"Agent input: {agent_input}")
    result = await agent.ainvoke(
        input=agent_input, config={"recursion_limit": recursion_limit}
    )

    # Process the result
    response_content = result["messages"][-1].content
    logger.debug(f"{agent_name.capitalize()} full response: {response_content}")

    # Update the step with the execution result
    current_step.execution_res = response_content
    logger.info(f"Step '{current_step.title}' execution completed by {agent_name}")

    # 记录agent输出到数据库
    thread_id = state.get("thread_id")
    if thread_id:
        update_messages_to_database(
            thread_id=thread_id,
            agent=agent_name,
            content=response_content,
            msg_type="assistant"
        )

    # Preserve paper_writing_mode state during research execution
    update_dict = {
        "messages": [
            HumanMessage(
                content=response_content,
                name=agent_name,
            )
        ],
        "observations": observations + [response_content],
        "current_plan": current_plan,  # Save the updated plan back to state
    }
    
    # Preserve paper_writing_mode if it exists
    paper_writing_mode = state.get("paper_writing_mode", False)
    if paper_writing_mode:
        update_dict["paper_writing_mode"] = True
        logger.info(f"Preserving paper_writing_mode=True in agent step update")
    else:
        logger.info(f"No paper_writing_mode to preserve (current value: {paper_writing_mode})")
    
    return Command(
        update=update_dict,
        goto="research_team",
    )


async def _setup_and_execute_agent_step(
    state: State,
    config: RunnableConfig,
    agent_type: str,
    default_tools: list,
) -> Command[Literal["research_team"]]:
    """Helper function to set up an agent with appropriate tools and execute a step.

    This function handles the common logic for both researcher_node and coder_node:
    1. Configures MCP servers and tools based on agent type
    2. Creates an agent with the appropriate tools or uses the default agent
    3. Executes the agent on the current step

    Args:
        state: The current state
        config: The runnable config
        agent_type: The type of agent ("researcher" or "coder")
        default_tools: The default tools to add to the agent

    Returns:
        Command to update state and go to research_team
    """
    configurable = Configuration.from_runnable_config(config)
    mcp_servers = {}
    enabled_tools = {}
    
    # Get locale from state
    locale = state.get("locale", "en-US")
    logger.debug(f"Using locale for {agent_type} agent: {locale}")

    # Prepare extra context for thinking agent template variables
    extra_context = {}
    if agent_type == "thinking":
        current_plan = state.get("current_plan")
        
        # Get user_query from plan title
        plan_title = current_plan.title if current_plan and hasattr(current_plan, 'title') else "Deep analysis required"
        # 获取state.messages中的用户交流历史记录, msg.name为空就是交流记录
        user_query = []
        if state["messages"]:
            for msg in state["messages"]:
                if not msg.name and msg.content != "":
                    user_query.append(f"{msg.type}: {msg.content}")
        extra_context["user_query"] = user_query
        
        # Get research_results from completed steps
        research_results_list = []
        if current_plan and current_plan.steps:
            for step in current_plan.steps:
                if step.execution_res:
                    research_results_list.append({
                        "title": step.title,
                        "description": step.description,
                        "result": step.execution_res
                    })
        
        # Format research results as JSON for thinking template
        research_results_json = json.dumps(research_results_list, ensure_ascii=False) if research_results_list else "No external research provided - rely on internal knowledge"
        extra_context["research_results"] = research_results_json
        
        logger.debug(f"Added user_query for thinking agent: {plan_title}")
        logger.debug(f"Added research_results for thinking agent: {len(research_results_json)} characters")
        logger.debug(f"Research results count: {len(research_results_list)} completed steps")

    # Extract MCP server configuration for this agent type
    if configurable.mcp_settings:
        for server_name, server_config in configurable.mcp_settings["servers"].items():
            if (
                server_config["enabled_tools"]
                and agent_type in server_config["add_to_agents"]
            ):
                mcp_servers[server_name] = {
                    k: v
                    for k, v in server_config.items()
                    if k in ("transport", "command", "args", "url", "env")
                }
                for tool_name in server_config["enabled_tools"]:
                    enabled_tools[tool_name] = server_name

    # Create and execute agent with MCP tools if available
    if mcp_servers:
        async with MultiServerMCPClient(mcp_servers) as client:
            loaded_tools = default_tools[:]
            for tool in client.get_tools():
                if tool.name in enabled_tools:
                    tool.description = (
                        f"Powered by '{enabled_tools[tool.name]}'.\n{tool.description}"
                    )
                    loaded_tools.append(tool)
            
            # Import the new function
            from src.agents.agents import create_agent_with_locale
            agent = create_agent_with_locale(state, agent_type, agent_type, loaded_tools, agent_type, locale, extra_context)
            return await _execute_agent_step(state, agent, agent_type)
    else:
        # Use default tools if no MCP servers are configured
        from src.agents.agents import create_agent_with_locale
        agent = create_agent_with_locale(state, agent_type, agent_type, default_tools, agent_type, locale, extra_context)
        return await _execute_agent_step(state, agent, agent_type)


async def researcher_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Researcher node that do research"""
    logger.info("Researcher node is researching.")
    configurable = Configuration.from_runnable_config(config)
    tools = [get_web_search_tool(configurable.max_search_results), crawl_tool]
    retriever_tool = get_retriever_tool(state.get("resources", []))
    if retriever_tool:
        tools.insert(0, retriever_tool)
    logger.info(f"Researcher tools: {tools}")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "researcher",
        tools,
    )


async def coder_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Coder node that do code analysis."""
    logger.info("Coder node is coding.")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "coder",
        [python_repl_tool],
    )


async def thinking_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Thinking node that performs deep analysis and synthesis."""
    logger.info("Thinking node is analyzing and synthesizing information.")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "thinking",
        [],  # No special tools needed for thinking steps
    )

# 在resaerch_team和outline_writer之间增加一个node，用于生成写作style
async def style_control_node(
    state: State, config: RunnableConfig
) -> Command[Literal["outline_writer"]]:
    """Style control node that generates writing style."""
    logger.info("Style control node is generating writing style.")
    # Temporarily set logger to DEBUG level for this node
    import logging
    original_level = logger.level
    logger.setLevel(logging.DEBUG)
    configurable = Configuration.from_runnable_config(config)
    
    # Find the processing step
    current_plan = state.get("current_plan")
    current_plan_step = ""
    if current_plan and current_plan.steps:
        for step in current_plan.steps:
            if step.step_type == StepType.PROCESSING:
                current_plan_step = step.description
                break
    
    # 获取state.messages中的用户交流历史记录, msg.name为空就是交流记录
    user_query = []
    if state["messages"]:
        for msg in state["messages"]:
            if not msg.name and msg.content != "":
                user_query.append(f"{msg.type}: {msg.content}")
    if not user_query:
        user_query = state["messages"][0].content if state["messages"] else ""

    
    # Apply outline writer prompt template
    template_state = {
        **state,
        "user_query": user_query,
        "current_plan_step": current_plan_step,
        "locale": state.get("locale", "en-US")
    }
    logger.debug(f"Template state keys: {list(template_state.keys())}")
    
    try:
        # Use simplified template to avoid model refusal
        messages = apply_prompt_template("style_control", template_state, configurable)
        messages = messages[:1] # only use the system message
    except Exception as e:
        logger.error(f"Failed to apply style control template: {e}")
        logger.debug(f"Template state: {template_state}")
        # Restore original logger level
        logger.setLevel(original_level)
        return Command(goto="outline_writer")
    
    # Get LLM for style_control generation
    if AGENT_LLM_MAP.get("style_control", "basic") == "basic":
        llm = get_llm_by_type("basic")
    else:
        llm = get_llm_by_type(AGENT_LLM_MAP.get("style_control", "basic"))
    
    # Generate style control - Use non-streaming approach for better reliability
    full_response = ""
    
    try:
        logger.debug("Using non-streaming LLM invoke for style control generation...")
        logger.debug(f"LLM object: {llm}")

        response = llm.invoke(messages)
        logger.debug(f"LLM response object: {response}")
        logger.debug(f"Response type: {type(response)}")
        
        full_response = response.content
        logger.info(f"Style control response: {full_response}")
    except Exception as e:
        logger.error(f"LLM invoke failed: {e}")
        import traceback
        logger.debug(f"Full traceback: {traceback.format_exc()}")
        
        # Try streaming as fallback (opposite of before)
        logger.debug("Trying streaming approach as fallback...")
        chunk_count = 0
        try:
            response = llm.stream(messages)
            for chunk in response:
                try:
                    chunk_count += 1
                    chunk_content = chunk.content   
                    full_response += chunk_content
                    logger.debug(f"Chunk {chunk_count}: '{chunk_content}' (length: {len(chunk_content)})")
                    
                except Exception as chunk_error:
                    logger.error(f"Error processing chunk {chunk_count}: {chunk_error}")
                    continue
            logger.info(f"Style control response (streaming fallback, {chunk_count} chunks): {full_response}")
            
        except Exception as e2:
            logger.error(f"Both invoke and streaming failed: {e2}")
            import traceback
            logger.debug(f"Streaming fallback traceback: {traceback.format_exc()}")
            # Restore original logger level
            logger.setLevel(original_level)
            return Command(goto="outline_writer")   

    # 记录style_control输出到数据库
    thread_id = state.get("thread_id")
    if thread_id:
        update_messages_to_database(
            thread_id=thread_id,
            agent="style_control",
            content=full_response,
            msg_type="assistant"
        )

    # Restore original logger level
    logger.setLevel(original_level)
    return Command(
        update={
            "messages": [AIMessage(content=full_response, name="style_control")],
            "style_control_result": full_response,
        },
        goto="outline_writer",
    )


def outline_writer_node(
    state: State, config: RunnableConfig
) -> Command[Literal["outline_feedback", "paper_writer"]]:
    """Outline writer node that generates paper outline based on research results."""
    logger.info("Outline writer node is generating paper outline.")
    
    # Temporarily set logger to DEBUG level for this node
    import logging
    original_level = logger.level
    logger.setLevel(logging.DEBUG)
    configurable = Configuration.from_runnable_config(config)
    
    # Prepare research results and planning information
    current_plan = state.get("current_plan")
    raw_plan = state.get("raw_plan", None)
    research_results = []
    research_thinking = ""
    research_results_list = []
    
    # Collect research results from completed steps
    if current_plan and current_plan.steps:
        for step in current_plan.steps:
            if step.execution_res:
                if step.step_type == StepType.THINKING:
                    research_thinking = step.execution_res
                elif step.step_type == StepType.RESEARCH:
                    research_results_list.append(step.execution_res)
            
        research_results = json.dumps(research_results_list, ensure_ascii=False)
        logger.debug(f"Found {len(research_results_list)} research results")
        
        # If no research results are available, we should not proceed with outline generation
        # as the outline would be of poor quality without research data
        if len(research_results_list) == 0:
            logger.warning("No research results available for outline generation. This may result in poor quality outline.")
            # Still proceed but with a warning
    else:
        logger.warning("No current plan or no steps found for outline generation")
        # Provide empty research results if no plan exists
        research_results = json.dumps([], ensure_ascii=False)
    
    # 获取state.messages中的用户交流历史记录, msg.name为空就是交流记录
    user_query = []
    if state["messages"]:
        for msg in state["messages"]:
            if not msg.name and msg.content != "":
                user_query.append(f"{msg.type}: {msg.content}")
    
    logger.debug(f"User query for outline: {user_query}")
    logger.debug(f"Current plan type: {type(current_plan)}")
    logger.debug(f"Current plan: {current_plan}")
    
    # Apply outline writer prompt template
    template_state = {
        **state,
        "user_query": user_query,
        "current_plan": json.dumps(current_plan.model_dump() if current_plan else {}, ensure_ascii=False),
        "locale": state.get("locale", "en-US"),
        "raw_plan": raw_plan
    }
    logger.debug(f"Template state keys: {list(template_state.keys())}")
    logger.debug(f"Template state locale: {template_state.get('locale')}")
    
    try:
        # Use simplified template to avoid model refusal
        messages = apply_prompt_template("outline_writer_simple", template_state, configurable)
        
        # Debug: Log the messages being sent to LLM
        logger.debug(f"Outline writer messages: {messages}")
        logger.debug(f"Template state keys: {list(template_state.keys())}")
        logger.debug(f"Research results length: {len(research_results)}")
        
        # Convert LangChain messages to proper format for LLM
        # Filter out assistant messages to avoid confusing the model
        converted_messages = []
        # for msg in messages:
        #     if isinstance(msg, dict):
        #         # Skip assistant messages that might confuse the model
        #         if msg.get('role') == 'assistant':
        #             continue
        #         converted_messages.append(msg)
        #     else:
        #         # Convert LangChain message to dict with proper role mapping
        #         if hasattr(msg, 'type'):
        #             if msg.type == 'human':
        #                 role = 'user'
        #             elif msg.type == 'ai':
        #                 # Skip AI/assistant messages to avoid confusion
        #                 continue
        #             elif msg.type == 'system':
        #                 role = 'system'
        #                 converted_msg = {
        #                     "role": role,
        #                     "content": msg.content
        #                 }
        #                 converted_messages.append(converted_msg)
        #             else:
        #                 role = 'user'  # Default fallback
        #         else:
        #             role = 'user'  # Default fallback
        
        messages = converted_messages
        # 增加思考和调研内容到用户消息中
        messages.append({
            "role": "user",
            "content": f"## ThinkWriter思考内容如下: {research_thinking}"
        })       
        messages.append({
            "role": "user",
            "content": f"## ThinkWriter调研内容如下: {research_results}"
        })  

        # 如果有，增加历史大纲内容
        old_outline = state.get("paper_outline", "")
        if old_outline:
            messages.append({
                "role": "user",
                "content": f"## 历史大纲内容如下: {old_outline}"
            })
            # 拼接最后一条消息(用户修改大纲的要求)
            feedback = state["messages"][-1].content
            messages.append({
                "role": "user",
                "content": f"## 用户修改大纲的要求: {feedback}"
            })

        logger.debug(f"Converted messages: {messages}")
    except Exception as e:
        logger.error(f"Failed to apply outline writer template: {e}")
        logger.debug(f"Template state: {template_state}")
        # Restore original logger level
        logger.setLevel(original_level)
        return Command(goto="reporter")
    
    # Get LLM for outline generation
    if AGENT_LLM_MAP.get("outline_writer", "basic") == "basic":
        llm = get_llm_by_type("basic")
    else:
        llm = get_llm_by_type(AGENT_LLM_MAP.get("outline_writer", "basic"))
    
    # Use default LLM configuration for now to avoid potential issues
    logger.debug(f"Using default LLM configuration")
    
    # Generate outline - Use non-streaming approach for better reliability
    full_response = ""
    
    try:
        logger.debug("Using non-streaming LLM invoke for outline generation...")
        logger.debug(f"LLM object: {llm}")
        
        response = llm.invoke(messages)
        logger.debug(f"LLM response object: {response}")
        logger.debug(f"Response type: {type(response)}")
        
        full_response = response.content
        logger.info(f"Outline writer response: {full_response}")
        logger.debug(f"Response length: {len(full_response)}")
        
        # Additional response metadata logging
        if hasattr(response, 'response_metadata'):
            logger.debug(f"Response metadata: {response.response_metadata}")
        
    except Exception as e:
        logger.error(f"LLM invoke failed: {e}")
        import traceback
        logger.debug(f"Full traceback: {traceback.format_exc()}")
        
        # Try streaming as fallback (opposite of before)
        logger.debug("Trying streaming approach as fallback...")
        chunk_count = 0
        try:
            response = llm.stream(messages)
            for chunk in response:
                try:
                    chunk_count += 1
                    chunk_content = chunk.content
                    full_response += chunk_content
                    logger.debug(f"Chunk {chunk_count}: '{chunk_content}' (length: {len(chunk_content)})")
                    
                except Exception as chunk_error:
                    logger.error(f"Error processing chunk {chunk_count}: {chunk_error}")
                    continue
            
            logger.info(f"Outline writer response (streaming fallback, {chunk_count} chunks): {full_response}")
            
        except Exception as e2:
            logger.error(f"Both invoke and streaming failed: {e2}")
            import traceback
            logger.debug(f"Streaming fallback traceback: {traceback.format_exc()}")
            # Restore original logger level
            logger.setLevel(original_level)
            return Command(goto="reporter")
    
    # Additional debug info if response is empty
    if not full_response.strip():
        logger.warning("Outline writer response is empty!")
        logger.debug(f"LLM type used: {AGENT_LLM_MAP.get('outline_writer', 'basic')}")
        logger.debug(f"Template used: outline_writer_simple")
        logger.debug(f"Template state keys: {list(template_state.keys())}")
        logger.debug(f"User query: {template_state.get('user_query', 'N/A')}")
        logger.debug(f"Research results length: {len(template_state.get('research_results', ''))}")
        logger.debug(f"Locale: {template_state.get('locale', 'N/A')}")
        
        # Convert messages to serializable format for logging
        serializable_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                serializable_messages.append(msg)
            else:
                # Convert LangChain message to dict
                serializable_messages.append({
                    "role": getattr(msg, 'role', 'unknown'),
                    "content": getattr(msg, 'content', str(msg))[:500] + "..." if len(getattr(msg, 'content', str(msg))) > 500 else getattr(msg, 'content', str(msg))
                })
        logger.debug(f"Messages sent to LLM (truncated): {json.dumps(serializable_messages, ensure_ascii=False, indent=2)}")
        
        # Return to reporter if response is empty
        # Restore original logger level
        logger.setLevel(original_level)
        return Command(goto="reporter")
    
    try:
        outline_data = json.loads(repair_json_output(full_response))
        
        # 记录outline_writer输出到数据库
        thread_id = state.get("thread_id")
        if thread_id:
            update_messages_to_database(
                thread_id=thread_id,
                agent="outline_writer",
                content=full_response,
                msg_type="assistant"
            )
        
        # Restore original logger level
        logger.setLevel(original_level)
        return Command(
            update={
                "messages": [AIMessage(content=full_response, name="outline_writer")],
                "paper_outline": full_response,
            },
            goto="outline_feedback",
        )
    except json.JSONDecodeError:
        logger.warning("Outline writer response is not valid JSON")
        logger.debug(f"Raw response: {full_response}")
        logger.debug(f"Repaired response: {repair_json_output(full_response)}")
        # Restore original logger level
        logger.setLevel(original_level)
        return Command(goto="reporter")


def outline_feedback_node(
    state,
) -> Command[Literal["outline_writer", "paper_writer", "__end__"]]:
    """Human feedback node for outline review and approval."""
    current_outline = state.get("paper_outline", "")
    
    # Check if outline is auto accepted
    auto_accepted_outline = state.get("auto_accepted_outline", False)
    if not auto_accepted_outline:
        feedback = interrupt("Please Review the Paper Outline.")
        
        # Handle feedback
        if feedback and str(feedback).upper().startswith("[EDIT_OUTLINE]"):
            thread_id = state.get("thread_id")
            if thread_id:
                # feedback去除前缀[ACCEPTED]
                feedback_content = feedback.replace("[edit_outline]", "").strip()
                update_messages_to_database(
                    thread_id=thread_id,
                    agent="ThinkWriter",
                    content=feedback_content,
                    msg_type="user"
                )
            return Command(
                update={
                    "messages": [
                        HumanMessage(content=feedback, name="feedback"),
                    ],
                },
                goto="outline_writer",
            )
        elif feedback and str(feedback).upper().startswith("[ACCEPTED]"):
            logger.info("Outline is accepted by user.")
        else:
            raise TypeError(f"Interrupt value of {feedback} is not supported.")
    
    # Parse and store approved outline
    try:
        outline_data = json.loads(repair_json_output(current_outline))
        return Command(
            update={
                "approved_outline": current_outline,
                "current_section_index": 0,
                "paper_sections": [],
            },
            goto="paper_writer",
        )
    except json.JSONDecodeError:
        logger.warning("Cannot parse outline JSON")
        return Command(goto="__end__")


def paper_writer_node(
    state: State, config: RunnableConfig
) -> Command[Literal["paper_writer", "references_writer", "reporter"]]:
    """Paper writer node that generates paper content section by section."""
    logger.info("Paper writer node is generating paper content.")
    configurable = Configuration.from_runnable_config(config)
    
    # Set debug logging temporarily
    original_level = logger.level
    logger.setLevel(logging.DEBUG)
    style_control_result = state.get("style_control_result", "")
    # Get approved outline and current section index
    approved_outline = state.get("approved_outline", "")
    current_section_index = state.get("current_section_index", 0)
    paper_sections = state.get("paper_sections", [])
    raw_plan = state.get("raw_plan", None)
    
    logger.debug(f"Paper writer state - outline exists: {bool(approved_outline)}")
    logger.debug(f"Current section index: {current_section_index}")
    logger.debug(f"Existing paper sections: {len(paper_sections)}")
    
    if not approved_outline:
        logger.warning("No approved outline found")
        logger.setLevel(original_level)
        return Command(goto="reporter")
    
    try:
        outline_data = json.loads(repair_json_output(approved_outline))
        sections = outline_data.get("sections", [])
        logger.debug(f"Total sections in outline: {len(sections)}")
        
        # Check if all sections are completed
        if current_section_index >= len(sections):
            logger.info("All sections completed, proceeding to references generation")
            # All sections completed, go to references writer
            logger.setLevel(original_level)
            return Command(
                update={
                    "sections_completed": True,
                },
                goto="references_writer",
            )
        
        # Get current section to write
        current_section = sections[current_section_index]
        logger.debug(f"Writing section {current_section_index}: {current_section.get('title', 'Unknown')}")
        
        # Prepare research results for this section
        current_plan = state.get("current_plan")
        research_results = ""
        current_plan_step = ""
        
        if current_plan and current_plan.steps:
            research_results_list = []
            for step in current_plan.steps:
                if step.execution_res and step.step_type == StepType.RESEARCH:
                    research_results_list.append({
                        "title": step.title,
                        "description": step.description,
                        "result": step.execution_res
                    })
                if step.step_type == StepType.PROCESSING:
                    current_plan_step = step
            research_results = json.dumps(research_results_list, ensure_ascii=False)
            logger.debug(f"Research results length: {len(research_results)}")
        
        # Get user query from messages
        user_query = state["messages"][0].content if state["messages"] else ""
        logger.debug(f"User query: {user_query[:100]}...")
        
        # Apply paper writer prompt template (use simplified version)
        template_state = {
            **state,
            "user_query": user_query,
            "current_plan_step": current_plan_step.description,
            "research_results": research_results,  # TODO 暂时用在系统提示词里,后续考虑放在用户提示词内
            "approved_outline": approved_outline,
            "last_section_content": paper_sections[current_section_index - 1] if current_section_index > 0 else "",
            "current_section": json.dumps(current_section, ensure_ascii=False),
            "locale": state.get("locale", "en-US"),
            "style_control_result": style_control_result,
            "raw_plan": raw_plan
        }
        
        try:
            messages = apply_prompt_template("paper_writer_simple", template_state, configurable)
            messages = messages[:1] # 暂时只取第一条消息(system)
            logger.debug("Template applied successfully")
        except Exception as e:
            logger.error(f"Template application failed: {e}")
            # Fallback to original template
            messages = apply_prompt_template("paper_writer", template_state, configurable)
        
        # Filter and convert messages to proper format
        filtered_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                # Skip assistant messages that might confuse the model
                if msg.get("role") != "assistant":
                    filtered_messages.append(msg)
            else:
                # Convert LangChain message to dict, skip assistant messages
                if hasattr(msg, 'role') and msg.role != "assistant":
                    filtered_messages.append({
                        "role": msg.role,
                        "content": msg.content
                    })
                elif not hasattr(msg, 'role'):
                    # Assume it's a user message if no role specified
                    if hasattr(msg, 'name') and msg.name == "paper_writer":
                        filtered_messages.append({
                            "role": "user",
                            "content": str(msg)
                        })
        
        logger.debug(f"Filtered messages count: {len(filtered_messages)}")
        
        # Get LLM for paper writing
        if AGENT_LLM_MAP.get("paper_writer", "basic") == "basic":
            llm = get_llm_by_type("basic")
        else:
            llm = get_llm_by_type(AGENT_LLM_MAP.get("paper_writer", "basic"))
        
        # Generate section content with fallback mechanism
        full_response = ""
        try:
            logger.debug("Trying LLM invoke for paper writer ...")
            response = llm.invoke(filtered_messages)
            full_response = response.content
            logger.debug(f"Invoke successful, response length: {len(full_response)}")
        except Exception as e:
            logger.error(f"LLM invoke failed: {e}")
            logger.setLevel(original_level)
            return Command(goto="reporter")
        
        logger.info(f"Paper writer response for section {current_section_index}: {full_response[:200]}...")
        
        if not full_response.strip():
            logger.warning("Paper writer returned empty response")
            logger.setLevel(original_level)
            return Command(goto="reporter")
        
        try:
            # section_data = json.loads(repair_json_output(full_response))
            # section_content = section_data.get("section_content", "")
            # section_references = section_data.get("references", "")
            section_content = full_response
            
            if not section_content.strip():
                logger.warning("Section content is empty after JSON parsing")
                logger.setLevel(original_level)
                return Command(goto="reporter")
            
            # 记录paper_writer输出到数据库
            thread_id = state.get("thread_id")
            if thread_id:
                update_messages_to_database(
                    thread_id=thread_id,
                    agent="paper_writer",
                    content=section_content,
                    msg_type="assistant"
                )

            # Add section content to paper sections
            updated_sections = paper_sections + [section_content]
            logger.debug(f"Section {current_section_index} completed, moving to next section")
            # logger.debug(f"Added {len(section_references)} references")

            # 存储section_references到state中, 列表扩充
            state_section_references = state.get("section_references", [])  
            # state_section_references.append(section_references)
            
            logger.setLevel(original_level)
            return Command(
                update={
                    "paper_sections": updated_sections,
                    "current_section_index": current_section_index + 1,
                    "messages": [AIMessage(content=section_content, name="paper_writer")],
                    "section_references": state_section_references,
                },
                goto="paper_writer",
            )
        except Exception as e:
            logger.warning(f"Paper writer response error: {e}")
            logger.debug(f"Raw response: {full_response}")
            logger.debug(f"Repaired response: {repair_json_output(full_response)}")
            logger.setLevel(original_level)
            return Command(goto="reporter")
            
    except json.JSONDecodeError as e:
        logger.warning(f"Cannot parse approved outline JSON: {e}")
        logger.setLevel(original_level)
        return Command(goto="reporter")


def references_writer_node(
    state: State, config: RunnableConfig
) -> Command[Literal["reporter"]]:
    """References writer node that generates properly formatted references."""
    logger.info("References writer node is generating references.")
    configurable = Configuration.from_runnable_config(config)
    
    # Set debug logging temporarily
    original_level = logger.level
    logger.setLevel(logging.DEBUG)
    
    # Get paper sections and outline
    approved_outline = state.get("approved_outline", "")
    paper_sections = state.get("paper_sections", [])
    
    logger.debug(f"References writer state - outline exists: {bool(approved_outline)}")
    logger.debug(f"Paper sections count: {len(paper_sections)}")
    
    if not approved_outline or not paper_sections:
        logger.warning("Missing outline or paper sections for references generation")
        logger.setLevel(original_level)
        return Command(goto="reporter")
    
    try:
        outline_data = json.loads(repair_json_output(approved_outline))
        sections = outline_data.get("sections", [])
        logger.info(f"lenght of sections: {len(sections)}")
        section_references = []
        for section in sections:
            section_references.extend(section.get("quotes", []))
            if section.get("subsections"):
                for sub_section in section.get("subsections"):
                    section_references.extend(sub_section.get("quotes", []))
        
        # # Prepare research results
        # current_plan = state.get("current_plan")
        # research_results = ""
        
        # if current_plan and current_plan.steps:
        #     research_results_list = []
        #     for step in current_plan.steps:
        #         if step.execution_res:
        #             research_results_list.append({
        #                 "title": step.title,
        #                 "description": step.description,
        #                 "result": step.execution_res
        #             })
        #     research_results = json.dumps(research_results_list, ensure_ascii=False)
        #     logger.debug(f"Research results length: {len(research_results)}")
        
        # # Get user query from messages
        # user_query = state["messages"][0].content if state["messages"] else ""
        # logger.debug(f"User query: {user_query[:100]}...")
        
        # # Combine paper sections for references analysis
        # combined_sections = "\n\n".join(paper_sections)
        
        # Apply references writer prompt template
        template_state = {
            **state,
            # "user_query": user_query,
            # "research_results": research_results,
            # "paper_sections": combined_sections,
            "section_references": section_references,
            "locale": state.get("locale", "en-US")
        }
        
        messages = apply_prompt_template("references_writer", template_state, configurable)
        messages = messages[:1] # 暂时只取第一条消息(system)
        logger.debug("References writer template applied successfully")
        
        # Filter and convert messages to proper format
        filtered_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                # Skip assistant messages that might confuse the model
                if msg.get("role") != "assistant":
                    filtered_messages.append(msg)
            else:
                # Convert LangChain message to dict, skip assistant messages
                if hasattr(msg, 'role') and msg.role != "assistant":
                    filtered_messages.append({
                        "role": msg.role,
                        "content": msg.content
                    })
                elif not hasattr(msg, 'role'):
                    # Assume it's a user message if no role specified
                    filtered_messages.append({
                        "role": "user", 
                        "content": str(msg)
                    })
        
        logger.debug(f"Filtered messages count: {len(filtered_messages)}")
        
        # Get LLM for references writing
        if AGENT_LLM_MAP.get("paper_writer", "basic") == "basic":
            llm = get_llm_by_type("basic")
        else:
            llm = get_llm_by_type(AGENT_LLM_MAP.get("paper_writer", "basic"))
        
        # Generate references
        full_response = ""
        try:
            logger.debug("Attempting to stream LLM response for references...")
            response = llm.stream(filtered_messages)
            for chunk in response:
                full_response += chunk.content
            logger.debug(f"References streaming successful, response length: {len(full_response)}")
        except Exception as e:
            logger.error(f"References LLM streaming failed: {e}")
            try:
                logger.debug("Trying LLM invoke as fallback for references...")
                response = llm.invoke(filtered_messages)
                full_response = response.content
                logger.debug(f"References invoke successful, response length: {len(full_response)}")
            except Exception as e2:
                logger.error(f"References LLM invoke also failed: {e2}")
                logger.setLevel(original_level)
                return Command(goto="reporter")
        
        logger.info(f"References writer response: {full_response[:200]}...")
        
        if not full_response.strip():
            logger.warning("References writer returned empty response")
            logger.setLevel(original_level)
            return Command(goto="reporter")
        
        try:
            references_content = full_response
            reference_count = len(state.get("section_references", []))
            # references_data = json.loads(repair_json_output(full_response))
            # references_content = references_data.get("references_content", "")
            # reference_count = references_data.get("reference_count", 0)
            # formatting_notes = references_data.get("formatting_notes", "")
            
            # logger.info(f"Generated {reference_count} references")
            # logger.debug(f"Formatting notes: {formatting_notes}")
            
            # Now generate final paper with proper references
            final_paper = f"# {outline_data.get('title', 'Research Paper')}\n\n"
            if outline_data.get('abstract'):
                final_paper += f"## Abstract\n\n{outline_data['abstract']}\n\n"
            
            for section_content in paper_sections:
                final_paper += section_content + "\n\n"
            
            # Add the AI-generated references section (let AI decide the title and format)
            if references_content.strip():
                final_paper += f"{references_content}\n\n"
            
            # 记录references_writer输出到数据库
            thread_id = state.get("thread_id")
            if thread_id:
                update_messages_to_database(
                    thread_id=thread_id,
                    agent="references_writer",
                    content=references_content,
                    msg_type="assistant"
                )

            logger.info(f"📄 Final paper with AI-generated references completed, length: {len(final_paper)} characters")
            logger.info(f"📄 Final paper preview: {final_paper[:300]}...")
            logger.info("📄 References writer completed, transitioning to reporter node")
            
            logger.setLevel(original_level)
            return Command(
                update={
                    "final_paper": final_paper,
                    "references_content": references_content,
                    "reference_count": reference_count,
                    "messages": [AIMessage(content=references_content, name="references_writer")],
                },
                goto="reporter",
            )
            
        except json.JSONDecodeError as e:
            logger.warning(f"References writer response is not valid JSON: {e}")
            logger.debug(f"Raw response: {full_response}")
            logger.debug(f"Repaired response: {repair_json_output(full_response)}")
            
            # Fallback: create basic references from outline
            final_paper = f"# {outline_data.get('title', 'Research Paper')}\n\n"
            if outline_data.get('abstract'):
                final_paper += f"## Abstract\n\n{outline_data['abstract']}\n\n"
            
            for section_content in paper_sections:
                final_paper += section_content + "\n\n"
            
            logger.info("📄 Final paper completed (without AI-generated references)")
            logger.setLevel(original_level)
            return Command(
                update={
                    "final_paper": final_paper,
                },
                goto="reporter",
            )
            
    except json.JSONDecodeError as e:
        logger.warning(f"Cannot parse approved outline JSON: {e}")
        logger.setLevel(original_level)
        return Command(goto="reporter")
