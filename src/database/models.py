# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, BigInteger, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class User(Base):
    """
    用户表模型
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    email = Column(String(255), unique=True, index=True, nullable=False, comment="用户邮箱")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="修改时间")
    total_credits = Column(Integer, default=1000, nullable=False, comment="总积分")
    remaining_credits = Column(Integer, default=1000, nullable=False, comment="剩余积分")
    
    # 关联关系
    chat_histories = relationship("ChatHistory", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', remaining_credits={self.remaining_credits})>"

class ChatHistory(Base):
    """
    对话历史记录表模型
    """
    __tablename__ = "chat_histories"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    thread_id = Column(String(255), nullable=False, index=True, comment="LangGraph线程ID")
    title = Column(String(100), nullable=False, default="", comment="对话标题")
    
    # 非reasoning模型token消耗
    non_reasoning_input_tokens = Column(BigInteger, default=0, nullable=False, comment="非reasoning模型输入token数")
    non_reasoning_output_tokens = Column(BigInteger, default=0, nullable=False, comment="非reasoning模型输出token数")
    
    # reasoning模型token消耗
    reasoning_input_tokens = Column(BigInteger, default=0, nullable=False, comment="reasoning模型输入token数")
    reasoning_output_tokens = Column(BigInteger, default=0, nullable=False, comment="reasoning模型输出token数")
    
    # 结果文档id
    result_document_id = Column(String(255), nullable=True, comment="结果文档ID")

    # 对话内容（可选，用于记录对话摘要）
    conversation_summary = Column(Text, comment="对话摘要")
    
    # 消息历史记录 - JSON格式存储
    messages = Column(JSON, nullable=True, comment="消息历史记录JSON格式")
    
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="修改时间")
    
    # 关联关系
    user = relationship("User", back_populates="chat_histories")
    
    def __repr__(self):
        return f"<ChatHistory(id={self.id}, user_id={self.user_id}, thread_id='{self.thread_id}', title='{self.title}')>"
    
    @property
    def total_input_tokens(self) -> int:
        """总输入token数"""
        return self.non_reasoning_input_tokens + self.reasoning_input_tokens
    
    @property
    def total_output_tokens(self) -> int:
        """总输出token数"""
        return self.non_reasoning_output_tokens + self.reasoning_output_tokens
    
    @property
    def total_tokens(self) -> int:
        """总token数"""
        return self.total_input_tokens + self.total_output_tokens 
    

class TaskExecution(Base):
    """
    任务执行状态表模型
    """
    __tablename__ = "task_executions"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True, comment="主键ID")
    task_id = Column(String(255), nullable=False, unique=True, index=True, comment="任务ID")
    thread_id = Column(String(255), nullable=False, index=True, comment="LangGraph线程ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    status = Column(String(50), nullable=False, default="pending", comment="任务状态: pending, running, completed, failed")
    progress = Column(Integer, default=0, nullable=False, comment="进度百分比 0-100")
    current_step = Column(String(255), nullable=True, comment="当前执行步骤")
    result_document_id = Column(String(255), nullable=True, comment="结果文档ID")
    error_message = Column(Text, nullable=True, comment="错误信息")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="修改时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    
    # 关联关系
    user = relationship("User")


class ResultDocument(Base):
    """
    结果文档表模型
    """
    __tablename__ = "result_documents"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True, comment="主键ID")
    document_id = Column(String(255), nullable=True, index=True, comment="结果文档ID")
    thread_id = Column(String(255), nullable=True, index=True, comment="对话唯一ID")
    # version = Column(Integer, nullable=False, comment="文件版本")
    url = Column(Text, nullable=False, comment="结果文档url")
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="修改时间")