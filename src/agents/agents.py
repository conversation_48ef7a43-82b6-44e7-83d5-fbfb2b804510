# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langgraph.prebuilt import create_react_agent

from src.graph.types import State
from src.prompts import apply_prompt_template
from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP



# Create agents using configured LLM types
def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str):
    """Factory function to create agents with consistent configuration."""
    return create_react_agent(
        name=agent_name,
        model=get_llm_by_type(AGENT_LLM_MAP[agent_type]),
        tools=tools,
        prompt=lambda state: apply_prompt_template(prompt_template, state),
    )


def create_agent_with_locale(tr_state: State, agent_name: str, agent_type: str, tools: list, prompt_template: str, locale: str, extra_context: dict = None):
    """Factory function to create agents with locale and extra context."""
    
    def enhanced_prompt(state):
        # Merge the LangGraph state with locale and extra context
        enhanced_state = {
            **state,
            "locale": locale,
        }
        raw_plan = tr_state.get("raw_plan", None)
        if raw_plan:
            enhanced_state["raw_plan"] = raw_plan
        # Add current plan and current step information
        current_plan = tr_state.get("current_plan")

        if current_plan:
            enhanced_state["current_plan"] = current_plan
            # Find the first unexecuted step as current_plan_step
            current_step = None
            for step in current_plan.steps:
                if not step.execution_res:
                    current_step = step
                    break
            
            if current_step:
                enhanced_state["current_plan_step"] = f"Description\n\n{current_step.description}\n\n## Locale\n\n{state.get('locale', 'en-US')}"
        
            # Get user query from messages - use the original user query, not the latest message
        user_query = ""
        if state["messages"]:
            # Find the first user message (original query)
            for msg in state["messages"]:
                if hasattr(msg, 'role') and (msg.role == "user" or msg.role == "human"):
                    user_query = msg.content
                    break
            # Fallback to first message if no user role found
            if not user_query:
                user_query = state["messages"][0].content if state["messages"] else ""
        enhanced_state["user_query"] = user_query
        # extra_context会覆盖上面的设置，所以需要优先改extra_context
        if extra_context:
            enhanced_state.update(extra_context)
        print(f"Enhanced state: {enhanced_state}")
        return apply_prompt_template(prompt_template, enhanced_state)
    
    return create_react_agent(
        name=agent_name,
        model=get_llm_by_type(AGENT_LLM_MAP[agent_type]),
        tools=tools,
        prompt=enhanced_prompt,
    )
