# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import Optional, List
from src.database.models import User, ChatHistory, ResultDocument
from src.auth.utils import (
    generate_verification_code, 
    store_verification_code, 
    verify_verification_code,
    send_verification_email,
    verify_google_token,
    create_access_token
)
from src.auth.models import TokenUsageRequest

class AuthService:
    """
    用户认证服务类
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户
        """
        return self.db.query(User).filter(User.email == email).first()
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        根据ID获取用户
        """
        return self.db.query(User).filter(User.id == user_id).first()
    
    def create_user(self, email: str, initial_credits: int = 1000) -> User:
        """
        创建新用户
        """
        user = User(
            email=email,
            total_credits=initial_credits,
            remaining_credits=initial_credits
        )
        
        try:
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
            return user
        except IntegrityError:
            self.db.rollback()
            # 如果邮箱已存在，返回现有用户
            return self.get_user_by_email(email)
    
    def send_verification_code(self, email: str) -> bool:
        """
        发送验证码
        """
        code = generate_verification_code()
        
        # 存储验证码到Redis
        if not store_verification_code(email, code):
            return False
        
        # 发送邮件
        return send_verification_email(email, code)
    
    def login_with_email(self, email: str, verification_code: str) -> Optional[str]:
        """
        邮箱验证码登录
        
        Returns:
            JWT token if successful, None otherwise
        """
        # 验证验证码
        if not verify_verification_code(email, verification_code):
            return None
        
        # 获取或创建用户
        user = self.get_user_by_email(email)
        if not user:
            user = self.create_user(email)
        
        # 生成JWT token
        token_data = {
            "user_id": user.id,
            "email": user.email
        }
        return create_access_token(token_data)
    
    def login_with_google(self, google_token: str) -> Optional[str]:
        """
        Google登录
        
        Returns:
            JWT token if successful, None otherwise
        """
        # 验证Google token
        google_user_info = verify_google_token(google_token)
        if not google_user_info:
            return None
        
        email = google_user_info.get('email')
        if not email:
            return None
        
        # 获取或创建用户
        user = self.get_user_by_email(email)
        if not user:
            user = self.create_user(email)
        
        # 生成JWT token
        token_data = {
            "user_id": user.id,
            "email": user.email
        }
        return create_access_token(token_data)
    
    def create_or_get_chat_history(self, user_id: int, thread_id: str, title: str) -> ChatHistory:
        """
        创建或获取对话历史记录
        
        Args:
            user_id: 用户ID
            thread_id: 线程ID
            title: 对话标题
        
        Returns:
            ChatHistory: 对话历史记录
        """
        # 检查是否已存在相同thread_id的记录
        existing_chat = self.db.query(ChatHistory).filter(
            ChatHistory.user_id == user_id,
            ChatHistory.thread_id == thread_id
        ).first()
        
        if existing_chat:
            # 如果已存在，直接返回
            return existing_chat
        
        # 创建新记录
        chat_history = ChatHistory(
            user_id=user_id,
            thread_id=thread_id,
            title=title,
            non_reasoning_input_tokens=0,
            non_reasoning_output_tokens=0,
            reasoning_input_tokens=0,
            reasoning_output_tokens=0
        )
        
        self.db.add(chat_history)
        self.db.commit()
        self.db.refresh(chat_history)
        
        return chat_history
    
    def update_chat_history(self, chat_history: ChatHistory):
        """
        更新对话历史记录
        """
        self.db.commit()
        self.db.refresh(chat_history)
        return chat_history
    
    def get_user_chat_threads(self, user_id: int, limit: int = 50, offset: int = 0) -> tuple[List[ChatHistory], int]:
        """
        获取用户对话线程列表
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
        
        Returns:
            tuple: (对话历史列表, 总数)
        """
        # 获取总数
        total = self.db.query(ChatHistory).filter(ChatHistory.user_id == user_id).count()
        
        # 获取对话列表，按更新时间倒序
        threads = self.db.query(ChatHistory).filter(
            ChatHistory.user_id == user_id
        ).order_by(ChatHistory.updated_at.desc()).offset(offset).limit(limit).all()
        
        return threads, total
    
    def record_token_usage(self, user_id: int, usage_request: TokenUsageRequest) -> ChatHistory:
        """
        记录token使用情况
        """
        # 检查是否已存在相同thread_id的记录
        existing_chat = self.db.query(ChatHistory).filter(
            ChatHistory.user_id == user_id,
            ChatHistory.thread_id == usage_request.thread_id
        ).first()
        
        if existing_chat:
            # 更新现有记录
            existing_chat.non_reasoning_input_tokens += usage_request.non_reasoning_input_tokens
            existing_chat.non_reasoning_output_tokens += usage_request.non_reasoning_output_tokens
            existing_chat.reasoning_input_tokens += usage_request.reasoning_input_tokens
            existing_chat.reasoning_output_tokens += usage_request.reasoning_output_tokens
            
            if usage_request.conversation_summary:
                existing_chat.conversation_summary = usage_request.conversation_summary
            
            chat_history = existing_chat
        else:
            # 创建新记录
            chat_history = ChatHistory(
                user_id=user_id,
                thread_id=usage_request.thread_id,
                title="",  # 这里可以根据需要设置标题
                non_reasoning_input_tokens=usage_request.non_reasoning_input_tokens,
                non_reasoning_output_tokens=usage_request.non_reasoning_output_tokens,
                reasoning_input_tokens=usage_request.reasoning_input_tokens,
                reasoning_output_tokens=usage_request.reasoning_output_tokens,
                conversation_summary=usage_request.conversation_summary
            )
            self.db.add(chat_history)
        
        self.db.commit()
        self.db.refresh(chat_history)
        
        # 可以在这里实现积分扣减逻辑
        self._deduct_credits(user_id, chat_history.total_tokens)
        
        return chat_history
    
    def get_user_chat_history(self, user_id: int, limit: int = 50, offset: int = 0) -> List[ChatHistory]:
        """
        获取用户对话历史，不带messages(因为太大了)
        """
        return self.db.query(ChatHistory.id, ChatHistory.thread_id, ChatHistory.title, ChatHistory.created_at, ChatHistory.updated_at).filter(
            ChatHistory.user_id == user_id
        ).order_by(ChatHistory.created_at.desc()).offset(offset).limit(limit).all()
    
    def get_chat_history_by_thread(self, user_id: int, thread_id: str) -> Optional[ChatHistory]:
        """
        根据thread_id获取对话历史（包含messages）
        """
        return self.db.query(ChatHistory).filter(
            ChatHistory.user_id == user_id,
            ChatHistory.thread_id == thread_id
        ).first()
    
    def _deduct_credits(self, user_id: int, tokens_used: int):
        """
        扣减用户积分（示例实现）
        
        Args:
            user_id: 用户ID
            tokens_used: 使用的token数量
        """
        user = self.get_user_by_id(user_id)
        if user:
            # 简单的积分扣减逻辑：每1000个token扣1积分
            credits_to_deduct = max(1, tokens_used // 1000)
            user.remaining_credits = max(0, user.remaining_credits - credits_to_deduct)
            self.db.commit()
    
    def update_user_credits(self, user_id: int, additional_credits: int) -> Optional[User]:
        """
        更新用户积分
        """
        user = self.get_user_by_id(user_id)
        if user:
            user.total_credits += additional_credits
            user.remaining_credits += additional_credits
            self.db.commit()
            self.db.refresh(user)
        return user 
    
    def get_result_document_by_id(self, result_document_id: str) -> Optional[ResultDocument]:
        """
        根据结果文档ID获取结果文档
        """
        return self.db.query(ResultDocument).filter(ResultDocument.document_id == result_document_id).first()
    

    def create_result_document(self, document_id: str, url: str) -> ResultDocument:
        """
        创建结果文档
        """
        result_document = ResultDocument(document_id=document_id, url=url)
        self.db.add(result_document)
        self.db.commit()
        self.db.refresh(result_document)
        return result_document