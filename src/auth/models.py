# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List, Dict



class UserLoginRequest(BaseModel):
    """
    用户登录请求模型（邮箱验证码登录）
    """
    email: EmailStr
    verification_code: str

class GoogleLoginRequest(BaseModel):
    """
    Google登录请求模型
    """
    google_token: str

class EmailVerificationRequest(BaseModel):
    """
    发送邮箱验证码请求模型
    """
    email: EmailStr

class UserResponse(BaseModel):
    """
    用户信息响应模型
    """
    id: int
    email: str
    created_at: datetime
    updated_at: datetime
    total_credits: int
    remaining_credits: int
    
    class Config:
        from_attributes = True

class LoginResponse(BaseModel):
    """
    登录响应模型
    """
    access_token: str
    token_type: str = "bearer"
    user: UserResponse

class SendVerificationResponse(BaseModel):
    """
    发送验证码响应模型
    """
    success: bool
    message: str
    expires_in: int = 300  # 验证码有效期5分钟

class TokenUsageRequest(BaseModel):
    """
    Token使用记录请求模型
    """
    thread_id: str
    non_reasoning_input_tokens: int = 0
    non_reasoning_output_tokens: int = 0
    reasoning_input_tokens: int = 0
    reasoning_output_tokens: int = 0
    conversation_summary: Optional[str] = None

class ChatHistoryResponse(BaseModel):
    """
    对话历史响应模型
    """
    id: int
    thread_id: str
    title: str
    # non_reasoning_input_tokens: int
    # non_reasoning_output_tokens: int
    # reasoning_input_tokens: int
    # reasoning_output_tokens: int
    # conversation_summary: Optional[str]
    # messages: List[Dict]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ResultDocument(BaseModel):
    id: int
    document_id: str
    thread_id: str
    url: str
    created_at: datetime
    updated_at: datetime


class ChatHistoryDetailResponse(BaseModel):
    """
    对话历史响应模型
    """
    id: int
    thread_id: str
    title: str
    non_reasoning_input_tokens: int
    non_reasoning_output_tokens: int
    reasoning_input_tokens: int
    reasoning_output_tokens: int
    conversation_summary: Optional[str]
    messages: List[Dict]
    created_at: datetime
    updated_at: datetime
    result_document: Optional[ResultDocument]

    class Config:
        from_attributes = True

class ChatThreadItem(BaseModel):
    """
    对话线程项目模型
    """
    thread_id: str
    title: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ChatThreadListResponse(BaseModel):
    """
    对话线程列表响应模型
    """
    threads: List[ChatThreadItem]
    total: int 