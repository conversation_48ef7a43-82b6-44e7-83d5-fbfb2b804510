# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from fastapi import HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
from src.auth.utils import verify_access_token
from src.auth.service import AuthService
from src.database import get_db, User

# HTTP Bearer安全方案
security = HTTPBearer()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    获取当前认证用户
    
    Args:
        credentials: HTTP Bearer认证凭证
        db: 数据库会话
    
    Returns:
        当前用户对象
    
    Raises:
        HTTPException: 如果认证失败
    """
    # 验证token
    token_data = verify_access_token(credentials.credentials)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查是否是错误响应
    if isinstance(token_data, dict) and "error" in token_data:
        if token_data["error"] == "token_expired":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer", "Error-Code": "TOKEN_EXPIRED"},
            )
        elif token_data["error"] == "token_invalid":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token format or signature",
                headers={"WWW-Authenticate": "Bearer", "Error-Code": "TOKEN_INVALID"},
            )
    
    # 获取用户信息
    auth_service = AuthService(db)
    user = auth_service.get_user_by_id(token_data.get("user_id"))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer", "Error-Code": "USER_NOT_FOUND"},
        )
    
    return user

def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    获取当前认证用户（可选）
    
    如果没有提供认证信息或认证失败，返回None而不是抛出异常
    
    Args:
        credentials: HTTP Bearer认证凭证（可选）
        db: 数据库会话
    
    Returns:
        当前用户对象或None
    """
    if not credentials:
        return None
    
    try:
        # 验证token
        token_data = verify_access_token(credentials.credentials)
        if not token_data:
            return None
        
        # 检查是否是错误响应（对于可选认证，错误时返回None）
        if isinstance(token_data, dict) and "error" in token_data:
            return None
        
        # 获取用户信息
        auth_service = AuthService(db)
        user = auth_service.get_user_by_id(token_data.get("user_id"))
        return user
    except Exception:
        return None

def require_credits(min_credits: int = 1):
    """
    装饰器：要求用户有足够的积分
    
    Args:
        min_credits: 最低积分要求
    
    Returns:
        装饰器函数
    """
    def decorator(current_user: User = Depends(get_current_user)) -> User:
        if current_user.remaining_credits < min_credits:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"Insufficient credits. Required: {min_credits}, Available: {current_user.remaining_credits}"
            )
        return current_user
    
    return decorator 