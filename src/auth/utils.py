# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import random
import string
import smtplib
import redis
import jwt
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from google.auth.transport import requests
from google.oauth2 import id_token
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# Redis配置（用于存储验证码）
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

# 邮箱配置
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.163.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", "465"))
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "<EMAIL>")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "OXPQRZKIWQCYQWHD")
FROM_EMAIL = os.getenv("FROM_EMAIL", SMTP_USERNAME)

# JWT配置
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this-11")
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = int(os.getenv("JWT_EXPIRATION_HOURS", "72"))

# Google OAuth配置
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "")

def get_redis_client():
    """获取Redis客户端"""
    try:
        return redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
    except Exception as e:
        print(f"Redis连接失败: {e}")
        return None

def generate_verification_code(length: int = 6) -> str:
    """
    生成数字验证码
    """
    return ''.join(random.choices(string.digits, k=length))

def store_verification_code(email: str, code: str, expires_in: int = 300) -> bool:
    """
    将验证码存储到Redis中
    
    Args:
        email: 邮箱地址
        code: 验证码
        expires_in: 过期时间（秒）
    
    Returns:
        是否存储成功
    """
    redis_client = get_redis_client()
    if not redis_client:
        return False
    
    try:
        key = f"verification_code:{email}"
        redis_client.setex(key, expires_in, code)
        return True
    except Exception as e:
        print(f"存储验证码失败: {e}")
        return False

def verify_verification_code(email: str, code: str) -> bool:
    """
    验证验证码
    
    Args:
        email: 邮箱地址
        code: 用户输入的验证码
    
    Returns:
        验证是否成功
    """
    redis_client = get_redis_client()
    if not redis_client:
        return False
    
    try:
        key = f"verification_code:{email}"
        stored_code = redis_client.get(key)
        
        if not stored_code:
            return False
        
        # 验证成功后删除验证码
        if stored_code == code:
            redis_client.delete(key)
            return True
        
        return False
    except Exception as e:
        print(f"验证验证码失败: {e}")
        return False

def send_verification_email(email: str, code: str) -> bool:
    """
    发送验证码邮件
    
    Args:
        email: 收件人邮箱
        code: 验证码
    
    Returns:
        是否发送成功
    """
    if not SMTP_USERNAME or not SMTP_PASSWORD:
        print("邮箱配置未设置")
        return False
    
    try:
        # 创建邮件
        msg = MIMEMultipart()
        msg['From'] = FROM_EMAIL
        msg['To'] = email
        msg['Subject'] = "ThinkWriter Verification Code"
        
        # HTML邮件正文
        html_body = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
        }}
        .container_oJOd_0 {{
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #dddddd;
            border-radius: 5px;
        }}
        .header_mHO3_1 {{
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 1px solid #eeeeee;
        }}
        .content_7E5Y_2 {{
            padding: 20px 0;
            text-align: center;
        }}
        .verification-code_B0aj_3 {{
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 5px;
            text-align: center;
        }}
        .footer_uCbh_4 {{
            text-align: center;
            font-size: 12px;
            color: #999999;
            padding-top: 10px;
            border-top: 1px solid #eeeeee;
        }}
    </style>
</head>
<body>
    <div class="container_oJOd_0">
        <div class="header_mHO3_1">
            <h2>Verification Code</h2>
        </div>
        
        <div class="content_7E5Y_2">
            <p>Hello!</p>
            <p>Your verification code is:</p>
            
            <div class="verification-code_B0aj_3">
                {code}
            </div>
            
            <p>This code is valid for 5 minutes. Please do not share it with anyone.</p>
        </div>
        
        <div class="footer_uCbh_4">
            <p>This is an automated email. Please do not reply.</p>
            <p>ThinkWriter Team</p>
        </div>
    </div>
</body>
</html>
        """.strip()
        
        msg.attach(MIMEText(html_body, 'html', 'utf-8'))
        
        # 连接SMTP服务器并发送邮件
        with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            server.sendmail(SMTP_USERNAME, email, msg.as_string())
        
        return True
    except Exception as e:
        print(f"发送邮件失败: {e}")
        return False

def verify_google_token(token: str) -> Optional[Dict[str, Any]]:
    """
    验证Google OAuth token
    
    Args:
        token: Google ID token
    
    Returns:
        用户信息字典，如果验证失败返回None
    """
    if not GOOGLE_CLIENT_ID:
        print("Google客户端ID未配置")
        return None
    
    try:
        # 验证token
        idinfo = id_token.verify_oauth2_token(
            token, requests.Request(), GOOGLE_CLIENT_ID
        )
        
        # 检查issuer
        if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
            raise ValueError('Wrong issuer.')
        
        return {
            'email': idinfo.get('email'),
            'name': idinfo.get('name'),
            'picture': idinfo.get('picture'),
            'google_id': idinfo.get('sub')
        }
    except ValueError as e:
        print(f"Google token验证失败: {e}")
        return None
    except Exception as e:
        print(f"Google token验证异常: {e}")
        return None

def create_access_token(data: Dict[str, Any]) -> str:
    """
    创建JWT访问令牌
    
    Args:
        data: 要编码的数据
    
    Returns:
        JWT token字符串
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_access_token(token: str) -> Optional[Dict[str, Any]]:
    """
    验证JWT访问令牌
    
    Args:
        token: JWT token字符串
    
    Returns:
        解码后的数据，如果验证失败返回None
        如果token过期，返回{"error": "token_expired"}
        如果token无效，返回{"error": "token_invalid"}
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return {"error": "token_expired"}
    except jwt.PyJWTError:
        return {"error": "token_invalid"} 