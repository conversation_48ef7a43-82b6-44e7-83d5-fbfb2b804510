# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class StepType(str, Enum):
    RESEARCH = "research"
    PROCESSING = "processing"
    THINKING = "thinking"


class Step(BaseModel):
    need_search: bool = Field(..., description="Must be explicitly set for each step")
    title: str
    description: str = Field(..., description="Specify exactly what data to collect")
    step_type: StepType = Field(..., description="Indicates the nature of the step")
    execution_res: Optional[str] = Field(
        default=None, description="The Step execution result"
    )


class Plan(BaseModel):
    locale: str = Field(
        ..., description="e.g. 'en-US' or 'zh-CN', based on the user's language"
    )
    has_enough_context: bool
    thought: str
    title: str
    task_type: str = Field(
        ..., description="Type of task (e.g., 学术论文, 市场分析, 技术报告, 综合研究)"
    )
    writing_style: str = Field(
        ..., description="Writing style (e.g., 学术严谨, 通俗易懂, 专业技术, 商业报告)"
    )
    target_audience: str = Field(
        ..., description="Target audience (e.g., 学术研究者, 普通读者, 专业人士, 决策者)"
    )
    word_count: str = Field(
        ..., description="Expected word count (e.g., 3000-5000字, 1万字以上, 简要报告)"
    )
    need_outline: bool = Field(
        ..., description="Whether an outline is needed based on word count (true for >500 words)"
    )
    steps: List[Step] = Field(
        default_factory=list,
        description="Research, Thinking & Processing steps to get more context",
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "has_enough_context": False,
                    "thought": (
                        "To understand the current market trends in AI, we need to gather comprehensive information and conduct deep analysis."
                    ),
                    "title": "AI Market Research Plan",
                    "task_type": "市场分析",
                    "writing_style": "专业技术",
                    "target_audience": "决策者",
                    "word_count": "5000-8000字",
                    "need_outline": True,
                    "steps": [
                        {
                            "need_search": True,
                            "title": "Current AI Market Analysis",
                            "description": (
                                "Collect data on market size, growth rates, major players, and investment trends in AI sector."
                            ),
                            "step_type": "research",
                        },
                        {
                            "need_web_search": False,
                            "title": "Deep Analysis of AI Market Trends",
                            "description": (
                                "Analyze the collected market data to identify key trends, competitive dynamics, and strategic implications."
                            ),
                            "step_type": "thinking",
                        }
                    ],
                }
            ]
        }


# New structures for the updated JSON format

class CitationStyle(str, Enum):
    """Citation styles for academic and professional writing."""
    GENERAL = "General"
    MLA = "MLA" 
    APA = "APA"
    IEEE = "IEEE"
    AMA = "AMA"
    ACS = "ACS"
    HARVARD = "Harvard"
    VANCOUVER = "Vancouver"


class TaskItem(BaseModel):
    """Individual task item in execution phases."""
    task_id: str = Field(..., description="Unique identifier for the task (e.g., T1, R1, W1)")
    description: str = Field(..., description="Description of what needs to be done")
    search_terms: Optional[List[str]] = Field(
        default=None, 
        description="Search terms for research tasks (only applicable to research_phase)"
    )


class TaskSummary(BaseModel):
    """Summary of the writing task with metadata."""
    title: str = Field(..., description="The title of the writing task")
    type: str = Field(..., description="The type of writing (e.g., blog post, academic paper, marketing copy)")
    style: str = Field(..., description="The writing style to be used (e.g., formal, conversational, persuasive)")
    target_audience: str = Field(..., description="The intended readers of the content")
    word_count: str = Field(..., description="Estimated word count range")
    if_outline: bool = Field(..., description="Whether outline is needed (true for complex tasks >600 words)")
    citation_style: CitationStyle = Field(..., description="Citation format to be used")


class ExecutionPlan(BaseModel):
    """Execution plan with different phases of tasks."""
    thinking_phase: List[TaskItem] = Field(
        default_factory=list,
        description="Thinking tasks - only one if needed"
    )
    research_phase: List[TaskItem] = Field(
        default_factory=list, 
        description="Research tasks - 3-5 items if research is needed"
    )
    writing_phase: List[TaskItem] = Field(
        default_factory=list,
        description="Writing tasks - always has at least one item"
    )


class NewPlanResponse(BaseModel):
    """New format for planner response with structured task and execution plan."""
    thought: str = Field(..., description="Thinking before Plan")
    task_summary: TaskSummary = Field(..., description="Summary of the writing task")
    execution_plan: ExecutionPlan = Field(..., description="Detailed execution plan with phases")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "task_summary": {
                        "title": "AI Market Research Analysis",
                        "type": "market analysis",
                        "style": "professional technical",
                        "target_audience": "decision makers",
                        "word_count": "5000-8000 words",
                        "if_outline": True,
                        "citation_style": "APA"
                    },
                    "execution_plan": {
                        "thinking_phase": [
                            {
                                "task_id": "T1",
                                "description": "Analyze the AI market landscape and identify key trends"
                            }
                        ],
                        "research_phase": [
                            {
                                "task_id": "R1",
                                "description": "Research latest AI market statistics and growth rates",
                                "search_terms": ["AI market size", "artificial intelligence growth", "AI investment trends"]
                            },
                            {
                                "task_id": "R2",
                                "description": "Research major AI companies and competitive landscape", 
                                "search_terms": ["AI companies", "AI market leaders", "AI competition"]
                            }
                        ],
                        "writing_phase": [
                            {
                                "task_id": "W1",
                                "description": "Create comprehensive market analysis report with outline"
                            }
                        ]
                    }
                }
            ]
        }
