# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT
import json
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, model_validator

from src.rag.retriever import Resource


class RAGConfigResponse(BaseModel):
    """Response model for RAG configuration."""
    provider: str = Field(..., description="The selected RAG provider")


class RAGResourceRequest(BaseModel):
    """Request model for listing RAG resources."""
    query: Optional[str] = Field(None, description="Search query to filter resources")


class RAGResourcesResponse(BaseModel):
    """Response model for RAG resources."""
    resources: List[Resource] = Field(..., description="List of available resources")


class RAGUploadRequest(BaseModel):
    """Request model for uploading documents to RAG."""
    dataset_id: str = Field(..., description="The ID of the dataset to upload to")

    @model_validator(mode='before')
    @classmethod
    def validate_to_json(cls, value: Any) -> Any:
        print(value)
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class RAGUploadResponse(BaseModel):
    """Response model for document upload."""
    success: bool = Field(..., description="Whether the upload was successful")
    message: str = Field(..., description="Success or error message")
    data: Optional[Dict[str, Any]] = Field(None, description="Upload result data")


class RAGCreateDatasetRequest(BaseModel):
    """Request model for creating a new dataset."""
    name: str = Field(..., description="Name of the dataset")
    description: Optional[str] = Field("", description="Description of the dataset")
    chunk_method: Optional[str] = Field("naive", description="Chunking method")
    embedding_model: Optional[str] = Field(None, description="Embedding model to use")


class RAGCreateDatasetResponse(BaseModel):
    """Response model for dataset creation."""
    success: bool = Field(..., description="Whether the creation was successful")
    message: str = Field(..., description="Success or error message")
    dataset: Optional[Dict[str, Any]] = Field(None, description="Created dataset information")


class RAGDocumentListResponse(BaseModel):
    """Response model for listing documents."""
    documents: List[Dict[str, Any]] = Field(..., description="List of documents in the dataset")


class RAGDocumentDeleteRequest(BaseModel):
    """Request model for deleting documents."""
    dataset_id: str = Field(..., description="The ID of the dataset")
    document_ids: List[str] = Field(..., description="List of document IDs to delete")


class RAGDocumentDeleteResponse(BaseModel):
    """Response model for document deletion."""
    success: bool = Field(..., description="Whether the deletion was successful")
    message: str = Field(..., description="Success or error message")


class RAGDocumentParseRequest(BaseModel):
    """Request model for parsing documents."""
    dataset_id: str = Field(..., description="The ID of the dataset")
    document_ids: List[str] = Field(..., description="List of document IDs to parse")


class RAGDocumentParseResponse(BaseModel):
    """Response model for document parsing."""
    success: bool = Field(..., description="Whether the parsing was initiated successfully")
    message: str = Field(..., description="Success or error message")
