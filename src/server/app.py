# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import asyncio
import base64
import json
import logging
import os
from typing import Annotated, List, cast
from uuid import uuid4
import httpx

logger = logging.getLogger(__name__)

from fastapi import FastAPI, HTTPException, Query, UploadFile, File, Form, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse, JSONResponse
from starlette.requests import ClientDisconnect
from langchain_core.messages import AIMessageChunk, ToolMessage, BaseMessage
from langgraph.types import Command
from langgraph.graph.state import CompiledStateGraph

from src.config.tools import SELECTED_RAG_PROVIDER
from src.graph.builder import build_graph_with_memory
from src.podcast.graph.builder import build_graph as build_podcast_graph
from src.ppt.graph.builder import build_graph as build_ppt_graph
from src.prose.graph.builder import build_graph as build_prose_graph
from src.rag.builder import build_retriever
from src.rag.retriever import Resource
from src.server.chat_request import (
    ChatMessage,
    ChatRequest,
    FinalPaperResponse,
    GeneratePodcastRequest,
    GeneratePPTRequest,
    GenerateProseRequest,
    TTSRequest,
)
from src.server.mcp_request import MCPServerMetadataRequest, MCPServerMetadataResponse
from src.server.mcp_utils import load_mcp_tools
from src.server.rag_request import (
    RAGConfigResponse,
    RAGResourceRequest,
    RAGResourcesResponse,
    RAGUploadRequest,
    RAGUploadResponse,
    RAGCreateDatasetRequest,
    RAGCreateDatasetResponse,
    RAGDocumentListResponse,
    RAGDocumentDeleteRequest,
    RAGDocumentDeleteResponse,
    RAGDocumentParseRequest,
    RAGDocumentParseResponse,
)
from src.server.langsmith_api import router as langsmith_router
from src.server.auth_routes import router as auth_router
from src.server.file_routes import router as file_router
from src.server.background_tasks import background_task_manager
from src.database.database import create_tables, get_db, SessionLocal
from src.tools import VolcengineTTS
from src.auth.middleware import get_current_user
from src.auth.service import AuthService
from src.database.models import User, ChatHistory
from src.server.token_manager import token_manager
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# 初始化数据库表
try:
    create_tables()
    logger.info("数据库表初始化成功")
except Exception as e:
    logger.warning(f"数据库表初始化失败: {e}")

app = FastAPI(
    title="ThinkWriter API",
    description="API for Deer with Authentication and Database Support",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(langsmith_router)
app.include_router(auth_router, prefix="/api")
app.include_router(file_router, prefix="/api")

graph = build_graph_with_memory()


@app.post("/api/chat/stream")
async def chat_stream(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    聊天流接口 - 需要认证
    """
    thread_id = request.thread_id
    if thread_id == "__default__":
        thread_id = str(uuid4())
    
    # 创建或获取对话历史记录
    auth_service = AuthService(db)
    
    # 从第一条消息的内容中提取标题（前50个字符）
    title = ""
    if request.messages and len(request.messages) > 0:
        first_message_content = request.messages[0].content
        title = first_message_content[:50] if len(first_message_content) > 50 else first_message_content
    
    # 创建或获取对话历史记录
    chat_history = auth_service.create_or_get_chat_history(
        user_id=current_user.id,
        thread_id=thread_id,
        title=title
    )
    
    # 检查用户积分
    has_enough_credits, remaining_credits = token_manager.check_user_credits(current_user.id, db)
    if not has_enough_credits:
        raise HTTPException(
            status_code=403,
            detail=f"积分不足，当前剩余积分: {remaining_credits}，需要至少 {token_manager.min_credits_threshold} 积分"
        )

    async def safe_stream_wrapper():
        """包装流式生成器，添加额外的客户端断开连接检测"""
        try:
            async for chunk in _astream_workflow_generator(
                request.model_dump()["messages"],
                thread_id,
                request.resources,
                request.max_plan_iterations,
                request.max_step_num,
                request.max_search_results,
                request.auto_accepted_plan,
                request.interrupt_feedback,
                request.mcp_settings,
                request.enable_background_investigation,
                current_user.id,  # 传递用户ID用于token统计
                db,  # 传递数据库会话
            ):
                yield chunk
        except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
            logger.warning(f"StreamingResponse层检测到客户端断开连接: {e}")
            # 这里不需要再次启动后台任务，因为内层已经处理了
        except Exception as e:
            logger.error(f"StreamingResponse层捕获到未处理异常: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    return StreamingResponse(
        safe_stream_wrapper(),
        media_type="text/event-stream",
    )


async def _astream_workflow_generator(
    messages: List[ChatMessage],
    thread_id: str,
    resources: List[Resource],
    max_plan_iterations: int,
    max_step_num: int,
    max_search_results: int,
    auto_accepted_plan: bool,
    interrupt_feedback: str,
    mcp_settings: dict,
    enable_background_investigation,
    user_id: int,  # 用户ID
    db: Session,  # 数据库会话
):
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": auto_accepted_plan,
        "enable_background_investigation": enable_background_investigation,
        "user_id": user_id,
        "thread_id": thread_id,
    }
    if not auto_accepted_plan and interrupt_feedback:
        resume_msg = f"[{interrupt_feedback}]"
        # add the last message to the resume message
        if messages:
            resume_msg += f" {messages[-1]['content']}"
        input_ = Command(resume=resume_msg)
    
    node_count = 0  # 节点计数器
    client_disconnected = False  # 客户端断开标志
    task_id = str(uuid4())  # 生成任务ID
    event_count = 0  # 事件计数器，用于定期连接检查
    
    async def handle_client_disconnection():
        """处理客户端断开连接，启动后台任务"""
        nonlocal client_disconnected
        if not client_disconnected:
            client_disconnected = True
            logger.warning("客户端断开连接，启动后台任务")
            # 启动后台任务继续执行
            await background_task_manager.start_background_execution(
                task_id=task_id,
                messages=messages,
                thread_id=thread_id,
                user_id=user_id,
                resources=resources,
                max_plan_iterations=max_plan_iterations,
                max_step_num=max_step_num,
                max_search_results=max_search_results,
                auto_accepted_plan=auto_accepted_plan,
                interrupt_feedback=interrupt_feedback,
                mcp_settings=mcp_settings,
                enable_background_investigation=enable_background_investigation,
            )
            # 注意：不能在这里yield，因为这是一个async函数而不是generator
            # 如果需要发送消息，应该在调用者处理
    
    try:
        # 包装LangGraph的stream，增加异常处理
        async def safe_langgraph_stream():
            try:
                async for agent, _, event_data in graph.astream(
                    input_,
                    config={
                        "thread_id": thread_id,
                        "resources": resources,
                        "max_plan_iterations": max_plan_iterations,
                        "max_step_num": max_step_num,
                        "max_search_results": max_search_results,
                        "mcp_settings": mcp_settings,
                    },
                    stream_mode=["messages", "updates"],
                    subgraphs=True,
                ):
                    yield agent, _, event_data
            except Exception as e:
                logger.error(f"LangGraph stream 内部错误: {e}")
                # 如果LangGraph内部出错，也认为是需要后台处理的情况
                if not client_disconnected:
                    await handle_client_disconnection()
                raise e
        
        async for agent, _, event_data in safe_langgraph_stream():
            # 检查客户端是否已断开连接
            if client_disconnected:
                logger.info("检测到客户端已断开连接，停止处理")
                break
                
            # 增加事件计数器，定期发送测试事件来检测客户端连接状态
            event_count += 1
            if event_count % 10 == 0:  # 每10个事件检查一次连接
                try:
                    # 发送一个小的状态事件来测试连接
                    yield _make_event("status", {"alive": True, "count": event_count})
                except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                    logger.warning(f"定期连接检查发现客户端断开: {e}")
                    await handle_client_disconnection()
                    break
            if isinstance(event_data, dict):
                if "__interrupt__" in event_data:
                    try:
                        yield _make_event(
                            "interrupt",
                            {
                                "thread_id": thread_id,
                                "id": event_data["__interrupt__"][0].ns[0],
                                "role": "assistant",
                                "content": event_data["__interrupt__"][0].value,
                                "finish_reason": "interrupt",
                                "options": [
                                    {"text": "Edit plan", "value": "edit_plan"},
                                    {"text": "Start research", "value": "accepted"},
                                ],
                            },
                        )
                    except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                        await handle_client_disconnection()
                        break
                continue
            message_chunk, message_metadata = cast(
                tuple[BaseMessage, dict[str, any]], event_data
            )
            event_stream_message: dict[str, any] = {
                "thread_id": thread_id,
                "agent": agent[0].split(":")[0],
                "id": message_chunk.id,
                "role": "assistant",
                "content": message_chunk.content,
            }
            logger.info(f"message_chunk: {message_chunk}")
            if message_chunk.response_metadata.get("finish_reason"):
                event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                    "finish_reason"
                )
            if isinstance(message_chunk, ToolMessage):
                # Tool Message - Return the result of the tool call
                event_stream_message["tool_call_id"] = message_chunk.tool_call_id
                try:
                    yield _make_event("tool_call_result", event_stream_message)
                except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                    await handle_client_disconnection()
                    break
            elif isinstance(message_chunk, AIMessageChunk):
                # AI Message - Raw message tokens

                # 统计token使用量并检查积分（仅对完整响应）
                if message_chunk.content == '':
                    node_count += 1
                    logger.info(f"节点 {node_count}，统计token使用量")

                    # 提取token使用量
                    input_tokens, output_tokens = token_manager.extract_token_usage_from_response(message_chunk)

                    if input_tokens > 0 or output_tokens > 0:
                        # 累加到Redis
                        token_manager.add_token_usage(thread_id, input_tokens, output_tokens)

                        # 获取当前累计使用量
                        current_usage = token_manager.get_current_usage(thread_id)
                        logger.info(f"线程 {thread_id} 累计token使用量: 输入{current_usage['input_tokens']}, 输出{current_usage['output_tokens']}")

                    # 在进入下一个节点前检查积分
                    has_enough_credits, remaining_credits = token_manager.check_user_credits(user_id, db)
                    if not has_enough_credits:
                        logger.warning(f"用户 {user_id} 积分不足，停止处理: 剩余积分{remaining_credits}")

                        # 同步当前token使用量到数据库
                        token_manager.sync_tokens_to_database(thread_id, user_id, db)

                        # 发送积分不足消息
                        error_message = {
                            "thread_id": thread_id,
                            "agent": agent[0].split(":")[0] if agent else "system",
                            "id": f"error_{node_count}",
                            "role": "assistant",
                            "content": f"积分不足，无法继续处理。当前剩余积分: {remaining_credits}，需要至少 {token_manager.min_credits_threshold} 积分。",
                            "finish_reason": "credits_insufficient"
                        }
                        try:
                            yield _make_event("error", error_message)
                        except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                            await handle_client_disconnection()
                        return

                if message_chunk.tool_calls:
                    # AI Message - Tool Call
                    event_stream_message["tool_calls"] = message_chunk.tool_calls
                    event_stream_message["tool_call_chunks"] = (
                        message_chunk.tool_call_chunks
                    )
                    try:
                        yield _make_event("tool_calls", event_stream_message)
                    except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                        await handle_client_disconnection()
                        break
                elif message_chunk.tool_call_chunks:
                    # AI Message - Tool Call Chunks
                    event_stream_message["tool_call_chunks"] = (
                        message_chunk.tool_call_chunks
                    )
                    try:
                        yield _make_event("tool_call_chunks", event_stream_message)
                    except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                        await handle_client_disconnection()
                        break
                else:
                    # AI Message - Raw message tokens
                    try:
                        yield _make_event("message_chunk", event_stream_message)
                    except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
                        await handle_client_disconnection()
                        break
    except (ConnectionResetError, BrokenPipeError, GeneratorExit, ConnectionAbortedError, asyncio.CancelledError, ClientDisconnect) as e:
        await handle_client_disconnection()
    except Exception as e:
        logger.error(f"chat stream error: {e}")
        if not client_disconnected:
            # 只有在非客户端断开的情况下才启动后台任务
            await background_task_manager.start_background_execution(
                task_id=task_id,
                messages=messages,
                thread_id=thread_id,
                user_id=user_id,
                resources=resources,
                max_plan_iterations=max_plan_iterations,
                max_step_num=max_step_num,
                max_search_results=max_search_results,
                auto_accepted_plan=auto_accepted_plan,
                interrupt_feedback=interrupt_feedback,
                mcp_settings=mcp_settings,
                enable_background_investigation=enable_background_investigation,
            )
    finally:
        # 流处理结束，同步token使用量到数据库
        if not client_disconnected:
            token_manager.sync_tokens_to_database(thread_id, user_id, db)


def _make_event(event_type: str, data: dict[str, any]):
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"





@app.post("/api/tts")
async def text_to_speech(request: TTSRequest):
    """Convert text to speech using volcengine TTS API."""
    try:
        app_id = os.getenv("VOLCENGINE_TTS_APPID", "")
        if not app_id:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_APPID is not set"
            )
        access_token = os.getenv("VOLCENGINE_TTS_ACCESS_TOKEN", "")
        if not access_token:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_ACCESS_TOKEN is not set"
            )
        cluster = os.getenv("VOLCENGINE_TTS_CLUSTER", "volcano_tts")
        voice_type = os.getenv("VOLCENGINE_TTS_VOICE_TYPE", "BV700_V2_streaming")

        tts_client = VolcengineTTS(
            appid=app_id,
            access_token=access_token,
            cluster=cluster,
            voice_type=voice_type,
        )
        # Call the TTS API
        result = tts_client.text_to_speech(
            text=request.text[:1024],
            encoding=request.encoding,
            speed_ratio=request.speed_ratio,
            volume_ratio=request.volume_ratio,
            pitch_ratio=request.pitch_ratio,
            text_type=request.text_type,
            with_frontend=request.with_frontend,
            frontend_type=request.frontend_type,
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=str(result["error"]))

        # Decode the base64 audio data
        audio_data = base64.b64decode(result["audio_data"])

        # Return the audio file
        return Response(
            content=audio_data,
            media_type=f"audio/{request.encoding}",
            headers={
                "Content-Disposition": (
                    f"attachment; filename=tts_output.{request.encoding}"
                )
            },
        )
    except Exception as e:
        logger.exception(f"Error in TTS endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/podcast/generate")
async def generate_podcast(request: GeneratePodcastRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_podcast_graph()
        final_state = workflow.invoke({"input": report_content})
        audio_bytes = final_state["output"]
        return Response(content=audio_bytes, media_type="audio/mp3")
    except Exception as e:
        logger.exception(f"Error occurred during podcast generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/ppt/generate")
async def generate_ppt(request: GeneratePPTRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_ppt_graph()
        final_state = workflow.invoke({"input": report_content})
        generated_file_path = final_state["generated_file_path"]
        with open(generated_file_path, "rb") as f:
            ppt_bytes = f.read()
        return Response(
            content=ppt_bytes,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
        )
    except Exception as e:
        logger.exception(f"Error occurred during ppt generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/prose/generate")
async def generate_prose(request: GenerateProseRequest):
    try:
        logger.info(f"Generating prose for prompt: {request.prompt}")
        workflow = build_prose_graph()
        events = workflow.astream(
            {
                "content": request.prompt,
                "option": request.option,
                "command": request.command,
            },
            stream_mode="messages",
            subgraphs=True,
        )
        return StreamingResponse(
            (f"data: {event[0].content}\n\n" async for _, event in events),
            media_type="text/event-stream",
        )
    except Exception as e:
        logger.exception(f"Error occurred during prose generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/mcp/server/metadata", response_model=MCPServerMetadataResponse)
async def mcp_server_metadata(request: MCPServerMetadataRequest):
    """Get information about an MCP server."""
    try:
        # Set default timeout with a longer value for this endpoint
        timeout = 300  # Default to 300 seconds for this endpoint

        # Use custom timeout from request if provided
        if request.timeout_seconds is not None:
            timeout = request.timeout_seconds

        # Load tools from the MCP server using the utility function
        tools = await load_mcp_tools(
            server_type=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            timeout_seconds=timeout,
        )

        # Create the response with tools
        response = MCPServerMetadataResponse(
            transport=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            tools=tools,
        )

        return response
    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.exception(f"Error in MCP server metadata endpoint: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
        raise


@app.get("/api/paper/{thread_id}", response_model=FinalPaperResponse)
async def get_final_paper(thread_id: str):
    """Get the final paper document from a completed workflow thread."""
    try:
        # Get the current state of the thread
        state = graph.get_state(config={"configurable": {"thread_id": thread_id}})
        
        if not state or not state.values:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        # Check if there's a final paper in the state
        final_paper = state.values.get("final_paper", "")
        
        if not final_paper:
            raise HTTPException(status_code=404, detail="No final paper found in this thread")
        
        return FinalPaperResponse(
            thread_id=thread_id,
            final_paper=final_paper,
            paper_writing_mode=state.values.get("paper_writing_mode", False),
            status="completed"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrieving final paper for thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/rag/config", response_model=RAGConfigResponse)
async def rag_config():
    """Get the config of the RAG."""
    return RAGConfigResponse(provider=SELECTED_RAG_PROVIDER)


@app.get("/api/rag/resources", response_model=RAGResourcesResponse)
async def rag_resources(request: Annotated[RAGResourceRequest, Query()]):
    """Get the resources of the RAG."""
    retriever = build_retriever()
    if retriever:
        return RAGResourcesResponse(resources=retriever.list_resources(request.query))
    return RAGResourcesResponse(resources=[])


@app.post("/api/rag/datasets", response_model=RAGCreateDatasetResponse)
async def create_rag_dataset(request: RAGCreateDatasetRequest):
    """Create a new dataset in RAGFlow."""
    try:
        retriever = build_retriever()
        if not retriever or not hasattr(retriever, 'create_dataset'):
            raise HTTPException(status_code=503, detail="RAGFlow provider not available or doesn't support dataset creation")
        
        # Prepare kwargs for additional parameters
        kwargs = {}
        if request.chunk_method:
            kwargs['chunk_method'] = request.chunk_method
        if request.embedding_model:
            kwargs['embedding_model'] = request.embedding_model
        
        result = retriever.create_dataset(
            name=request.name,
            description=request.description,
            **kwargs
        )
        
        # Handle RAGFlow response format: {"code": 0, "data": {...}}
        if result.get("code") == 0:
            dataset_data = result.get("data", {})
        else:
            dataset_data = result.get("data", {})
        
        return RAGCreateDatasetResponse(
            success=True,
            message="Dataset created successfully",
            dataset=dataset_data
        )
    except Exception as e:
        logger.exception(f"Error creating dataset: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/rag/upload", response_model=RAGUploadResponse)
async def upload_documents_to_rag(
    file: UploadFile = File(...), 
    dataset_id: str = Form(""),
    auto_parse: bool = Form(True)
):
    """Upload documents to a RAGFlow dataset with optional auto-parsing."""
    try:
        retriever = build_retriever()
        if not retriever or not hasattr(retriever, 'upload_documents'):
            raise HTTPException(status_code=503, detail="RAGFlow provider not available or doesn't support document upload")
        
        result = await retriever.upload_documents(
            dataset_id=dataset_id,
            files=[file],
            auto_parse=auto_parse
        )
        
        # Handle RAGFlow response format: {"code": 0, "data": [...]}
        if result.get("code") == 0:
            documents_data = result.get("data", [])
        else:
            documents_data = result.get("data", [])
        
        # Ensure data is in the expected format
        if isinstance(documents_data, list):
            data = {"documents": documents_data}
        else:
            data = documents_data
            
        # Add parsing information to response if available
        if result.get("parse_started") is not None:
            data["parse_started"] = result.get("parse_started")
            if result.get("parsed_document_ids"):
                data["parsed_document_ids"] = result.get("parsed_document_ids")
            if result.get("parse_error"):
                data["parse_error"] = result.get("parse_error")
        
        return RAGUploadResponse(
            success=True,
            message="Documents uploaded successfully" + (" and parsing started" if result.get("parse_started") else ""),
            data=data
        )
    except Exception as e:
        logger.exception(f"Error uploading documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/rag/datasets/{dataset_id}/documents", response_model=RAGDocumentListResponse)
async def list_rag_documents(dataset_id: str, keywords: str = None, page: int = 1, page_size: int = 30):
    """List documents in a RAGFlow dataset."""
    try:
        retriever = build_retriever()
        if not retriever or not hasattr(retriever, 'list_documents'):
            raise HTTPException(status_code=503, detail="RAGFlow provider not available or doesn't support document listing")
        
        kwargs = {"page": page, "page_size": page_size}
        if keywords:
            kwargs["keywords"] = keywords
        
        documents = retriever.list_documents(dataset_id, **kwargs)
        
        return RAGDocumentListResponse(documents=documents)
    except Exception as e:
        logger.exception(f"Error listing documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/rag/documents", response_model=RAGDocumentDeleteResponse)
async def delete_rag_documents(request: RAGDocumentDeleteRequest):
    """Delete documents from a RAGFlow dataset."""
    try:
        retriever = build_retriever()
        if not retriever or not hasattr(retriever, 'delete_documents'):
            raise HTTPException(status_code=503, detail="RAGFlow provider not available or doesn't support document deletion")
        
        result = retriever.delete_documents(
            dataset_id=request.dataset_id,
            document_ids=request.document_ids
        )
        
        return RAGDocumentDeleteResponse(
            success=True,
            message="Documents deleted successfully"
        )
    except Exception as e:
        logger.exception(f"Error deleting documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/rag/documents/parse", response_model=RAGDocumentParseResponse)
async def parse_rag_documents(request: RAGDocumentParseRequest):
    """Start parsing documents in a RAGFlow dataset."""
    try:
        retriever = build_retriever()
        if not retriever or not hasattr(retriever, 'parse_documents'):
            raise HTTPException(status_code=503, detail="RAGFlow provider not available or doesn't support document parsing")
        
        result = retriever.parse_documents(
            dataset_id=request.dataset_id,
            document_ids=request.document_ids
        )
        
        return RAGDocumentParseResponse(
            success=True,
            message="Document parsing started successfully"
        )
    except Exception as e:
        logger.exception(f"Error starting document parsing: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/token/usage/{thread_id}")
async def get_token_usage(
    thread_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定线程的token使用量统计"""
    
    # 获取Redis中的实时使用量
    redis_usage = token_manager.get_current_usage(thread_id)
    
    # 获取数据库中的历史使用量
    auth_service = AuthService(db)
    chat_history = db.query(ChatHistory).filter(
        ChatHistory.thread_id == thread_id,
        ChatHistory.user_id == current_user.id
    ).first()
    
    db_usage = {
        "input_tokens": 0,
        "output_tokens": 0
    }
    
    if chat_history:
        db_usage = {
            "input_tokens": chat_history.total_input_tokens,
            "output_tokens": chat_history.total_output_tokens
        }
    
    # 计算总使用量
    total_usage = {
        "input_tokens": redis_usage["input_tokens"] + db_usage["input_tokens"],
        "output_tokens": redis_usage["output_tokens"] + db_usage["output_tokens"]
    }
    
    # 计算积分消耗
    total_tokens = total_usage["input_tokens"] + total_usage["output_tokens"]
    credits_used = token_manager.get_token_cost_in_credits(
        total_usage["input_tokens"], 
        total_usage["output_tokens"]
    )
    
    return {
        "thread_id": thread_id,
        "current_session": redis_usage,  # 当前会话使用量
        "historical": db_usage,  # 历史使用量
        "total": total_usage,  # 总使用量
        "total_tokens": total_tokens,
        "credits_used": credits_used,
        "user_remaining_credits": current_user.remaining_credits
    }


@app.get("/api/user/credits")
async def get_user_credits(
    current_user: User = Depends(get_current_user)
):
    """获取用户积分信息"""
    return {
        "user_id": current_user.id,
        "total_credits": current_user.total_credits,
        "remaining_credits": current_user.remaining_credits,
        "used_credits": current_user.total_credits - current_user.remaining_credits,
        "tokens_per_credit": token_manager.tokens_per_credit,
        "min_credits_threshold": token_manager.min_credits_threshold
    }


@app.get("/api/tasks/{task_id}")
async def get_task_status(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取任务执行状态"""
    task_status = background_task_manager.get_task_status(task_id)
    
    if not task_status:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    # 验证任务是否属于当前用户
    from src.database.models import TaskExecution
    db = SessionLocal()
    try:
        task_execution = db.query(TaskExecution).filter(
            TaskExecution.task_id == task_id,
            TaskExecution.user_id == current_user.id
        ).first()
        if not task_execution:
            raise HTTPException(
                status_code=403,
                detail="无权访问此任务"
            )
    finally:
        db.close()
    
    return {
        "success": True,
        "task": task_status
    }


@app.get("/api/tasks")
async def get_user_tasks(
    limit: int = 20,
    current_user: User = Depends(get_current_user)
):
    """获取用户的所有任务列表"""
    tasks = background_task_manager.get_user_tasks(current_user.id, limit)
    
    return {
        "success": True,
        "tasks": tasks,
        "total": len(tasks)
    }


@app.post("/api/chat/background")
async def start_background_chat(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """直接启动后台聊天任务（不需要流式连接）"""
    thread_id = request.thread_id
    if thread_id == "__default__":
        thread_id = str(uuid4())
    
    # 创建或获取对话历史记录
    auth_service = AuthService(db)
    
    # 从第一条消息的内容中提取标题（前30个字符）
    title = ""
    if request.messages and len(request.messages) > 0:
        first_message_content = request.messages[0].content
        title = first_message_content[:30] if len(first_message_content) > 30 else first_message_content
    
    # 创建或获取对话历史记录
    chat_history = auth_service.create_or_get_chat_history(
        user_id=current_user.id,
        thread_id=thread_id,
        title=title
    )
    
    # 检查用户积分
    has_enough_credits, remaining_credits = token_manager.check_user_credits(current_user.id, db)
    if not has_enough_credits:
        raise HTTPException(
            status_code=403,
            detail=f"积分不足，当前剩余积分: {remaining_credits}，需要至少 {token_manager.min_credits_threshold} 积分"
        )
    
    # 生成任务ID
    task_id = str(uuid4())
    
    # 启动后台任务
    success = await background_task_manager.start_background_execution(
        task_id=task_id,
        messages=request.model_dump()["messages"],
        thread_id=thread_id,
        user_id=current_user.id,
        resources=request.resources,
        max_plan_iterations=request.max_plan_iterations,
        max_step_num=request.max_step_num,
        max_search_results=request.max_search_results,
        auto_accepted_plan=request.auto_accepted_plan,
        interrupt_feedback=request.interrupt_feedback,
        mcp_settings=request.mcp_settings,
        enable_background_investigation=request.enable_background_investigation,
    )
    
    if not success:
        raise HTTPException(
            status_code=500,
            detail="启动后台任务失败"
        )
    
    return {
        "success": True,
        "task_id": task_id,
        "thread_id": thread_id,
        "message": "后台任务已启动",
        "status": "pending"
    }
