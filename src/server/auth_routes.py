# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from src.database import get_db, User
from src.auth.models import (
    UserLoginRequest,
    GoogleLoginRequest,
    EmailVerificationRequest,
    UserResponse,
    LoginResponse,
    SendVerificationResponse,
    TokenUsageRequest,
    ChatHistoryResponse,
    ChatThreadListResponse,
    ChatThreadItem, ChatHistoryDetailResponse, ResultDocument
)
from src.auth.service import AuthService
from src.auth.middleware import get_current_user
from src.database.models import ResultDocument as Document

router = APIRouter(tags=["User"])

@router.post("/auth/send-verification", response_model=SendVerificationResponse)
async def send_verification_code(
    request: EmailVerificationRequest,
    db: Session = Depends(get_db)
):
    """
    发送邮箱验证码
    """
    auth_service = AuthService(db)
    
    success = auth_service.send_verification_code(request.email)
    
    if success:
        return SendVerificationResponse(
            success=True,
            message="验证码已发送到您的邮箱",
            expires_in=300
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证码发送失败，请稍后重试"
        )

@router.post("/auth/login/email", response_model=LoginResponse)
async def login_with_email(
    request: UserLoginRequest,
    db: Session = Depends(get_db)
):
    """
    邮箱验证码登录
    """
    auth_service = AuthService(db)
    
    access_token = auth_service.login_with_email(
        request.email, 
        request.verification_code
    )
    
    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="验证码无效或已过期"
        )
    
    user = auth_service.get_user_by_email(request.email)
    
    return LoginResponse(
        access_token=access_token,
        user=UserResponse.from_orm(user)
    )

@router.post("/auth/login/google", response_model=LoginResponse)
async def login_with_google(
    request: GoogleLoginRequest,
    db: Session = Depends(get_db)
):
    """
    Google登录
    """
    auth_service = AuthService(db)
    
    access_token = auth_service.login_with_google(request.google_token)
    
    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Google token验证失败"
        )
    
    # 从token中获取用户信息
    from src.auth.utils import verify_google_token
    google_user_info = verify_google_token(request.google_token)
    email = google_user_info.get('email')
    
    user = auth_service.get_user_by_email(email)
    
    return LoginResponse(
        access_token=access_token,
        user=UserResponse.from_orm(user)
    )

@router.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息
    """
    return UserResponse.from_orm(current_user)

@router.post("/auth/token-usage")
async def record_token_usage(
    request: TokenUsageRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    记录token使用情况
    """
    auth_service = AuthService(db)
    
    chat_history = auth_service.record_token_usage(current_user.id, request)
    
    return {
        "success": True,
        "message": "Token使用情况已记录",
        "chat_history_id": chat_history.id,
        "total_tokens": chat_history.total_tokens
    }

@router.get("/auth/chat-history", response_model=List[ChatHistoryResponse])
async def get_chat_history(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户对话历史
    """
    auth_service = AuthService(db)
    
    chat_histories = auth_service.get_user_chat_history(
        current_user.id, 
        limit=limit, 
        offset=offset
    )
    
    return [ChatHistoryResponse.from_orm(chat) for chat in chat_histories]

@router.get("/auth/chat-history/{thread_id}", response_model=ChatHistoryDetailResponse)
async def get_chat_history_by_thread(
    thread_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    根据thread_id获取对话历史
    """
    auth_service = AuthService(db)
    
    chat_history = auth_service.get_chat_history_by_thread(current_user.id, thread_id)
    
    if not chat_history:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="对话历史未找到"
        )
    
    # 通过thread_id获取最新的ResultDocument
    result_document = db.query(Document).filter(Document.thread_id == thread_id).order_by(Document.created_at.desc()).first()
    if result_document:
        result_document = ResultDocument(
            id=result_document.id,
            document_id=result_document.document_id,
            thread_id=result_document.thread_id,
            url=result_document.url,
            created_at=result_document.created_at,
            updated_at=result_document.updated_at
        )
    
    # res = ChatHistoryDetailResponse.from_orm(chat_history)
    # res.result_document = result_document
    res = ChatHistoryDetailResponse(id=chat_history.id, thread_id=chat_history.thread_id, title=chat_history.title,
        non_reasoning_input_tokens=chat_history.non_reasoning_input_tokens, non_reasoning_output_tokens=chat_history.non_reasoning_output_tokens,
        reasoning_input_tokens=chat_history.reasoning_input_tokens, reasoning_output_tokens=chat_history.reasoning_output_tokens,
        conversation_summary=chat_history.conversation_summary, messages=chat_history.messages, created_at=chat_history.created_at, updated_at=chat_history.updated_at,
        result_document=result_document
    )
    return res

@router.get("/auth/chat-threads", response_model=ChatThreadListResponse)
async def get_chat_threads(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户对话线程列表
    """
    auth_service = AuthService(db)
    
    threads, total = auth_service.get_user_chat_threads(
        current_user.id, 
        limit=limit, 
        offset=offset
    )
    
    thread_items = [
        ChatThreadItem(
            thread_id=thread.thread_id,
            title=thread.title,
            created_at=thread.created_at,
            updated_at=thread.updated_at
        ) for thread in threads
    ]
    
    return ChatThreadListResponse(
        threads=thread_items,
        total=total
    )

@router.post("/auth/credits/add")
async def add_credits(
    credits: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    增加用户积分（管理员功能）
    """
    # 这里可以添加管理员权限检查
    auth_service = AuthService(db)
    
    updated_user = auth_service.update_user_credits(current_user.id, credits)
    
    return {
        "success": True,
        "message": f"已增加 {credits} 积分",
        "user": UserResponse.from_orm(updated_user)
    } 