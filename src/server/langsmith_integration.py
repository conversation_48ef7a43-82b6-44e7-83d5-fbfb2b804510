# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT
import json
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

try:
    from langsmith import Client
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    Client = None

logger = logging.getLogger(__name__)


@dataclass
class TokenUsage:
    """Token usage information"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_usd: Optional[float] = None


@dataclass
class LangSmithRun:
    """LangSmith run information"""
    id: str
    name: str
    run_type: str
    start_time: datetime
    end_time: Optional[datetime]
    status: str
    inputs: Dict[str, Any]
    outputs: Optional[Dict[str, Any]]
    error: Optional[str]
    token_usage: Optional[TokenUsage]
    trace_id: str
    parent_run_id: Optional[str]
    session_id: Optional[str]
    tags: List[str]
    metadata: Dict[str, Any]


@dataclass
class TaskTrace:
    """Complete task trace information"""
    trace_id: str
    thread_id: str
    session_name: Optional[str]
    start_time: datetime
    end_time: Optional[datetime]
    status: str
    total_runs: int
    runs: List[LangSmithRun]
    total_tokens: TokenUsage
    total_cost_usd: Optional[float]
    tags: List[str]


class LangSmithIntegration:
    """Integration with LangSmith for querying task execution data"""
    
    def __init__(self):
        self.client = None
        self.project_name = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize LangSmith client if available and configured"""
        if not LANGSMITH_AVAILABLE:
            logger.warning("LangSmith package not available. Install with: pip install langsmith")
            return
        
        api_key = os.getenv("LANGSMITH_API_KEY")
        endpoint = os.getenv("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com")
        self.project_name = os.getenv("LANGSMITH_PROJECT")
        
        if not api_key:
            logger.warning("LANGSMITH_API_KEY not configured. LangSmith integration disabled.")
            return
        
        if not self.project_name:
            logger.warning("LANGSMITH_PROJECT not configured. LangSmith integration disabled.")
            return
        
        try:
            self.client = Client(api_url=endpoint, api_key=api_key)
            logger.info(f"LangSmith client initialized for project: {self.project_name}")
        except Exception as e:
            logger.error(f"Failed to initialize LangSmith client: {e}")
    
    def is_available(self) -> bool:
        """Check if LangSmith integration is available and configured"""
        return self.client is not None
    
    def _extract_token_usage(self, run_data: Dict) -> Optional[TokenUsage]:
        """Extract token usage from run data"""
        # Try different possible token usage locations in LangSmith data
        usage_data = None
        
        # Check if it's a run object with usage_metadata attribute
        if hasattr(run_data, 'usage_metadata') and run_data.usage_metadata:
            usage_data = run_data.usage_metadata
        # Check if it's a dict with usage_metadata
        elif isinstance(run_data, dict) and run_data.get("usage_metadata"):
            usage_data = run_data["usage_metadata"]
        # Check for other common token usage fields
        elif isinstance(run_data, dict):
            if "token_usage" in run_data:
                usage_data = run_data["token_usage"]
            elif "usage" in run_data:
                usage_data = run_data["usage"]
        
        # Check in outputs.generations[0][0].message.kwargs.usage_metadata for LLM runs
        if not usage_data:
            try:
                outputs = getattr(run_data, 'outputs', None) or (run_data.get('outputs') if isinstance(run_data, dict) else None)
                if outputs and isinstance(outputs, dict):
                    generations = outputs.get('generations', [])
                    if generations and len(generations) > 0 and len(generations[0]) > 0:
                        message = generations[0][0].get('message', {})
                        if isinstance(message, dict) and 'kwargs' in message:
                            usage_data = message['kwargs'].get('usage_metadata')
            except (AttributeError, KeyError, IndexError, TypeError):
                pass
        
        if not usage_data:
            return None
        
        # Handle different usage data formats
        if isinstance(usage_data, dict):
            return TokenUsage(
                prompt_tokens=usage_data.get("input_tokens", usage_data.get("prompt_tokens", 0)),
                completion_tokens=usage_data.get("output_tokens", usage_data.get("completion_tokens", 0)),
                total_tokens=usage_data.get("total_tokens", 0),
                cost_usd=usage_data.get("cost", usage_data.get("total_cost", None))
            )
        
        return None

    # 解析langchain返回的消息
    def _parse_langchain_messages(self, messages_data):
        """Parse LangChain message format to simple text"""
        if not messages_data:
            return ""
        
        try:
            if isinstance(messages_data, list) and len(messages_data) > 0:
                # Handle nested message arrays
                if isinstance(messages_data[0], list):
                    messages_data = messages_data[0]
                
                parsed_messages = []
                for msg in messages_data:
                    # print("msg: ", msg)
                    # 拼接的message类型: {"agent": "paper_writer", "id": "run-8131709f-2b8e-4097-a0ae-bd731a730c34", "content": "和智能终端的普及，电子商务已成为现代商业活动中不可或缺的重要"}
                    if isinstance(msg, dict) and 'kwargs' in msg:
                        content = msg['kwargs'].get('content', '')
                        msg_type = msg['kwargs'].get('type', 'unknown')
                        agent_name = msg['kwargs'].get('name', 'ThinkWriter')
                        if msg_type == "ai" or agent_name == "thinking" or agent_name == "researcher":
                            msg_type = "assistant"
                        elif msg_type == "human":
                            msg_type = "user"
                        parsed_messages.append({"agent": agent_name, "type": msg_type, "id": msg['kwargs'].get('id', ''), "content": content})
                    elif isinstance(msg, dict) and 'content' in msg:
                        content = msg.get('content', '')
                        msg_type = msg.get('type', 'unknown')
                        agent_name = msg.get('name', 'ThinkWriter')
                        if msg_type == "ai" or agent_name == "thinking" or agent_name == "researcher":
                            msg_type = "assistant"
                        elif msg_type == "human":
                            msg_type = "user"
                        parsed_messages.append({"agent": agent_name, "type": msg_type, "id": msg.get('id', ''), "content": content})
                
                return parsed_messages
        except (TypeError, KeyError, AttributeError):
            pass
        
        return str(messages_data) if messages_data else ""
    
    def _parse_langchain_outputs(self, outputs_data):
        """Parse LangChain outputs format to simple text"""
        if not outputs_data:
            return ""
        
        try:
            if isinstance(outputs_data, dict):
                generations = outputs_data.get('generations', [])
                if generations and len(generations) > 0:
                    first_gen = generations[0]
                    if isinstance(first_gen, list) and len(first_gen) > 0:
                        gen_data = first_gen[0]
                        if isinstance(gen_data, dict):
                            # Try to get text content
                            text = gen_data.get('text')
                            if text:
                                return text
                            
                            # Try to get message content
                            message = gen_data.get('message', {})
                            if isinstance(message, dict) and 'kwargs' in message:
                                return message['kwargs'].get('content', '')
        except (TypeError, KeyError, AttributeError, IndexError):
            pass
        
        return str(outputs_data) if outputs_data else ""
    
    def _convert_langsmith_run(self, run_data) -> LangSmithRun:
        """Convert LangSmith run data to our format"""
        # Handle both dict and object formats
        if hasattr(run_data, '__dict__'):
            data = run_data.__dict__
            run_obj = run_data
        else:
            data = run_data
            run_obj = type('obj', (object,), data)()
        
        # Safe attribute access with UUID conversion
        def safe_get(obj, attr, default=None):
            if hasattr(obj, attr):
                value = getattr(obj, attr)
                # Convert UUID to string
                if hasattr(value, 'hex'):  # UUID objects have hex attribute
                    return str(value)
                return value
            elif isinstance(data, dict):
                value = data.get(attr, default)
                # Convert UUID to string
                if hasattr(value, 'hex'):  # UUID objects have hex attribute
                    return str(value)
                return value
            return default
        
        # Parse inputs and outputs
        raw_inputs = safe_get(run_obj, "inputs", {})
        raw_outputs = safe_get(run_obj, "outputs")
        
        # Parse LangChain format inputs
        parsed_inputs = {}
        if isinstance(raw_inputs, dict):
            for key, value in raw_inputs.items():
                if key == "messages" and isinstance(value, list):
                    parsed_inputs[key] = self._parse_langchain_messages(value)
                else:
                    parsed_inputs[key] = value
        else:
            parsed_inputs = raw_inputs
        
        # Parse LangChain format outputs
        parsed_outputs = {}
        if raw_outputs:
            if isinstance(raw_outputs, dict) and 'generations' in raw_outputs:
                parsed_outputs = {"content": self._parse_langchain_outputs(raw_outputs)}
            elif isinstance(raw_outputs, dict):
                for key, value in raw_outputs.items():
                    if key == "messages" and isinstance(value, list):
                        parsed_outputs[key] = self._parse_langchain_messages(value)
                    else:
                        parsed_outputs[key] = value
            else:
                parsed_outputs = raw_outputs
        
        # TODO 待测试验证
        # if len(parsed_outputs["messages"]) < len(parsed_inputs["messages"]):
        #     parsed_outputs["messages"] = parsed_inputs["messages"]
        
        return LangSmithRun(
            id=safe_get(run_obj, "id", ""),
            name=safe_get(run_obj, "name", ""),
            run_type=safe_get(run_obj, "run_type", ""),
            start_time=self._parse_datetime(safe_get(run_obj, "start_time")),
            end_time=self._parse_datetime(safe_get(run_obj, "end_time")) if safe_get(run_obj, "end_time") else None,
            status=safe_get(run_obj, "status", ""),
            inputs=parsed_inputs,
            outputs=parsed_outputs,
            error=safe_get(run_obj, "error"),
            token_usage=self._extract_token_usage(run_obj),
            trace_id=safe_get(run_obj, "trace_id", safe_get(run_obj, "session_id", "")),
            parent_run_id=safe_get(run_obj, "parent_run_id"),
            session_id=safe_get(run_obj, "session_id"),
            tags=safe_get(run_obj, "tags", []),
            metadata=safe_get(run_obj, "extra", safe_get(run_obj, "metadata", {}))
        )
    
    def _parse_datetime(self, dt_str):
        """Parse datetime string"""
        if not dt_str:
            return datetime.now()
        
        if isinstance(dt_str, datetime):
            return dt_str
        
        try:
            # Remove Z and add timezone if needed
            if isinstance(dt_str, str):
                if dt_str.endswith('Z'):
                    dt_str = dt_str[:-1] + '+00:00'
                return datetime.fromisoformat(dt_str)
        except:
            pass
        
        return datetime.now()
    
    async def get_task_traces(
        self, 
        thread_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[TaskTrace]:
        """Get task traces from LangSmith"""
        if not self.is_available():
            logger.warning("LangSmith not available")
            return []
        
        try:
            # Build query parameters
            query_params = {
                "project_name": self.project_name,
                "limit": min(limit, 50)
            }
            
            # Add thread_id filter if specified
            if thread_id:
                filter_string = f'and(in(metadata_key, ["session_id","conversation_id","thread_id"]), eq(metadata_value, "{thread_id}"))'
                query_params["filter"] = filter_string
                logger.info(f"Querying with thread_id filter: {filter_string}")
            else:
                # For general query, get root runs
                query_params["is_root"] = True
            
            # Add time filters if specified
            if start_time:
                query_params["start_time"] = start_time
            if end_time:
                query_params["end_time"] = end_time
            
            # Query LangSmith
            runs_data = list(self.client.list_runs(**query_params))
            
            if not runs_data:
                logger.info(f"No runs found in LangSmith with filters: {query_params}")
                return []
            
            logger.info(f"Found {len(runs_data)} runs")
            logger.info(f"runs: {runs_data[0]}")

            # Group runs by session/trace
            traces_by_session = {}
            
            for run in runs_data:
                session_id = getattr(run, 'session_id', None) or str(getattr(run, 'id', ''))
                # Convert UUID to string if needed
                if hasattr(session_id, 'hex'):
                    session_id = str(session_id)
                
                if session_id not in traces_by_session:
                    traces_by_session[session_id] = []
                traces_by_session[session_id].append(run)
            
            task_traces = []
            
            for session_id, session_runs in list(traces_by_session.items())[:limit]:
                try:
                    # Convert runs to our format
                    converted_runs = [self._convert_langsmith_run(run) for run in session_runs]
                    
                    if not converted_runs:
                        logger.info(f"No valid converted runs for session {session_id}")
                        continue
                    
                    # Additional validation: ensure we have real data
                    if len(converted_runs) == 0 or not any(run.name for run in converted_runs):
                        logger.info(f"No meaningful run data for session {session_id}")
                        continue
                    
                    # Calculate total token usage
                    total_prompt_tokens = 0
                    total_completion_tokens = 0
                    total_cost = 0.0
                    
                    for run in converted_runs:
                        if run.token_usage:
                            total_prompt_tokens += run.token_usage.prompt_tokens
                            total_completion_tokens += run.token_usage.completion_tokens
                            if run.token_usage.cost_usd:
                                total_cost += run.token_usage.cost_usd
                    
                    total_tokens = TokenUsage(
                        prompt_tokens=total_prompt_tokens,
                        completion_tokens=total_completion_tokens,
                        total_tokens=total_prompt_tokens + total_completion_tokens,
                        cost_usd=total_cost if total_cost > 0 else None
                    )
                    
                    # Get session info from first run
                    first_run = converted_runs[0]
                    
                    # Extract thread_id from metadata (prioritize the queried thread_id)
                    session_thread_id = (
                        thread_id or
                        first_run.metadata.get('thread_id') or 
                        first_run.metadata.get('conversation_id') or
                        first_run.metadata.get('session_id') or
                        session_id
                    )
                    
                    task_trace = TaskTrace(
                        trace_id=session_id,
                        thread_id=session_thread_id,
                        session_name=first_run.name or f"Session {session_id[:8]}",
                        start_time=first_run.start_time,
                        end_time=max((run.end_time for run in converted_runs if run.end_time), default=None),
                        status="completed" if all(run.status in ["success", "completed"] for run in converted_runs) else "error",
                        total_runs=len(converted_runs),
                        runs=converted_runs,
                        total_tokens=total_tokens,
                        total_cost_usd=total_cost if total_cost > 0 else None,
                        tags=list(set(tag for run in converted_runs for tag in run.tags))
                    )
                    
                    task_traces.append(task_trace)
                    
                except Exception as e:
                    logger.warning(f"Error processing session {session_id}: {e}")
                    continue
            
            # Sort by start time (newest first)
            task_traces.sort(key=lambda x: x.start_time, reverse=True)
            
            logger.info(f"Found {len(task_traces)} task traces")
            return task_traces[:limit]
            
        except Exception as e:
            logger.error(f"Error querying LangSmith traces: {e}")
            return []
    
    async def get_task_trace_by_thread_id(self, thread_id: str) -> Optional[TaskTrace]:
        """Get a specific task trace by thread_id"""
        if not thread_id:
            return None
        
        logger.info(f"Getting task trace for thread_id: {thread_id}")
        traces = await self.get_task_traces(thread_id=thread_id, limit=1)
        
        if traces:
            logger.info(f"Found trace for thread_id {thread_id}: {traces[0].trace_id}")
            return traces[0]
        else:
            logger.warning(f"No trace found for thread_id: {thread_id}")
            return None
    
    async def get_recent_task_traces(self, hours: int = 24, limit: int = 50) -> List[TaskTrace]:
        """Get recent task traces"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        return await self.get_task_traces(
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
    
    async def get_token_usage_summary(
        self, 
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get token usage summary for a time period"""
        if not start_time:
            start_time = datetime.now() - timedelta(days=7)  # Last 7 days
        
        if not end_time:
            end_time = datetime.now()
        
        traces = await self.get_task_traces(
            start_time=start_time,
            end_time=end_time,
            limit=1000
        )
        
        # Aggregate statistics
        total_traces = len(traces)
        total_prompt_tokens = 0
        total_completion_tokens = 0
        total_cost = 0.0
        
        completed_traces = 0
        failed_traces = 0
        
        model_usage = {}  # Track usage by model
        daily_usage = {}  # Track usage by day
        
        for trace in traces:
            # Count by status
            if trace.status == "completed":
                completed_traces += 1
            elif trace.status == "error":
                failed_traces += 1
            
            # Sum tokens and costs
            total_prompt_tokens += trace.total_tokens.prompt_tokens
            total_completion_tokens += trace.total_tokens.completion_tokens
            if trace.total_cost_usd:
                total_cost += trace.total_cost_usd
            
            # Track daily usage
            day_key = trace.start_time.date().isoformat()
            if day_key not in daily_usage:
                daily_usage[day_key] = {
                    "traces": 0,
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "cost_usd": 0.0
                }
            
            daily_usage[day_key]["traces"] += 1
            daily_usage[day_key]["prompt_tokens"] += trace.total_tokens.prompt_tokens
            daily_usage[day_key]["completion_tokens"] += trace.total_tokens.completion_tokens
            if trace.total_cost_usd:
                daily_usage[day_key]["cost_usd"] += trace.total_cost_usd
            
            # Track model usage from runs
            for run in trace.runs:
                if run.run_type == "llm" and run.token_usage:
                    model_name = run.metadata.get("ls_model_name", run.metadata.get("model_name", "unknown"))
                    if model_name not in model_usage:
                        model_usage[model_name] = {
                            "calls": 0,
                            "prompt_tokens": 0,
                            "completion_tokens": 0,
                            "cost_usd": 0.0
                        }
                    
                    model_usage[model_name]["calls"] += 1
                    model_usage[model_name]["prompt_tokens"] += run.token_usage.prompt_tokens
                    model_usage[model_name]["completion_tokens"] += run.token_usage.completion_tokens
                    if run.token_usage.cost_usd:
                        model_usage[model_name]["cost_usd"] += run.token_usage.cost_usd
        
        return {
            "period": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_hours": (end_time - start_time).total_seconds() / 3600
            },
            "summary": {
                "total_traces": total_traces,
                "completed_traces": completed_traces,
                "failed_traces": failed_traces,
                "success_rate": completed_traces / total_traces if total_traces > 0 else 0,
                "total_prompt_tokens": total_prompt_tokens,
                "total_completion_tokens": total_completion_tokens,
                "total_tokens": total_prompt_tokens + total_completion_tokens,
                "total_cost_usd": total_cost if total_cost > 0 else None,
                "avg_tokens_per_trace": (total_prompt_tokens + total_completion_tokens) / total_traces if total_traces > 0 else 0
            },
            "daily_usage": daily_usage,
            "model_usage": model_usage
        }
    
    async def get_trace_details(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific trace"""
        if not self.is_available():
            return None
        
        try:
            # Get all runs for this session/trace
            runs_data = list(self.client.list_runs(
                project_name=self.project_name,
                session_id=trace_id
            ))
            
            if not runs_data:
                return None
            
            # Convert runs to our format
            converted_runs = [self._convert_langsmith_run(run) for run in runs_data]
            
            # Build hierarchical structure (simplified)
            runs_dict = {}
            for run in converted_runs:
                runs_dict[run.id] = {
                    "id": run.id,
                    "name": run.name,
                    "run_type": run.run_type,
                    "start_time": run.start_time.isoformat(),
                    "end_time": run.end_time.isoformat() if run.end_time else None,
                    "status": run.status,
                    "inputs": run.inputs,
                    "outputs": run.outputs,
                    "error": run.error,
                    "token_usage": {
                        "prompt_tokens": run.token_usage.prompt_tokens,
                        "completion_tokens": run.token_usage.completion_tokens,
                        "total_tokens": run.token_usage.total_tokens,
                        "cost_usd": run.token_usage.cost_usd
                    } if run.token_usage else None,
                    "metadata": run.metadata
                }
            
            first_run = converted_runs[0]
            
            return {
                "trace_id": trace_id,
                "session_name": first_run.name or f"Session {trace_id[:8]}",
                "start_time": first_run.start_time.isoformat(),
                "end_time": max((run.end_time for run in converted_runs if run.end_time), default=datetime.now()).isoformat(),
                "total_runs": len(runs_data),
                "runs": list(runs_dict.values()),
                "metadata": first_run.metadata
            }
            
        except Exception as e:
            logger.error(f"Error getting trace details for {trace_id}: {e}")
            return None


# Global instance
langsmith_integration = LangSmithIntegration() 