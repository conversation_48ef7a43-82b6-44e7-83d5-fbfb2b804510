# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from .langsmith_integration import langsmith_integration, TaskTrace, TokenUsage
from src.database import get_db
from ..database.models import ResultDocument as Document


logger = logging.getLogger(__name__)

# Create router for LangSmith endpoints
router = APIRouter(prefix="/api/langsmith", tags=["LangSmith"])


class TaskTraceResponse(BaseModel):
    """Response model for task trace"""
    trace_id: str
    thread_id: str
    session_name: Optional[str]
    start_time: str
    end_time: Optional[str]
    status: str
    total_runs: int
    total_tokens: Dict[str, Any]
    total_cost_usd: Optional[float]
    tags: List[str]


class TokenUsageResponse(BaseModel):
    """Response model for token usage"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_usd: Optional[float]


class TokenUsageSummaryResponse(BaseModel):
    """Response model for token usage summary"""
    period: Dict[str, Any]
    summary: Dict[str, Any]
    daily_usage: Dict[str, Dict[str, Any]]
    model_usage: Dict[str, Dict[str, Any]]


class TaskRunResponse(BaseModel):
    """Response model for task run details"""
    id: str
    name: str
    run_type: str
    start_time: str
    end_time: Optional[str]
    status: str
    inputs: Dict[str, Any]
    outputs: Optional[Dict[str, Any]]
    token_usage: Optional[TokenUsageResponse]
    error: Optional[str]
    metadata: Dict[str, Any]

class ResultDocument(BaseModel):
    id: int
    document_id: str
    thread_id: str
    url: str
    created_at: datetime
    updated_at: datetime

class TaskDetailResponse(BaseModel):
    """Response model for detailed task information"""
    trace_id: str
    thread_id: str
    session_name: Optional[str]
    start_time: str
    end_time: Optional[str]
    status: str
    total_runs: int
    runs: List[TaskRunResponse]
    total_tokens: TokenUsageResponse
    total_cost_usd: Optional[float]
    tags: List[str]
    result_document: Optional[ResultDocument]


class TaskDetailResponseV2(BaseModel):
    """Response model for detailed task information"""
    messages: List[Dict[str, Any]]
    result_document: Optional[ResultDocument]


def _convert_task_trace_to_response(trace: TaskTrace) -> TaskTraceResponse:
    """Convert TaskTrace to response model"""
    return TaskTraceResponse(
        trace_id=trace.trace_id,
        thread_id=trace.thread_id,
        session_name=trace.session_name,
        start_time=trace.start_time.isoformat(),
        end_time=trace.end_time.isoformat() if trace.end_time else None,
        status=trace.status,
        total_runs=trace.total_runs,
        total_tokens={
            "prompt_tokens": trace.total_tokens.prompt_tokens,
            "completion_tokens": trace.total_tokens.completion_tokens,
            "total_tokens": trace.total_tokens.total_tokens,
            "cost_usd": trace.total_tokens.cost_usd
        },
        total_cost_usd=trace.total_cost_usd,
        tags=trace.tags
    )


def _convert_token_usage_to_response(token_usage: TokenUsage) -> TokenUsageResponse:
    """Convert TokenUsage to response model"""
    return TokenUsageResponse(
        prompt_tokens=token_usage.prompt_tokens,
        completion_tokens=token_usage.completion_tokens,
        total_tokens=token_usage.total_tokens,
        cost_usd=token_usage.cost_usd
    )


@router.get("/status")
async def langsmith_status():
    """Check LangSmith integration status"""
    return {
        "available": langsmith_integration.is_available(),
        "project_name": langsmith_integration.project_name,
        "client_configured": langsmith_integration.client is not None
    }


@router.get("/traces", response_model=List[TaskTraceResponse])
async def get_task_traces(
    thread_id: Optional[str] = Query(None, description="Filter by specific thread ID"),
    hours: Optional[int] = Query(24, description="Number of hours to look back", ge=1, le=720),  # Max 30 days
    limit: Optional[int] = Query(50, description="Maximum number of traces to return", ge=1, le=1000)
):
    """Get task traces from LangSmith"""
    if not langsmith_integration.is_available():
        raise HTTPException(status_code=503, detail="LangSmith integration not available")
    
    try:
        if thread_id:
            # Get specific trace by thread_id
            trace = await langsmith_integration.get_task_trace_by_thread_id(thread_id)
            return [_convert_task_trace_to_response(trace)] if trace else []
        else:
            # Get recent traces
            traces = await langsmith_integration.get_recent_task_traces(hours=hours, limit=limit)
            return [_convert_task_trace_to_response(trace) for trace in traces]
    
    except Exception as e:
        logger.error(f"Error fetching task traces: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/traces/{thread_id}", response_model=TaskDetailResponseV2)
async def get_task_trace_detail(thread_id: str, db: Session = Depends(get_db)):
    """Get detailed information about a specific task trace"""
    if not langsmith_integration.is_available():
        raise HTTPException(status_code=503, detail="LangSmith integration not available")
    
    try:
        trace = await langsmith_integration.get_task_trace_by_thread_id(thread_id)
        if not trace:
            raise HTTPException(status_code=404, detail="Task trace not found")
        
        # Convert runs to response format
        runs = []
        for run in trace.runs:
            token_usage_response = None
            if run.token_usage:
                token_usage_response = _convert_token_usage_to_response(run.token_usage)
            
            runs.append(TaskRunResponse(
                id=run.id,
                name=run.name,
                run_type=run.run_type,
                start_time=run.start_time.isoformat(),
                end_time=run.end_time.isoformat() if run.end_time else None,
                status=run.status,
                inputs=run.inputs,
                outputs=run.outputs,
                token_usage=token_usage_response,
                error=run.error,
                metadata=run.metadata
            ))

        # 通过thread_id获取最新的ResultDocument
        result_document = db.query(Document).filter(Document.thread_id == thread_id).order_by(Document.created_at.desc()).first()
        if result_document:
            result_document = ResultDocument(
                id=result_document.id,
                document_id=result_document.document_id,
                thread_id=result_document.thread_id,
                url=result_document.url,
                created_at=result_document.created_at,
                updated_at=result_document.updated_at
            )

        # return TaskDetailResponse(
        #     trace_id=trace.trace_id,
        #     thread_id=trace.thread_id,
        #     session_name=trace.session_name,
        #     start_time=trace.start_time.isoformat(),
        #     end_time=trace.end_time.isoformat() if trace.end_time else None,
        #     status=trace.status,
        #     total_runs=trace.total_runs,
        #     runs=runs,
        #     total_tokens=_convert_token_usage_to_response(trace.total_tokens),
        #     total_cost_usd=trace.total_cost_usd,
        #     tags=trace.tags,
        #     result_document=result_document
        # )
        return TaskDetailResponseV2(
            messages = runs[0].outputs.get("messages", {}) if runs and runs[0].outputs else [],
            result_document=result_document
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching task trace detail for {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/token-usage/summary", response_model=TokenUsageSummaryResponse)
async def get_token_usage_summary(
    days: Optional[int] = Query(7, description="Number of days to analyze", ge=1, le=90),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD format)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD format)")
):
    """Get token usage summary for a time period"""
    if not langsmith_integration.is_available():
        raise HTTPException(status_code=503, detail="LangSmith integration not available")
    
    try:
        # Parse dates if provided
        start_time = None
        end_time = None
        
        if start_date:
            try:
                start_time = datetime.fromisoformat(start_date)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start_date format. Use YYYY-MM-DD")
        
        if end_date:
            try:
                end_time = datetime.fromisoformat(end_date)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end_date format. Use YYYY-MM-DD")
        
        # If no dates provided, use days parameter
        if not start_time and not end_time:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
        elif start_time and not end_time:
            end_time = datetime.now()
        elif not start_time and end_time:
            start_time = end_time - timedelta(days=days)
        
        # Validate date range
        if start_time >= end_time:
            raise HTTPException(status_code=400, detail="start_date must be before end_date")
        
        if (end_time - start_time).days > 90:
            raise HTTPException(status_code=400, detail="Date range cannot exceed 90 days")
        
        summary = await langsmith_integration.get_token_usage_summary(start_time, end_time)
        return TokenUsageSummaryResponse(**summary)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching token usage summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/trace-details/{trace_id}")
async def get_trace_details(trace_id: str):
    """Get detailed trace information from LangSmith"""
    if not langsmith_integration.is_available():
        raise HTTPException(status_code=503, detail="LangSmith integration not available")
    
    try:
        details = await langsmith_integration.get_trace_details(trace_id)
        if not details:
            raise HTTPException(status_code=404, detail="Trace not found")
        
        return details
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching trace details for {trace_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recent-activity")
async def get_recent_activity(
    hours: Optional[int] = Query(24, description="Hours to look back", ge=1, le=168)  # Max 7 days
):
    """Get recent activity summary"""
    if not langsmith_integration.is_available():
        raise HTTPException(status_code=503, detail="LangSmith integration not available")
    
    try:
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        # Get traces and usage summary
        traces = await langsmith_integration.get_task_traces(
            start_time=start_time, 
            end_time=end_time, 
            limit=100
        )
        
        # Calculate basic statistics
        total_traces = len(traces)
        completed_traces = sum(1 for trace in traces if trace.status == "completed")
        failed_traces = sum(1 for trace in traces if trace.status == "error")
        running_traces = sum(1 for trace in traces if trace.status == "running")
        
        total_tokens = sum(trace.total_tokens.total_tokens for trace in traces)
        total_cost = sum(trace.total_cost_usd or 0 for trace in traces)
        
        # Recent traces (last 10)
        recent_traces = [_convert_task_trace_to_response(trace) for trace in traces[:10]]
        
        return {
            "period": {
                "hours": hours,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            },
            "summary": {
                "total_traces": total_traces,
                "completed_traces": completed_traces,
                "failed_traces": failed_traces,
                "running_traces": running_traces,
                "success_rate": completed_traces / total_traces if total_traces > 0 else 0,
                "total_tokens": total_tokens,
                "total_cost_usd": total_cost if total_cost > 0 else None
            },
            "recent_traces": recent_traces
        }
    
    except Exception as e:
        logger.error(f"Error fetching recent activity: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 