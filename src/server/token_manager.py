# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
import redis
import json
from typing import Dict, <PERSON><PERSON>, Tuple
from sqlalchemy.orm import Session
from src.auth.utils import get_redis_client
from src.database.models import User, ChatHistory
from src.auth.service import AuthService

logger = logging.getLogger(__name__)

class TokenManager:
    """Token使用量管理和积分检查"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.tokens_per_credit = 1000  # 1积分对应1000token
        self.min_credits_threshold = 10  # 最小积分阈值
    
    def _get_redis_key(self, thread_id: str) -> str:
        """获取Redis键名"""
        return f"token_usage:{thread_id}"
    
    def get_current_usage(self, thread_id: str) -> Dict[str, int]:
        """获取当前线程的token使用量"""
        if not self.redis_client:
            return {"input_tokens": 0, "output_tokens": 0}
        
        try:
            key = self._get_redis_key(thread_id)
            usage_data = self.redis_client.hgetall(key)
            
            return {
                "input_tokens": int(usage_data.get("input_tokens", 0)),
                "output_tokens": int(usage_data.get("output_tokens", 0))
            }
        except Exception as e:
            logger.error(f"获取token使用量失败: {e}")
            return {"input_tokens": 0, "output_tokens": 0}
    
    def add_token_usage(self, thread_id: str, input_tokens: int, output_tokens: int) -> bool:
        """累加token使用量到Redis"""
        if not self.redis_client:
            logger.warning("Redis客户端不可用，跳过token统计")
            return False
        
        try:
            key = self._get_redis_key(thread_id)
            pipe = self.redis_client.pipeline()
            
            # 累加input和output tokens
            pipe.hincrby(key, "input_tokens", input_tokens)
            pipe.hincrby(key, "output_tokens", output_tokens)
            pipe.expire(key, 86400)  # 24小时过期
            
            pipe.execute()
            
            logger.debug(f"线程 {thread_id} 累加token: 输入+{input_tokens}, 输出+{output_tokens}")
            return True
            
        except Exception as e:
            logger.error(f"累加token使用量失败: {e}")
            return False
    
    def check_user_credits(self, user_id: int, db: Session) -> Tuple[bool, int]:
        """检查用户积分是否充足
        
        Returns:
            (是否有足够积分, 当前剩余积分)
        """
        try:
            auth_service = AuthService(db)
            user = auth_service.get_user_by_id(user_id)
            
            if not user:
                logger.error(f"用户 {user_id} 不存在")
                return False, 0
            
            remaining_credits = user.remaining_credits
            has_enough_credits = remaining_credits >= self.min_credits_threshold
            
            logger.debug(f"用户 {user_id} 剩余积分: {remaining_credits}, 阈值: {self.min_credits_threshold}")
            
            return has_enough_credits, remaining_credits
            
        except Exception as e:
            logger.error(f"检查用户积分失败: {e}")
            return False, 0
    
    def get_token_cost_in_credits(self, input_tokens: int, output_tokens: int) -> float:
        """计算token消耗对应的积分数"""
        total_tokens = input_tokens + output_tokens
        return total_tokens / self.tokens_per_credit
    
    def deduct_user_credits(self, user_id: int, credits_to_deduct: float, db: Session) -> bool:
        """扣除用户积分
        
        Args:
            user_id: 用户ID
            credits_to_deduct: 要扣除的积分数
            db: 数据库会话
            
        Returns:
            是否扣除成功
        """
        try:
            auth_service = AuthService(db)
            user = auth_service.get_user_by_id(user_id)
            
            if not user:
                logger.error(f"用户 {user_id} 不存在")
                return False
            
            if user.remaining_credits < credits_to_deduct:
                logger.warning(f"用户 {user_id} 积分不足: 剩余{user.remaining_credits}, 需要{credits_to_deduct}")
                return False
            
            # 扣除积分
            user.remaining_credits = max(0, user.remaining_credits - int(credits_to_deduct))
            db.commit()
            
            logger.info(f"用户 {user_id} 扣除积分: {credits_to_deduct}, 剩余: {user.remaining_credits}")
            return True
            
        except Exception as e:
            logger.error(f"扣除用户积分失败: {e}")
            db.rollback()
            return False
    
    def sync_tokens_to_database(self, thread_id: str, user_id: int, db: Session) -> bool:
        """将Redis中的token使用量同步到数据库"""
        try:
            # 获取Redis中的token使用量
            usage = self.get_current_usage(thread_id)
            
            if usage["input_tokens"] == 0 and usage["output_tokens"] == 0:
                return True  # 没有token使用量，跳过
            
            # 获取或创建ChatHistory记录
            auth_service = AuthService(db)
            chat_history = auth_service.create_or_get_chat_history(
                user_id=user_id,
                thread_id=thread_id,
                title=""  # 会话标题由调用方设置
            )
            
            # 更新token使用量（累加）
            chat_history.non_reasoning_input_tokens += usage["input_tokens"]
            chat_history.non_reasoning_output_tokens += usage["output_tokens"]
            
            db.commit()
            
            # 计算并扣除积分
            credits_to_deduct = self.get_token_cost_in_credits(
                usage["input_tokens"], 
                usage["output_tokens"]
            )
            
            if credits_to_deduct > 0:
                self.deduct_user_credits(user_id, credits_to_deduct, db)
            
            logger.info(f"同步token到数据库成功: 线程{thread_id}, 用户{user_id}, "
                       f"输入{usage['input_tokens']}, 输出{usage['output_tokens']}, "
                       f"扣除积分{credits_to_deduct}")
            
            # 清除Redis中的记录
            if self.redis_client:
                self.redis_client.delete(self._get_redis_key(thread_id))
            
            return True
            
        except Exception as e:
            logger.error(f"同步token到数据库失败: {e}")
            db.rollback()
            return False
    
    def extract_token_usage_from_response(self, response) -> Tuple[int, int]:
        """从LLM响应中提取token使用量
        
        Args:
            response: LLM响应对象
            
        Returns:
            (input_tokens, output_tokens)
        """
        input_tokens = 0
        output_tokens = 0
        
        try:
            if hasattr(response, 'response_metadata'):
                metadata = response.response_metadata

                # 尝试从不同字段获取token信息
                usage_info = metadata.get('token_usage', {}) or metadata.get('usage', {})
                if not usage_info:
                    usage_info = response.usage_metadata

                
                if usage_info:
                    input_tokens = usage_info.get('prompt_tokens', 0) or usage_info.get('input_tokens', 0)
                    output_tokens = usage_info.get('completion_tokens', 0) or usage_info.get('output_tokens', 0)
                    
                    logger.debug(f"从响应元数据提取token: 输入{input_tokens}, 输出{output_tokens}")
                else:
                    # 如果没有usage信息，尝试通过内容长度估算
                    if hasattr(response, 'content') and response.content:
                        # 粗略估算：英文约4字符=1token，中文约1.5字符=1token
                        content_length = len(response.content)
                        estimated_tokens = max(1, content_length // 3)  # 保守估算
                        output_tokens = estimated_tokens
                        logger.debug(f"通过内容长度估算输出token: {output_tokens}")

            
        except Exception as e:
            logger.error(f"提取token使用量失败: {e}")
        
        return input_tokens, output_tokens


# 全局token管理器实例
token_manager = TokenManager() 