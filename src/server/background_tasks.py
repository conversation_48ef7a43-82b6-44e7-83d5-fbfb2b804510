# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import asyncio
import logging
import traceback
from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from sqlalchemy.orm import Session
from langchain_core.messages import AIMessage

from src.database.database import SessionLocal
from src.database.models import TaskExecution, ChatHistory, User
from src.graph.builder import build_graph_with_memory
from src.rag.retriever import Resource
from src.server.chat_request import ChatMessage

logger = logging.getLogger(__name__)


class BackgroundTaskManager:
    """后台任务管理器"""
    
    def __init__(self):
        self.graph = build_graph_with_memory()
        self.running_tasks = {}  # task_id -> asyncio.Task
    
    async def start_background_execution_from_checkpoint(
        self,
        task_id: str,
        thread_id: str,
        user_id: int,
        resources: List[Resource] = None,
        max_plan_iterations: int = 3,
        max_step_num: int = 20,
        max_search_results: int = 10,
        auto_accepted_plan: bool = False,
        interrupt_feedback: str = "",
        mcp_settings: dict = None,
        enable_background_investigation: bool = True,
        checkpoint_config: dict = None,
    ) -> bool:
        """
        从checkpoint启动后台执行任务

        Returns:
            bool: 是否成功启动
        """
        try:
            # 创建任务记录
            db = SessionLocal()
            try:
                task_execution = TaskExecution(
                    task_id=task_id,
                    thread_id=thread_id,
                    user_id=user_id,
                    status="pending",
                    progress=0,
                    current_step="从checkpoint恢复执行"
                )
                db.add(task_execution)
                db.commit()
                db.refresh(task_execution)
            finally:
                db.close()

            # 启动后台任务
            task = asyncio.create_task(
                self._execute_graph_from_checkpoint(
                    task_id=task_id,
                    thread_id=thread_id,
                    user_id=user_id,
                    resources=resources or [],
                    max_plan_iterations=max_plan_iterations,
                    max_step_num=max_step_num,
                    max_search_results=max_search_results,
                    auto_accepted_plan=auto_accepted_plan,
                    interrupt_feedback=interrupt_feedback,
                    mcp_settings=mcp_settings or {},
                    enable_background_investigation=enable_background_investigation,
                    checkpoint_config=checkpoint_config,
                )
            )

            self.running_tasks[task_id] = task
            logger.info(f"后台任务已从checkpoint启动: {task_id}")
            return True

        except Exception as e:
            logger.error(f"从checkpoint启动后台任务失败: {e}")
            traceback.print_exc()
            return False

    async def start_background_execution(
        self,
        task_id: str,
        messages: List[ChatMessage],
        thread_id: str,
        user_id: int,
        resources: List[Resource] = None,
        max_plan_iterations: int = 3,
        max_step_num: int = 20,
        max_search_results: int = 10,
        auto_accepted_plan: bool = False,
        interrupt_feedback: str = "",
        mcp_settings: dict = None,
        enable_background_investigation: bool = True,
    ) -> bool:
        """
        启动后台执行任务
        
        Returns:
            bool: 是否成功启动
        """
        try:
            # 创建任务记录
            db = SessionLocal()
            try:
                task_execution = TaskExecution(
                    task_id=task_id,
                    thread_id=thread_id,
                    user_id=user_id,
                    status="pending",
                    progress=0,
                    current_step="初始化任务"
                )
                db.add(task_execution)
                db.commit()
                db.refresh(task_execution)
            finally:
                db.close()
            
            # 启动后台任务
            task = asyncio.create_task(
                self._execute_graph_background(
                    task_id=task_id,
                    messages=messages,
                    thread_id=thread_id,
                    user_id=user_id,
                    resources=resources or [],
                    max_plan_iterations=max_plan_iterations,
                    max_step_num=max_step_num,
                    max_search_results=max_search_results,
                    auto_accepted_plan=auto_accepted_plan,
                    interrupt_feedback=interrupt_feedback,
                    mcp_settings=mcp_settings or {},
                    enable_background_investigation=enable_background_investigation,
                )
            )
            
            self.running_tasks[task_id] = task
            logger.info(f"后台任务已启动: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动后台任务失败: {e}")
            traceback.print_exc()
            return False

    async def _execute_graph_from_checkpoint(
        self,
        task_id: str,
        thread_id: str,
        user_id: int,
        resources: List[Resource],
        max_plan_iterations: int,
        max_step_num: int,
        max_search_results: int,
        auto_accepted_plan: bool,
        interrupt_feedback: str,
        mcp_settings: dict,
        enable_background_investigation: bool,
        checkpoint_config: dict,
    ):
        """从checkpoint后台执行 graph"""
        db = SessionLocal()
        try:
            # 更新任务状态为运行中
            task_execution = db.query(TaskExecution).filter(
                TaskExecution.task_id == task_id
            ).first()
            if task_execution:
                task_execution.status = "running"
                task_execution.current_step = "从checkpoint恢复执行"
                db.commit()

            logger.info(f"开始从checkpoint后台执行 graph，task_id: {task_id}, checkpoint_config: {checkpoint_config}")

            node_count = 0
            total_nodes = max_step_num  # 估算总步数

            # 从checkpoint执行 graph，传入None作为input表示从当前状态继续
            # 使用原始的checkpoint配置，不添加额外参数以避免干扰LangGraph的checkpoint机制
            logger.info(f"使用原始checkpoint配置: {checkpoint_config}")
            
            async for agent, _, event_data in self.graph.astream(
                None,  # 从checkpoint恢复时传入None
                config=checkpoint_config,  # 使用原始checkpoint配置
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                try:
                    # 打印event_data
                    logger.info(f"checkpoint恢复执行 event_data: {event_data}")
                    node_count += 1
                    progress = min(90, int((node_count / total_nodes) * 90))  # 最高90%，留10%给完成

                    # 更新进度
                    task_execution = db.query(TaskExecution).filter(
                        TaskExecution.task_id == task_id
                    ).first()
                    if task_execution:
                        task_execution.progress = progress
                        task_execution.current_step = f"执行节点: {agent}"
                        db.commit()

                    logger.info(f"checkpoint恢复后台任务 {task_id} 进度: {progress}%, 当前节点: {agent}")

                except Exception as e:
                    logger.error(f"处理checkpoint恢复事件时出错: {e}")
                    continue

            # 任务完成
            task_execution = db.query(TaskExecution).filter(
                TaskExecution.task_id == task_id
            ).first()
            if task_execution:
                task_execution.status = "completed"
                task_execution.progress = 100
                task_execution.current_step = "任务完成"
                task_execution.completed_at = datetime.utcnow()

                # 尝试获取结果文档ID
                chat_history = db.query(ChatHistory).filter(
                    ChatHistory.thread_id == thread_id
                ).first()
                if chat_history and chat_history.result_document_id:
                    task_execution.result_document_id = chat_history.result_document_id

                db.commit()

            logger.info(f"checkpoint恢复后台任务完成: {task_id}")

        except Exception as e:
            logger.error(f"checkpoint恢复后台执行失败: {e}")
            traceback.print_exc()

            # 更新任务状态为失败
            try:
                task_execution = db.query(TaskExecution).filter(
                    TaskExecution.task_id == task_id
                ).first()
                if task_execution:
                    task_execution.status = "failed"
                    task_execution.error_message = str(e)
                    task_execution.completed_at = datetime.utcnow()
                    db.commit()
            except Exception as update_error:
                logger.error(f"更新checkpoint恢复任务失败状态时出错: {update_error}")
        finally:
            db.close()
            # 清理运行中的任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    async def _execute_graph_background(
        self,
        task_id: str,
        messages: List[ChatMessage],
        thread_id: str,
        user_id: int,
        resources: List[Resource],
        max_plan_iterations: int,
        max_step_num: int,
        max_search_results: int,
        auto_accepted_plan: bool,
        interrupt_feedback: str,
        mcp_settings: dict,
        enable_background_investigation: bool,
    ):
        """后台执行 graph"""
        db = SessionLocal()
        try:
            # 更新任务状态为运行中
            task_execution = db.query(TaskExecution).filter(
                TaskExecution.task_id == task_id
            ).first()
            if task_execution:
                task_execution.status = "running"
                task_execution.current_step = "开始执行"
                db.commit()
            
            # 准备输入
            input_ = {
                "messages": messages,
                "plan_iterations": 0,
                "final_report": "",
                "current_plan": None,
                "observations": [],
                "auto_accepted_plan": auto_accepted_plan,
                "enable_background_investigation": enable_background_investigation,
                "user_id": user_id,
                "thread_id": thread_id,  # 添加 thread_id 到状态中
            }
            
            logger.info(f"开始后台执行 graph，task_id: {task_id}")
            
            node_count = 0
            total_nodes = max_step_num  # 估算总步数
            
            # 执行 graph
            async for agent, _, event_data in self.graph.astream(
                input_,
                config={
                    "thread_id": thread_id,
                    "resources": resources,
                    "max_plan_iterations": max_plan_iterations,
                    "max_step_num": max_step_num,
                    "max_search_results": max_search_results,
                    "mcp_settings": mcp_settings,
                },
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                try:
                    # 打印event_data
                    logger.info(f"event_data: {event_data}")
                    node_count += 1
                    progress = min(90, int((node_count / total_nodes) * 90))  # 最高90%，留10%给完成
                    
                    # 更新进度
                    task_execution = db.query(TaskExecution).filter(
                        TaskExecution.task_id == task_id
                    ).first()
                    if task_execution:
                        task_execution.progress = progress
                        task_execution.current_step = f"执行节点: {agent}"
                        db.commit()
                    
                    logger.info(f"后台任务 {task_id} 进度: {progress}%, 当前节点: {agent}")
                    
                except Exception as e:
                    logger.error(f"更新任务进度失败: {e}")
            
            # 任务完成，更新状态
            task_execution = db.query(TaskExecution).filter(
                TaskExecution.task_id == task_id
            ).first()
            if task_execution:
                task_execution.status = "completed"
                task_execution.progress = 100
                task_execution.current_step = "任务完成"
                task_execution.completed_at = datetime.utcnow()
                
                # 尝试获取结果文档ID
                chat_history = db.query(ChatHistory).filter(
                    ChatHistory.thread_id == thread_id
                ).first()
                if chat_history and chat_history.result_document_id:
                    task_execution.result_document_id = chat_history.result_document_id
                
                db.commit()
            
            logger.info(f"后台任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"后台任务执行失败: {task_id}, 错误: {e}")
            traceback.print_exc()
            
            # 更新任务状态为失败
            try:
                task_execution = db.query(TaskExecution).filter(
                    TaskExecution.task_id == task_id
                ).first()
                if task_execution:
                    task_execution.status = "failed"
                    task_execution.error_message = str(e)
                    task_execution.completed_at = datetime.utcnow()
                    db.commit()
            except Exception as update_error:
                logger.error(f"更新任务失败状态时出错: {update_error}")
                
        finally:
            db.close()
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    def get_task_status(self, task_id: str) -> Optional[dict]:
        """获取任务状态"""
        db = SessionLocal()
        try:
            task_execution = db.query(TaskExecution).filter(
                TaskExecution.task_id == task_id
            ).first()
            
            if not task_execution:
                return None
            
            return {
                "task_id": task_execution.task_id,
                "thread_id": task_execution.thread_id,
                "status": task_execution.status,
                "progress": task_execution.progress,
                "current_step": task_execution.current_step,
                "result_document_id": task_execution.result_document_id,
                "error_message": task_execution.error_message,
                "created_at": task_execution.created_at.isoformat() if task_execution.created_at else None,
                "completed_at": task_execution.completed_at.isoformat() if task_execution.completed_at else None,
            }
        finally:
            db.close()
    
    def get_user_tasks(self, user_id: int, limit: int = 20) -> List[dict]:
        """获取用户的任务列表"""
        db = SessionLocal()
        try:
            tasks = db.query(TaskExecution).filter(
                TaskExecution.user_id == user_id
            ).order_by(TaskExecution.created_at.desc()).limit(limit).all()
            
            return [
                {
                    "task_id": task.task_id,
                    "thread_id": task.thread_id,
                    "status": task.status,
                    "progress": task.progress,
                    "current_step": task.current_step,
                    "result_document_id": task.result_document_id,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                }
                for task in tasks
            ]
        finally:
            db.close()
    
    def is_task_running(self, task_id: str) -> bool:
        """检查任务是否正在运行"""
        return task_id in self.running_tasks and not self.running_tasks[task_id].done()


# 全局后台任务管理器实例
background_task_manager = BackgroundTaskManager() 