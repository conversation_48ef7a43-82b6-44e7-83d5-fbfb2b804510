# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import Optional
from uuid import uuid4
import logging

from src.database import get_db
from src.database.models import User, ResultDocument
from src.auth.middleware import get_current_user
from src.utils.file_utils import upload_file_to_storage

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Files"])


class FileUploadResponse:
    def __init__(self, success: bool, message: str, document_id: str = None, file_url: str = None):
        self.success = success
        self.message = message
        self.document_id = document_id
        self.file_url = file_url


class DocumentResponse:
    def __init__(self, success: bool, message: str, document_id: str = None, content: str = None, url: str = None):
        self.success = success
        self.message = message
        self.document_id = document_id
        self.content = content
        self.url = url


@router.post("/files/upload")
async def upload_file(
    file: UploadFile = File(...),
    thread_id: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    上传文件到 Cloudflare 存储并保存到数据库
    """
    try:
        # 检查文件大小 (限制为50MB)
        max_file_size = 50 * 1024 * 1024  # 50MB
        if file.size > max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="文件大小超过限制（最大50MB）"
            )
        
        # 读取文件内容
        file_content = await file.read()
        file_content_str = file_content.decode('utf-8')
        
        # 生成document_id
        document_id = str(uuid4())
        
        # 上传文件到 Cloudflare 存储
        file_url = upload_file_to_storage(
            file_content=file_content_str,
            file_name=file.filename,
            user_id=current_user.id
        )
        
        if not file_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="文件上传失败"
            )
        
        # 保存到数据库
        result_document = ResultDocument(
            document_id=document_id,
            thread_id=thread_id,
            url=file_url
        )
        
        db.add(result_document)
        db.commit()
        db.refresh(result_document)
        
        logger.info(f"文件上传成功: document_id={document_id}, thread_id={thread_id}, user_id={current_user.id}, file_name={file.filename}")
        
        return {
            "success": True,
            "message": "文件上传成功",
            "document_id": document_id,
            "file_url": file_url
        }
        
    except UnicodeDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件编码格式不支持，请上传UTF-8编码的文本文件"
        )
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


@router.get("/files/{document_id}")
async def get_document(
    document_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    通过 document_id 获取文档内容
    """
    try:
        # 从数据库获取文档记录
        result_document = db.query(ResultDocument).filter(
            ResultDocument.document_id == document_id
        ).first()
        
        if not result_document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档未找到"
            )
        
        # 从 Cloudflare 存储获取文件内容
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(result_document.url)
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="获取文档内容失败"
                )
            
            content = response.text
        
        logger.info(f"文档获取成功: document_id={document_id}, user_id={current_user.id}")
        
        return {
            "success": True,
            "message": "文档获取成功",
            "document_id": document_id,
            "content": content,
            "url": result_document.url
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档获取失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档获取失败: {str(e)}"
        )
