import time

import boto3
import asyncio
import uuid
import logging
import tempfile

logger = logging.getLogger(__name__)

def upload_file_to_storage(file_content: str, file_name: str, user_id: int, retry_count: int = 3) -> str:
    """
    上传文件到云存储服务器
    
    :param file_content: 文件内容
    :param file_name: 文件名带后缀
    :param user_id: 用户ID
    :param retry_count: 重试次数
    :return: 上传后的文件URL
    """
    try:
        # 创建S3客户端
        s3 = boto3.client(
            service_name="s3",
            endpoint_url='https://5ed09baabc64f76f150b381face6ec5a.r2.cloudflarestorage.com',
            aws_access_key_id='bd3cf3113432b0cdc065586a946a2a75',
            aws_secret_access_key='309ee44ba3f911f295bd150f2fd233d8aec1d7c8732e0bc815f9958bb5a7e3a0',
            region_name="auto"
        )

        if not user_id:
            user_id = "noid"
        
        # 生成随机文件名
        random_file_name = str(uuid.uuid4()) + '.' + file_name.split('.')[-1]
        
        # 上传文件
        def upload_sync():
            # 将文件内容写入临时文件
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_content.encode('utf-8'))
                temp_file_path = temp_file.name
            
            # 上传文件
            with open(temp_file_path, 'rb') as file:
                s3.upload_fileobj(file, "thinkwriter", f"file/{user_id}/{random_file_name}")
            
            return f"https://files.thinkwriter.ai/file/{user_id}/{random_file_name}"

        return upload_sync()
        
    except Exception as e:
        logger.error(f"Upload file failed: {e}")
        # 重试
        if retry_count > 0:
            time.sleep(1)
            return upload_file_to_storage(file_content, file_name, user_id, retry_count - 1)
        else:
            return ""