# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import requests
from typing import List, Dict, Any, Optional, Union
from src.rag.retriever import Chunk, Document, Resource, Retriever
from urllib.parse import urlparse
import urllib3
import time

# Disable SSL warnings for demo servers
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Import FastAPI UploadFile for type hints
try:
    from fastapi import UploadFile
except ImportError:
    UploadFile = None


class RAGFlowProvider(Retriever):
    """
    RAGFlowProvider is a provider that uses RAGFlow to retrieve documents.
    """

    api_url: str
    api_key: str
    page_size: int = 10
    verify_ssl: bool = True

    def __init__(self):
        api_url = os.getenv("RAGFLOW_API_URL")
        if not api_url:
            raise ValueError("RAGFLOW_API_URL is not set")
        self.api_url = api_url

        api_key = os.getenv("RAGFLOW_API_KEY")
        if not api_key:
            raise ValueError("RAGFLOW_API_KEY is not set")
        self.api_key = api_key

        page_size = os.getenv("RAGFLOW_PAGE_SIZE", "10")
        try:
            self.page_size = int(page_size)
        except ValueError:
            self.page_size = 10
        
        # Disable SSL verification for demo servers
        if "demo.ragflow.io" in api_url:
            self.verify_ssl = False

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with proper error handling and SSL settings."""
        url = f"{self.api_url.rstrip('/')}/api/v1/{endpoint.lstrip('/')}"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Add default kwargs
        kwargs.setdefault("headers", {}).update(headers)
        kwargs.setdefault("verify", self.verify_ssl)
        kwargs.setdefault("timeout", 30)
        
        # Retry configuration
        max_retries = 3
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                response = requests.request(method, url, **kwargs)
                response.raise_for_status()
                return response.json()
            except requests.exceptions.SSLError as e:
                if self.verify_ssl and attempt == 0:
                    # Retry without SSL verification for demo servers
                    print(f"SSL error, retrying without verification: {e}")
                    kwargs["verify"] = False
                    continue
                else:
                    raise ConnectionError(f"SSL verification failed: {str(e)}")
            except (requests.exceptions.ConnectionError, 
                    requests.exceptions.Timeout,
                    ConnectionResetError) as e:
                if attempt < max_retries - 1:
                    print(f"Connection error (attempt {attempt + 1}/{max_retries}), retrying in {retry_delay}s: {e}")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    raise ConnectionError(f"RAGFlow API connection failed after {max_retries} attempts: {str(e)}")
            except requests.exceptions.RequestException as e:
                raise ConnectionError(f"RAGFlow API request failed: {str(e)}")
            except ValueError as e:
                raise ValueError(f"Invalid JSON response from RAGFlow: {str(e)}")
        
        raise ConnectionError("Failed to complete request after all retries")

    def create_dataset(self, name: str, description: str = "", **kwargs) -> Dict[str, Any]:
        """Create a new dataset in RAGFlow."""
        payload = {
            "name": name,
            "description": description,
            "language": "English",
            "embedding_model": kwargs.get("embedding_model", "BAAI/bge-large-en-v1.5@BAAI"),
            "permission": "me",
            "document_count": 0,
            "chunk_count": 0,
            "parse_method": kwargs.get("chunk_method", "naive"),
            "parser_config": {}
        }
        
        return self._make_request("POST", "datasets", json=payload)

    async def upload_documents(self, dataset_id: str, files: List[Union[Dict[str, Any], 'UploadFile']], auto_parse: bool = True) -> Dict[str, Any]:
        """Upload documents to a RAGFlow dataset.
        
        Args:
            dataset_id: The ID of the dataset to upload to
            files: List of either UploadFile objects or dict format files
            auto_parse: Whether to automatically parse documents after upload
        """
        # For RAGFlow, we need to use multipart/form-data for file uploads
        url = f"{self.api_url.rstrip('/')}/api/v1/datasets/{dataset_id}/documents"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        # Prepare files for multipart upload
        files_data = []
        for file_info in files:
            # Handle both UploadFile objects and legacy dict format
            if hasattr(file_info, 'filename') and hasattr(file_info, 'read'):
                # This is an UploadFile object
                content = await file_info.read()
                filename = file_info.filename or "unnamed_file"
                content_type = file_info.content_type or "application/octet-stream"
                
                # Determine content type based on file extension if not provided
                if content_type == "application/octet-stream" and filename:
                    if filename.lower().endswith('.pdf'):
                        content_type = "application/pdf"
                    elif filename.lower().endswith(('.txt', '.md')):
                        content_type = "text/plain"
                    elif filename.lower().endswith(('.doc', '.docx')):
                        content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                
                files_data.append(
                    ("file", (filename, content, content_type))
                )
            else:
                # Legacy dict format for backward compatibility
                if isinstance(file_info["content"], bytes):
                    content = file_info["content"]
                elif isinstance(file_info["content"], list):
                    # Convert from list of integers to bytes
                    content = bytes(file_info["content"])
                else:
                    # Convert to bytes if it's not already
                    content = str(file_info["content"]).encode('utf-8')
                
                files_data.append(
                    ("file", (file_info["name"], content, "application/octet-stream"))
                )
        
        max_retries = 3
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    url, 
                    files=files_data, 
                    headers=headers,
                    verify=self.verify_ssl,
                    timeout=60
                )
                response.raise_for_status()
                upload_result = response.json()
                
                # If auto_parse is enabled and upload was successful, parse the documents
                if auto_parse and upload_result.get("code") == 0:
                    # Handle different response formats
                    data_part = upload_result.get("data", {})
                    documents = []
                    
                    # Check if data is directly a list or contains documents
                    if isinstance(data_part, list):
                        documents = data_part
                    elif isinstance(data_part, dict):
                        documents = data_part.get("documents", [])
                    
                    if documents:
                        document_ids = [doc.get("id") for doc in documents if isinstance(doc, dict) and doc.get("id")]
                        if document_ids:
                            try:
                                print(f"🔄 自动解析上传的文档: {document_ids}")
                                parse_result = self.parse_documents(dataset_id, document_ids)
                                if parse_result.get("code") == 0:
                                    print(f"✅ 文档解析已启动")
                                    # Add parse info to the upload result
                                    upload_result["parse_started"] = True
                                    upload_result["parsed_document_ids"] = document_ids
                                else:
                                    print(f"⚠️ 文档解析启动失败: {parse_result}")
                                    upload_result["parse_started"] = False
                                    upload_result["parse_error"] = parse_result
                            except Exception as e:
                                print(f"⚠️ 自动解析过程中出错: {e}")
                                upload_result["parse_started"] = False
                                upload_result["parse_error"] = str(e)
                
                return upload_result
                
            except requests.exceptions.SSLError as e:
                if self.verify_ssl and attempt == 0:
                    # Retry without SSL verification
                    print(f"SSL error during upload, retrying without verification: {e}")
                    self.verify_ssl = False
                    continue
                else:
                    raise ConnectionError(f"SSL verification failed during upload: {str(e)}")
            except (requests.exceptions.ConnectionError, 
                    requests.exceptions.Timeout,
                    ConnectionResetError) as e:
                if attempt < max_retries - 1:
                    print(f"Upload connection error (attempt {attempt + 1}/{max_retries}), retrying in {retry_delay}s: {e}")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise ConnectionError(f"Document upload failed after {max_retries} attempts: {str(e)}")
            except requests.exceptions.RequestException as e:
                raise ConnectionError(f"Document upload failed: {str(e)}")

    def list_documents(self, dataset_id: str, **kwargs) -> List[Dict[str, Any]]:
        """List documents in a dataset."""
        params = {
            "page": kwargs.get("page", 1),
            "page_size": kwargs.get("page_size", 30)
        }
        if kwargs.get("keywords"):
            params["keywords"] = kwargs["keywords"]
        
        response = self._make_request("GET", f"datasets/{dataset_id}/documents", params=params)
        return response.get("data", [])

    def delete_documents(self, dataset_id: str, document_ids: List[str]) -> Dict[str, Any]:
        """Delete documents from a dataset."""
        payload = {"document_ids": document_ids}
        return self._make_request("DELETE", f"datasets/{dataset_id}/documents", json=payload)

    def parse_documents(self, dataset_id: str, document_ids: List[str]) -> Dict[str, Any]:
        """Start parsing documents in a dataset using the chunks endpoint."""
        payload = {"document_ids": document_ids}
        return self._make_request("POST", f"datasets/{dataset_id}/chunks", json=payload)

    def query_relevant_documents(
        self, query: str, resources: list[Resource] = []
    ) -> list[Document]:
        dataset_ids: list[str] = []
        document_ids: list[str] = []

        for resource in resources:
            dataset_id, document_id = parse_uri(resource.uri)
            dataset_ids.append(dataset_id)
            if document_id:
                document_ids.append(document_id)

        payload = {
            "question": query,
            "dataset_ids": dataset_ids,
            "document_ids": document_ids,
            "page_size": self.page_size,
        }

        result = self._make_request("POST", "retrieval", json=payload)
        data = result.get("data", {})
        doc_aggs = data.get("doc_aggs", [])
        docs: dict[str, Document] = {
            doc.get("doc_id"): Document(
                id=doc.get("doc_id"),
                title=doc.get("doc_name"),
                chunks=[],
            )
            for doc in doc_aggs
        }

        for chunk in data.get("chunks", []):
            doc = docs.get(chunk.get("document_id"))
            if doc:
                doc.chunks.append(
                    Chunk(
                        content=chunk.get("content"),
                        similarity=chunk.get("similarity"),
                    )
                )

        return list(docs.values())

    def list_resources(self, query: str | None = None) -> list[Resource]:
        """List available datasets as resources."""
        params = {}
        if query:
            params["name"] = query

        try:
            result = self._make_request("GET", "datasets", params=params)
            resources = []

            for item in result.get("data", []):
                resource = Resource(
                    uri=f"rag://dataset/{item.get('id')}",
                    title=item.get("name", ""),
                    description=item.get("description", ""),
                )
                resources.append(resource)

            return resources
        except ConnectionError as e:
            print(f"Warning: Failed to connect to RAGFlow server: {e}")
            # Return empty list instead of crashing the server
            return []
        except Exception as e:
            print(f"Warning: Error listing RAGFlow resources: {e}")
            return []


def parse_uri(uri: str) -> tuple[str, str]:
    parsed = urlparse(uri)
    if parsed.scheme != "rag":
        raise ValueError(f"Invalid URI: {uri}")
    return parsed.path.split("/")[1], parsed.fragment


if __name__ == "__main__":
    uri = "rag://dataset/123#abc"
    parsed = urlparse(uri)
    print(parsed.scheme)
    print(parsed.netloc)
    print(parsed.path)
    print(parsed.fragment)
