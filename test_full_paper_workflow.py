#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.graph.nodes import human_feedback_node, research_team_node, _execute_agent_step
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_full_paper_workflow():
    """Test the complete paper writing workflow to verify state preservation."""
    
    # Step 1: Test human_feedback_node with [PAPER_WRITING] choice
    print("Step 1: Testing human_feedback_node with [PAPER_WRITING] choice")
    
    plan_json = {
        "locale": "zh-CN",
        "has_enough_context": False,
        "title": "基于AI的写作工具分析研究计划",
        "thought": "分析基于AI的写作工具",
        "steps": [
            {
                "need_web_search": True,
                "title": "AI写作工具历史与市场概况研究",
                "description": "收集AI写作工具的发展历史",
                "step_type": "research",
                "execution_res": None
            },
            {
                "need_web_search": True,
                "title": "主流AI写作工具技术、功能与评价研究",
                "description": "研究当前市场上主流的AI写作工具",
                "step_type": "research",
                "execution_res": None
            }
        ]
    }
    
    initial_state = {
        "messages": [
            HumanMessage(content="基于AI的写作工具分析"),
        ],
        "locale": "zh-CN",
        "current_plan": json.dumps(plan_json, ensure_ascii=False),
        "plan_iterations": 0,
        "auto_accepted_plan": False
    }
    
    # Mock the interrupt to return [PAPER_WRITING]
    import unittest.mock
    with unittest.mock.patch('src.graph.nodes.interrupt', return_value="[PAPER_WRITING]"):
        result1 = human_feedback_node(initial_state)
    
    print(f"human_feedback_node result: {result1}")
    print(f"Update keys: {list(result1.update.keys()) if result1.update else 'None'}")
    print(f"paper_writing_mode in update: {result1.update.get('paper_writing_mode', 'Not found') if result1.update else 'No update'}")
    print(f"Next node: {result1.goto}")
    
    assert result1.goto == "research_team", f"Expected 'research_team', got '{result1.goto}'"
    assert result1.update.get("paper_writing_mode") == True, f"Expected paper_writing_mode=True, got {result1.update.get('paper_writing_mode')}"
    print("✅ human_feedback_node test passed\n")
    
    # Step 2: Create state after human_feedback_node
    print("Step 2: Testing research_team_node with paper_writing_mode=True")
    
    # current_plan is already a Plan object, not a JSON string
    plan = result1.update["current_plan"]
    state_after_feedback = {
        "messages": [
            HumanMessage(content="基于AI的写作工具分析"),
        ],
        "locale": result1.update["locale"],
        "current_plan": plan,
        "plan_iterations": result1.update["plan_iterations"],
        "paper_writing_mode": result1.update["paper_writing_mode"],
        "observations": []
    }
    
    print(f"State after feedback - paper_writing_mode: {state_after_feedback.get('paper_writing_mode')}")
    
    # Test research_team_node with incomplete research
    result2 = research_team_node(state_after_feedback)
    print(f"research_team_node result (incomplete): {result2}")
    assert result2.goto == "researcher", f"Expected 'researcher', got '{result2.goto}'"
    print("✅ research_team_node (incomplete) test passed\n")
    
    # Step 3: Simulate completing all research steps
    print("Step 3: Testing research_team_node with completed research")
    
    # Complete all research steps
    for step in plan.steps:
        step.execution_res = f"研究结果：{step.title} 已完成"
    
    state_with_completed_research = {
        **state_after_feedback,
        "current_plan": plan,
    }
    
    print(f"State with completed research - paper_writing_mode: {state_with_completed_research.get('paper_writing_mode')}")
    
    # Test research_team_node with completed research
    result3 = research_team_node(state_with_completed_research)
    print(f"research_team_node result (completed): {result3}")
    print(f"Expected: goto='outline_writer', Actual: goto='{result3.goto}'")
    
    assert result3.goto == "outline_writer", f"Expected 'outline_writer', got '{result3.goto}'"
    print("✅ research_team_node (completed) test passed\n")
    
    print("All workflow tests passed! 🎉")
    return True

if __name__ == "__main__":
    try:
        success = test_full_paper_workflow()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 