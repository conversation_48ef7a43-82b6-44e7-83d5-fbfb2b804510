# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import pytest
from unittest.mock import Mock, patch
from langchain_core.messages import HumanMessage, AIMessage

from src.graph.nodes import outline_writer_node, paper_writer_node
from src.graph.types import State
from src.prompts.planner_model import Plan, Step, StepType
from src.config.configuration import Configuration


@pytest.fixture
def mock_state():
    """Create a mock state for testing."""
    plan = Plan(
        locale="en-US",
        has_enough_context=True,
        title="Test Research Plan",
        thought="This is a test plan",
        steps=[
            Step(
                need_web_search=True,
                title="Step 1",
                description="First step",
                step_type=StepType.RESEARCH,
                execution_res="Step 1 result"
            ),
            Step(
                need_web_search=False,
                title="Step 2", 
                description="Second step",
                step_type=StepType.PROCESSING,
                execution_res="Step 2 result"
            )
        ]
    )
    
    return {
        "messages": [HumanMessage(content="Write a paper about AI")],
        "current_plan": plan,
        "locale": "en-US"
    }


@pytest.fixture
def mock_config():
    """Create a mock configuration."""
    return {"configurable": {}}


@patch('src.graph.nodes.get_llm_by_type')
@patch('src.graph.nodes.apply_prompt_template')
def test_outline_writer_node_template_application(mock_apply_template, mock_get_llm, mock_state, mock_config):
    """Test that outline_writer_node correctly applies the template."""
    # Mock the template application
    mock_apply_template.return_value = [
        {"role": "system", "content": "You are an outline writer"},
        {"role": "user", "content": "Write a paper about AI"}
    ]
    
    # Mock the LLM response
    mock_llm = Mock()
    mock_llm.stream.return_value = [
        Mock(content='{"title": "AI Paper", "abstract": "Test abstract", "sections": []}')
    ]
    mock_get_llm.return_value = mock_llm
    
    # Call the node
    result = outline_writer_node(mock_state, mock_config)
    
    # Verify template was called with correct parameters
    mock_apply_template.assert_called_once()
    call_args = mock_apply_template.call_args
    
    # Check that the template state includes all required fields
    template_state = call_args[0][1]  # Second argument is the state
    assert "messages" in template_state
    assert "user_query" in template_state
    assert "current_plan" in template_state
    assert "research_thinking" in template_state
    assert "research_results" in template_state
    assert "locale" in template_state
    
    # Verify the result
    assert result.goto == "outline_feedback"
    assert "paper_outline" in result.update


@patch('src.graph.nodes.get_llm_by_type')
@patch('src.graph.nodes.apply_prompt_template')
def test_paper_writer_node_template_application(mock_apply_template, mock_get_llm, mock_state, mock_config):
    """Test that paper_writer_node correctly applies the template."""
    # Add paper writing specific state
    mock_state.update({
        "approved_outline": '{"title": "AI Paper", "sections": [{"sectionTitle": "Introduction"}]}',
        "current_section_index": 0,
        "paper_sections": []
    })
    
    # Mock the template application
    mock_apply_template.return_value = [
        {"role": "system", "content": "You are a paper writer"},
        {"role": "user", "content": "Write a paper about AI"}
    ]
    
    # Mock the LLM response
    mock_llm = Mock()
    mock_llm.stream.return_value = [
        Mock(content='{"section_content": "This is the introduction section."}')
    ]
    mock_get_llm.return_value = mock_llm
    
    # Call the node
    result = paper_writer_node(mock_state, mock_config)
    
    # Verify template was called with correct parameters
    mock_apply_template.assert_called_once()
    call_args = mock_apply_template.call_args
    
    # Check that the template state includes all required fields
    template_state = call_args[0][1]  # Second argument is the state
    assert "messages" in template_state
    assert "user_query" in template_state
    assert "current_plan" in template_state
    assert "research_thinking" in template_state
    assert "research_results" in template_state
    assert "approved_outline" in template_state
    assert "current_section" in template_state
    assert "locale" in template_state
    
    # Verify the result
    assert result.goto == "paper_writer"
    assert "paper_sections" in result.update
    assert "current_section_index" in result.update


def test_template_state_structure(mock_state):
    """Test that the template state structure is correct."""
    from src.graph.nodes import outline_writer_node
    
    # Extract the template state creation logic
    user_query = mock_state["messages"][-1].content if mock_state["messages"] else ""
    current_plan = mock_state.get("current_plan")
    
    template_state = {
        **mock_state,
        "user_query": user_query,
        "current_plan": str(current_plan.model_dump() if current_plan else {}),
        "research_thinking": "",
        "research_results": "[]",
        "locale": mock_state.get("locale", "en-US")
    }
    
    # Verify all required keys are present
    required_keys = ["messages", "user_query", "current_plan", "research_thinking", "research_results", "locale"]
    for key in required_keys:
        assert key in template_state, f"Missing required key: {key}"
    
    # Verify messages is preserved from original state
    assert template_state["messages"] == mock_state["messages"] 