#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_prompt_analysis():
    """Analyze the prompt to identify why the model returns empty response."""
    
    # Create the same problematic state
    plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="人工智能在医疗诊断中应用的学术论文信息收集计划",
        thought="用户希望撰写一篇关于人工智能在医疗诊断中应用的学术论文，需要收集文献综述、技术分析和未来展望所需的信息。",
        steps=[
            Step(
                need_web_search=True,
                title="收集人工智能在医疗诊断应用的文献综述信息",
                description="收集关于人工智能在医疗诊断领域应用的现有学术文献、研究论文和综述。",
                step_type=StepType.RESEARCH,
                execution_res=None
            )
        ]
    )
    
    state = {
        "messages": [
            HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望"),
            AIMessage(content='{"locale": "zh-CN", "has_enough_context": false, "title": "人工智能在医疗诊断中应用的学术论文信息收集计划"}', name="planner")
        ],
        "locale": "zh-CN",
        "observations": [],
        "plan_iterations": 1,
        "current_plan": plan,
        "final_report": "",
        "auto_accepted_plan": False,
        "enable_background_investigation": True
    }
    
    try:
        # Generate the same template state
        current_plan = state.get("current_plan")
        research_results = json.dumps([], ensure_ascii=False)
        research_thinking = ""
        user_query = "撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望"
        
        template_state = {
            **state,
            "user_query": user_query,
            "current_plan": json.dumps(current_plan.model_dump() if current_plan else {}, ensure_ascii=False),
            "research_thinking": research_thinking,
            "research_results": research_results,
            "locale": state.get("locale", "en-US")
        }
        
        messages = apply_prompt_template("outline_writer", template_state, Configuration())
        
        print(f"Generated {len(messages)} messages")
        
        # Analyze each message
        for i, msg in enumerate(messages):
            if isinstance(msg, dict):
                print(f"\nMessage {i+1} (dict):")
                print(f"  Role: {msg.get('role', 'unknown')}")
                print(f"  Content length: {len(msg.get('content', ''))}")
                print(f"  Content preview: {msg.get('content', '')[:200]}...")
            else:
                print(f"\nMessage {i+1} (LangChain):")
                print(f"  Type: {type(msg)}")
                print(f"  Content length: {len(msg.content)}")
                print(f"  Content preview: {msg.content[:200]}...")
        
        # Test with a much simpler prompt
        print("\n" + "="*50)
        print("Testing with simplified prompt...")
        
        simple_messages = [
            {"role": "system", "content": "You are a helpful assistant. Please respond in JSON format with a paper outline."},
            {"role": "user", "content": "Create an outline for a paper about AI in medical diagnosis. Include title, abstract, and sections. Respond in JSON format."}
        ]
        
        llm = get_llm_by_type("basic")
        
        print("Testing simple prompt...")
        simple_response = llm.invoke(simple_messages)
        print(f"Simple response length: {len(simple_response.content)}")
        print(f"Simple response: {simple_response.content[:300]}...")
        
        # Test with medium complexity
        print("\n" + "="*30)
        print("Testing with medium complexity prompt...")
        
        medium_messages = [
            {"role": "system", "content": "You are a professional academic writing assistant. Create detailed outlines for research papers in JSON format."},
            {"role": "user", "content": f"Create a detailed outline for: {user_query}. Respond with JSON containing title, abstract, and sections array."}
        ]
        
        medium_response = llm.invoke(medium_messages)
        print(f"Medium response length: {len(medium_response.content)}")
        print(f"Medium response: {medium_response.content[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_prompt_analysis()
    sys.exit(0 if success else 1) 