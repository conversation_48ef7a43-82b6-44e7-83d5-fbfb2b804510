#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.graph.nodes import outline_writer_node
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_fixed_outline():
    """Test the fixed outline writer node."""
    
    # Create a plan that matches the problematic scenario
    plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="人工智能在医疗诊断中应用的学术论文信息收集计划",
        thought="用户希望撰写一篇关于人工智能在医疗诊断中应用的学术论文，需要收集文献综述、技术分析和未来展望所需的信息。",
        steps=[
            Step(
                need_web_search=True,
                title="收集人工智能在医疗诊断应用的文献综述信息",
                description="收集关于人工智能在医疗诊断领域应用的现有学术文献、研究论文和综述。",
                step_type=StepType.RESEARCH,
                execution_res=None  # No execution result
            ),
            Step(
                need_web_search=True,
                title="分析人工智能在医疗诊断中的技术细节和当前发展",
                description="收集关于在医疗诊断中使用的具体AI技术的信息。",
                step_type=StepType.RESEARCH,
                execution_res=None  # No execution result
            ),
            Step(
                need_web_search=True,
                title="探讨人工智能在医疗诊断中的未来展望和影响",
                description="收集关于人工智能在医疗诊断领域未来发展趋势的信息。",
                step_type=StepType.RESEARCH,
                execution_res=None  # No execution result
            )
        ]
    )
    
    # Create state that matches the problematic scenario
    state = {
        "messages": [
            HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望"),
            AIMessage(content='{"locale": "zh-CN", "has_enough_context": false, "title": "人工智能在医疗诊断中应用的学术论文信息收集计划"}', name="planner")
        ],
        "locale": "zh-CN",
        "observations": [],
        "plan_iterations": 1,
        "current_plan": plan,
        "final_report": "",
        "auto_accepted_plan": False,
        "enable_background_investigation": True
    }
    
    # Create configuration
    config = {
        "configurable": {
            "thread_id": "test",
            "max_plan_iterations": 1,
            "max_step_num": 3,
            "max_search_results": 5,
        }
    }
    
    try:
        print("Testing fixed outline writer node...")
        
        # Set up logging to see debug messages
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger("src.graph.nodes")
        logger.setLevel(logging.DEBUG)
        
        # Call the outline writer node
        result = outline_writer_node(state, config)
        
        print(f"Result type: {type(result)}")
        print(f"Result: {result}")
        
        # Check if the result is successful
        if hasattr(result, 'update') and result.update:
            if 'paper_outline' in result.update:
                outline = result.update['paper_outline']
                print(f"Generated outline length: {len(outline)}")
                print(f"Outline preview: {outline[:200]}...")
                
                # Try to parse as JSON
                try:
                    from src.utils.json_utils import repair_json_output
                    outline_data = json.loads(repair_json_output(outline))
                    print(f"JSON parsing successful!")
                    print(f"Outline title: {outline_data.get('title', 'N/A')}")
                    print(f"Number of sections: {len(outline_data.get('sections', []))}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"JSON parsing failed: {e}")
                    return False
            else:
                print("No paper_outline in result update")
                return False
        else:
            print("No update in result or result doesn't have update attribute")
            return False
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_outline()
    sys.exit(0 if success else 1) 