#!/usr/bin/env python3
"""
RAGFlow上传诊断脚本

帮助排查上传成功但RAGFlow后台看不到文件的问题
"""

import requests
import json
import os
import io

# 配置 - 使用用户的API密钥
RAGFLOW_API_URL = "http://localhost"
RAGFLOW_API_KEY = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"
DEERFLOW_API_URL = "http://localhost:8000"

# 设置环境变量
os.environ["RAGFLOW_API_URL"] = RAGFLOW_API_URL
os.environ["RAGFLOW_API_KEY"] = RAGFLOW_API_KEY
os.environ["RAG_PROVIDER"] = "ragflow"

def test_ragflow_direct():
    """直接测试RAGFlow API连接"""
    print("🔍 测试RAGFlow直接连接")
    print("=" * 50)
    
    headers = {
        "Authorization": f"Bearer {RAGFLOW_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 测试1: 检查RAGFlow API端点
    print("1️⃣ 检查RAGFlow API端点...")
    ragflow_endpoints = [
        f"{RAGFLOW_API_URL}/api/v1/datasets",
        f"{RAGFLOW_API_URL}/v1/datasets", 
        f"{RAGFLOW_API_URL}/api/datasets"
    ]
    
    working_endpoint = None
    for endpoint in ragflow_endpoints:
        try:
            print(f"   尝试: {endpoint}")
            response = requests.get(endpoint, headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                working_endpoint = endpoint
                print(f"   ✅ 工作的端点: {endpoint}")
                result = response.json()
                print(f"   📋 返回数据: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}...")
                break
            elif response.status_code == 401:
                print(f"   ❌ 认证失败 - 请检查API密钥")
            else:
                print(f"   ❌ 状态码: {response.status_code}")
                print(f"   响应: {response.text[:200]}...")
        except Exception as e:
            print(f"   ❌ 连接错误: {e}")
    
    if not working_endpoint:
        print("\n❌ 无法找到工作的RAGFlow API端点")
        print("请检查:")
        print("1. RAGFlow服务是否在 http://localhost 运行")
        print("2. API密钥是否正确")
        print("3. RAGFlow版本和API路径")
        return None
    
    return working_endpoint.replace("/datasets", "")

def test_deerflow_ragflow_integration():
    """测试DeerFlow与RAGFlow的集成"""
    print("\n🔗 测试DeerFlow与RAGFlow集成")
    print("=" * 50)
    
    # 测试DeerFlow的RAG配置
    print("1️⃣ 检查DeerFlow RAG配置...")
    try:
        response = requests.get(f"{DEERFLOW_API_URL}/api/rag/config")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ RAG配置: {config}")
        else:
            print(f"   ❌ 配置获取失败: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ 连接DeerFlow失败: {e}")
        return
    
    # 测试DeerFlow资源列表
    print("\n2️⃣ 检查DeerFlow资源列表...")
    try:
        response = requests.get(f"{DEERFLOW_API_URL}/api/rag/resources")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            resources = response.json()
            print(f"   ✅ 找到 {len(resources.get('resources', []))} 个资源")
            for resource in resources.get('resources', [])[:3]:
                print(f"     - {resource.get('title')}: {resource.get('uri')}")
        else:
            print(f"   ❌ 资源获取失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 获取资源失败: {e}")

def test_full_upload_workflow():
    """测试完整的上传工作流程并对比RAGFlow后台"""
    print("\n📤 测试完整上传工作流")
    print("=" * 50)
    
    # 步骤1: 通过DeerFlow创建数据集
    print("1️⃣ 通过DeerFlow创建数据集...")
    dataset_payload = {
        "name": f"Debug Test Dataset {int(__import__('time').time())}",
        "description": "用于调试的测试数据集",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
    }
    
    try:
        response = requests.post(f"{DEERFLOW_API_URL}/api/rag/datasets", json=dataset_payload)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                dataset_id = result["dataset"]["id"]
                dataset_name = result["dataset"]["name"]
                print(f"   ✅ 数据集创建成功")
                print(f"     ID: {dataset_id}")
                print(f"     名称: {dataset_name}")
            else:
                print(f"   ❌ 创建失败: {result.get('message')}")
                return
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return
    
    # 步骤2: 直接检查RAGFlow后台是否有这个数据集
    print("\n2️⃣ 检查RAGFlow后台数据集...")
    headers = {
        "Authorization": f"Bearer {RAGFLOW_API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{RAGFLOW_API_URL}/api/v1/datasets", headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            datasets = result.get("data", [])
            print(f"   📋 RAGFlow中总共有 {len(datasets)} 个数据集")
            
            found_dataset = None
            for ds in datasets:
                print(f"     - {ds.get('name')} (ID: {ds.get('id')})")
                if ds.get('id') == dataset_id:
                    found_dataset = ds
                    
            if found_dataset:
                print(f"   ✅ 在RAGFlow中找到对应数据集: {found_dataset.get('name')}")
            else:
                print(f"   ❌ 在RAGFlow中未找到数据集 {dataset_id}")
                print("   这可能表明DeerFlow与RAGFlow的连接有问题")
                return
        else:
            print(f"   ❌ 获取RAGFlow数据集失败: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ 连接RAGFlow失败: {e}")
        return
    
    # 步骤3: 通过DeerFlow上传文件
    print("\n3️⃣ 通过DeerFlow上传文件...")
    test_content = """
这是一个调试测试文档，用于验证RAG文件上传功能。

关键信息：
- 测试时间: """ + str(__import__('time').time()) + """
- 文件类型: 中文文本文档
- 用途: 调试RAGFlow集成

这个文档应该出现在RAGFlow后台。
    """.strip()
    
    files = {
        'file': ('debug_test.txt', io.BytesIO(test_content.encode('utf-8')), 'text/plain')
    }
    data = {
        'dataset_id': dataset_id
    }
    
    try:
        response = requests.post(f"{DEERFLOW_API_URL}/api/rag/upload", files=files, data=data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("success"):
                print(f"   ✅ DeerFlow报告上传成功")
                documents = result.get("data", {}).get("documents", [])
                if documents:
                    doc_id = documents[0].get('id')
                    print(f"     文档ID: {doc_id}")
                else:
                    print(f"   ⚠️ 没有返回文档信息")
                    return
            else:
                print(f"   ❌ 上传失败: {result.get('message')}")
                return
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ 上传失败: {e}")
        return
    
    # 步骤4: 检查RAGFlow后台是否有这个文件
    print("\n4️⃣ 检查RAGFlow后台文档...")
    try:
        response = requests.get(f"{RAGFLOW_API_URL}/api/v1/datasets/{dataset_id}/documents", headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            documents = result.get("data", [])
            print(f"   📋 RAGFlow中该数据集有 {len(documents)} 个文档")
            
            for doc in documents:
                print(f"     - {doc.get('name')} (大小: {doc.get('size')} bytes, 状态: {doc.get('run')})")
                
            if documents:
                print(f"   ✅ 在RAGFlow后台找到了上传的文档!")
            else:
                print(f"   ❌ RAGFlow后台没有找到文档")
                print("   可能的原因:")
                print("     1. 文件上传没有真正到达RAGFlow")
                print("     2. RAGFlow的文档存储有延迟")
                print("     3. API路径或方法有问题")
        else:
            print(f"   ❌ 获取文档列表失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 检查文档失败: {e}")
    
    print(f"\n📋 调试信息总结:")
    print(f"   数据集ID: {dataset_id}")
    print(f"   数据集名称: {dataset_name}")
    print(f"   RAGFlow URL: {RAGFLOW_API_URL}")
    print(f"   请在RAGFlow后台搜索这个数据集名称")

def main():
    """主诊断流程"""
    print("🚀 RAGFlow上传问题诊断工具")
    print("=" * 60)
    
    # 1. 测试RAGFlow直接连接
    ragflow_base = test_ragflow_direct()
    
    # 2. 测试DeerFlow集成
    test_deerflow_ragflow_integration()
    
    # 3. 测试完整工作流
    test_full_upload_workflow()
    
    print("\n" + "=" * 60)
    print("🔧 故障排除建议:")
    print("1. 确认RAGFlow在 http://localhost 正常运行")
    print("2. 确认API密钥有正确的权限")
    print("3. 检查RAGFlow版本是否支持所用的API")
    print("4. 查看DeerFlow和RAGFlow的日志")

if __name__ == "__main__":
    main() 