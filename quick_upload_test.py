#!/usr/bin/env python3
"""
Quick File Upload Test

简化的文件上传测试，确保基本功能正常工作
"""

import requests
import json
import os
import io

# 配置
DEERFLOW_API_URL = "http://localhost:8000"

# 请在这里设置您的实际API密钥
RAGFLOW_API_KEY = "ragflow-your-api-key"  # 请替换为您的实际API密钥

# 设置环境变量
os.environ["RAGFLOW_API_URL"] = "http://localhost"
os.environ["RAGFLOW_API_KEY"] = RAGFLOW_API_KEY
os.environ["RAG_PROVIDER"] = "ragflow"

def test_basic_upload():
    """测试基本的文件上传功能"""
    print("🚀 Quick Upload Test")
    print("=" * 40)
    
    # 步骤1: 创建数据集
    print("1️⃣ Creating dataset...")
    dataset_payload = {
        "name": "Quick Test Dataset",
        "description": "Quick test for file upload",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
    }
    
    try:
        response = requests.post(f"{DEERFLOW_API_URL}/api/rag/datasets", json=dataset_payload)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                dataset_id = result["dataset"]["id"]
                print(f"✅ Dataset created: {dataset_id}")
            else:
                print(f"❌ Dataset creation failed: {result.get('message')}")
                return
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return
    
    # 步骤2: 创建测试文件
    print("\n2️⃣ Creating test file...")
    test_content = """
    这是一个测试文档，用于验证RAG文件上传功能。
    
    内容包括：
    - 人工智能基础知识
    - 机器学习算法
    - 深度学习应用
    
    这些内容应该能够被索引和搜索。
    """.strip()
    
    # 步骤3: 上传文件
    print("\n3️⃣ Uploading file...")
    files = {
        'file': ('test_document.txt', io.StringIO(test_content), 'text/plain')
    }
    data = {
        'dataset_id': dataset_id
    }
    
    try:
        response = requests.post(f"{DEERFLOW_API_URL}/api/rag/upload", files=files, data=data)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ File uploaded successfully!")
                documents = result.get("data", {}).get("documents", [])
                if documents:
                    doc = documents[0]
                    print(f"   📄 Document ID: {doc.get('id')}")
                    print(f"   📝 Filename: {doc.get('name')}")
                    print(f"   📏 Size: {doc.get('size')} bytes")
                    document_id = doc.get('id')
                else:
                    print("⚠️ No document info returned")
                    return
            else:
                print(f"❌ Upload failed: {result.get('message')}")
                return
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return
    
    # 步骤4: 列出文档
    print("\n4️⃣ Listing documents...")
    try:
        response = requests.get(f"{DEERFLOW_API_URL}/api/rag/datasets/{dataset_id}/documents")
        if response.status_code == 200:
            result = response.json()
            documents = result.get("documents", [])
            print(f"✅ Found {len(documents)} documents")
            for doc in documents:
                print(f"   📄 {doc.get('name')} - Status: {doc.get('run')}")
        else:
            print(f"❌ List failed: {response.status_code}")
    except Exception as e:
        print(f"❌ List failed: {e}")
    
    # 步骤5: 启动解析
    print("\n5️⃣ Starting document parsing...")
    parse_payload = {
        "dataset_id": dataset_id,
        "document_ids": [document_id]
    }
    
    try:
        response = requests.post(f"{DEERFLOW_API_URL}/api/rag/documents/parse", json=parse_payload)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Parsing started successfully!")
            else:
                print(f"❌ Parse failed: {result.get('message')}")
        else:
            print(f"❌ Parse HTTP Error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Parse failed: {e}")
    
    print("\n🎉 Quick test completed!")
    print(f"📋 Dataset ID: {dataset_id}")
    print("\n🔧 Use this dataset ID in Postman for further testing")

if __name__ == "__main__":
    print("⚠️ 请先在脚本中设置您的实际 RAGFLOW_API_KEY")
    print("然后运行: python quick_upload_test.py")
    print()
    test_basic_upload() 