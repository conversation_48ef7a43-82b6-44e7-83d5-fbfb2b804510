#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.graph.nodes import planner_node, human_feedback_node, research_team_node
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig

def test_state_preservation():
    """Test that paper_writing_mode is preserved across all node transitions."""
    
    print("Testing state preservation across node transitions...")
    
    # Test 1: planner_node with has_enough_context=True should preserve paper_writing_mode
    print("\nTest 1: planner_node with has_enough_context=True")
    
    state_with_paper_mode = {
        "messages": [HumanMessage(content="基于AI的写作工具分析")],
        "locale": "zh-CN",
        "plan_iterations": 0,
        "paper_writing_mode": True,  # This should be preserved
    }
    
    config = RunnableConfig(configurable={"max_plan_iterations": 3})
    
    # Mock the planner to return a plan with has_enough_context=True
    import unittest.mock
    
    mock_plan = {
        "locale": "zh-CN",
        "has_enough_context": True,
        "title": "测试计划",
        "thought": "测试思路",
        "steps": []
    }
    
    with unittest.mock.patch('src.graph.nodes.get_llm_by_type') as mock_llm:
        mock_response = unittest.mock.MagicMock()
        mock_response.model_dump_json.return_value = json.dumps(mock_plan, ensure_ascii=False)
        mock_llm.return_value.with_structured_output.return_value.invoke.return_value = mock_response
        
        result = planner_node(state_with_paper_mode, config)
    
    print(f"planner_node result: {result}")
    print(f"paper_writing_mode preserved: {result.update.get('paper_writing_mode', 'NOT FOUND')}")
    print(f"goto: {result.goto}")
    
    assert result.update.get("paper_writing_mode") == True, f"Expected paper_writing_mode=True, got {result.update.get('paper_writing_mode')}"
    assert result.goto == "reporter", f"Expected 'reporter', got '{result.goto}'"
    print("✅ planner_node (has_enough_context=True) test passed")
    
    # Test 2: planner_node with has_enough_context=False should preserve paper_writing_mode
    print("\nTest 2: planner_node with has_enough_context=False")
    
    mock_plan_incomplete = {
        "locale": "zh-CN",
        "has_enough_context": False,
        "title": "测试计划",
        "thought": "测试思路",
        "steps": []
    }
    
    with unittest.mock.patch('src.graph.nodes.get_llm_by_type') as mock_llm:
        mock_response = unittest.mock.MagicMock()
        mock_response.model_dump_json.return_value = json.dumps(mock_plan_incomplete, ensure_ascii=False)
        mock_llm.return_value.with_structured_output.return_value.invoke.return_value = mock_response
        
        result2 = planner_node(state_with_paper_mode, config)
    
    print(f"planner_node result: {result2}")
    print(f"paper_writing_mode preserved: {result2.update.get('paper_writing_mode', 'NOT FOUND')}")
    print(f"goto: {result2.goto}")
    
    assert result2.update.get("paper_writing_mode") == True, f"Expected paper_writing_mode=True, got {result2.update.get('paper_writing_mode')}"
    assert result2.goto == "human_feedback", f"Expected 'human_feedback', got '{result2.goto}'"
    print("✅ planner_node (has_enough_context=False) test passed")
    
    # Test 3: human_feedback_node with [ACCEPTED] should preserve paper_writing_mode
    print("\nTest 3: human_feedback_node with [ACCEPTED]")
    
    state_with_plan = {
        "messages": [HumanMessage(content="基于AI的写作工具分析")],
        "locale": "zh-CN",
        "current_plan": json.dumps(mock_plan_incomplete, ensure_ascii=False),
        "plan_iterations": 0,
        "paper_writing_mode": True,
        "auto_accepted_plan": False
    }
    
    with unittest.mock.patch('src.graph.nodes.interrupt', return_value="[ACCEPTED]"):
        result3 = human_feedback_node(state_with_plan)
    
    print(f"human_feedback_node result: {result3}")
    print(f"paper_writing_mode preserved: {result3.update.get('paper_writing_mode', 'NOT FOUND')}")
    print(f"goto: {result3.goto}")
    
    assert result3.update.get("paper_writing_mode") == True, f"Expected paper_writing_mode=True, got {result3.update.get('paper_writing_mode')}"
    print("✅ human_feedback_node ([ACCEPTED]) test passed")
    
    print("\nAll state preservation tests passed! 🎉")
    return True

if __name__ == "__main__":
    try:
        success = test_state_preservation()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 