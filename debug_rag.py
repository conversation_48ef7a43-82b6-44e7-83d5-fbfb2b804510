#!/usr/bin/env python3
"""
Debug RAG Connection

This script tests RAG connection and initialization separately from the server.
"""

import os
import traceback
from src.rag.builder import build_retriever
from src.config.tools import SELECTED_RAG_PROVIDER

def main():
    print("🔍 Debugging RAG Connection")
    print("=" * 40)
    
    # Check environment variables
    print(f"RAG_PROVIDER: {os.getenv('RAG_PROVIDER')}")
    print(f"SELECTED_RAG_PROVIDER: {SELECTED_RAG_PROVIDER}")
    print(f"RAGFLOW_API_URL: {os.getenv('RAGFLOW_API_URL')}")
    print(f"RAGFLOW_API_KEY: {os.getenv('RAGFLOW_API_KEY', 'NOT_SET')[:20]}...")
    print()
    
    try:
        print("🔧 Building retriever...")
        retriever = build_retriever()
        
        if retriever is None:
            print("❌ Retriever is None - no provider selected or not configured")
            return
        
        print(f"✅ Retriever built successfully: {type(retriever).__name__}")
        print()
        
        # Test list_resources method
        print("📋 Testing list_resources...")
        try:
            resources = retriever.list_resources()
            print(f"✅ Found {len(resources)} resources")
            for i, resource in enumerate(resources[:3]):
                print(f"   {i+1}. {resource.title}: {resource.description}")
        except Exception as e:
            print(f"❌ Error in list_resources: {e}")
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Error building retriever: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main() 