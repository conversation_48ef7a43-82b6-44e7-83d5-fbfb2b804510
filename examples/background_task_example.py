#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
后台任务功能使用示例

这个示例展示了如何使用新的后台任务功能，确保即使客户端断开连接，
research graph 也能继续在后台运行。
"""

import requests
import time
import json

# 服务器配置
BASE_URL = "http://localhost:8000"
ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"  # 需要替换为真实的token

headers = {
    'Authorization': f'Bearer {ACCESS_TOKEN}',
    'Content-Type': 'application/json'
}

def start_background_task():
    """启动后台任务"""
    url = f"{BASE_URL}/api/chat/background"
    
    data = {
        "messages": [
            {
                "role": "user",
                "content": "请帮我研究人工智能在医疗领域的最新应用，并写一份详细的报告"
            }
        ],
        "thread_id": "__default__",
        "resources": [],
        "max_plan_iterations": 3,
        "max_step_num": 20,
        "max_search_results": 10,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": True
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 后台任务启动成功!")
            print(f"   任务ID: {result['task_id']}")
            print(f"   线程ID: {result['thread_id']}")
            return result['task_id'], result['thread_id']
        else:
            print(f"❌ 启动失败: {response.status_code} - {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None, None


def check_task_status(task_id):
    """检查任务状态"""
    url = f"{BASE_URL}/api/tasks/{task_id}"
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            result = response.json()
            task = result['task']
            print(f"📊 任务状态: {task['status']}")
            print(f"   进度: {task['progress']}%")
            print(f"   当前步骤: {task['current_step']}")
            if task['result_document_id']:
                print(f"   结果文档ID: {task['result_document_id']}")
            return task
        else:
            print(f"❌ 查询失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None


def get_user_tasks():
    """获取用户所有任务列表"""
    url = f"{BASE_URL}/api/tasks"
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            result = response.json()
            tasks = result['tasks']
            print(f"📋 您的任务列表 (共 {len(tasks)} 个):")
            for task in tasks:
                status_emoji = {
                    'pending': '⏳',
                    'running': '🔄',
                    'completed': '✅',
                    'failed': '❌'
                }.get(task['status'], '❓')
                print(f"   {status_emoji} {task['task_id'][:8]}... - {task['status']} ({task['progress']}%)")
            return tasks
        else:
            print(f"❌ 查询失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []


def get_document_content(document_id):
    """获取文档内容"""
    url = f"{BASE_URL}/api/files/{document_id}"
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"📄 文档内容预览:")
            content = result['content']
            print(f"   长度: {len(content)} 字符")
            print(f"   前200字符: {content[:200]}...")
            return content
        else:
            print(f"❌ 获取文档失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None


def monitor_task_until_completion(task_id, max_wait_time=1800):  # 最多等待30分钟
    """监控任务直到完成"""
    print(f"🔍 开始监控任务: {task_id}")
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        task = check_task_status(task_id)
        if not task:
            break
        
        status = task['status']
        if status == 'completed':
            print(f"🎉 任务完成!")
            if task['result_document_id']:
                print(f"📄 开始获取结果文档...")
                get_document_content(task['result_document_id'])
            break
        elif status == 'failed':
            print(f"💥 任务失败: {task.get('error_message', '未知错误')}")
            break
        elif status in ['pending', 'running']:
            print(f"⏱️  任务进行中，等待10秒后重新检查...")
            time.sleep(10)
        else:
            print(f"❓ 未知状态: {status}")
            break
    else:
        print(f"⏰ 监控超时 ({max_wait_time}秒)，任务可能仍在后台运行")


def main():
    """主函数"""
    print("🚀 后台任务功能演示")
    print("=" * 50)
    
    print("\n📋 当前任务列表:")
    get_user_tasks()
    
    print("\n🎯 启动新的后台任务:")
    task_id, thread_id = start_background_task()
    
    if task_id:
        print(f"\n🔍 监控任务执行:")
        monitor_task_until_completion(task_id)
        
        print(f"\n📋 更新后的任务列表:")
        get_user_tasks()
    
    print("\n✨ 演示完成!")


if __name__ == "__main__":
    print("⚠️  注意: 运行此示例前请确保:")
    print("1. 服务器已启动 (python server.py)")
    print("2. 已获取有效的认证token")
    print("3. 已将 YOUR_ACCESS_TOKEN 替换为真实token")
    print("4. 用户有足够的积分")
    print("-" * 50)
    
    # 取消注释下面这行来运行演示
    # main()
    
    print("如需运行演示，请取消注释 main() 函数调用") 