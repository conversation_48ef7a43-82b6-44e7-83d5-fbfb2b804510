#!/usr/bin/env python3
"""
RAGFlow Upload Example

This script demonstrates how to use DeerFlow's RAGFlow integration
to upload documents to RAGFlow datasets.

Prerequisites:
1. Set RAGFLOW_API_URL environment variable
2. Set RAGFLOW_API_KEY environment variable
3. Have a running DeerFlow server

Usage:
    python examples/ragflow_upload_example.py
"""

import os
import requests
import json
import base64
from pathlib import Path

# DeerFlow server configuration
DEERFLOW_API_URL = "http://localhost:8000"

def upload_file_to_base64(file_path: str) -> str:
    """Convert a file to base64 string."""
    with open(file_path, 'rb') as f:
        content = f.read()
    return base64.b64encode(content).decode('utf-8')

def create_dataset():
    """Create a new dataset in RAGFlow."""
    print("🔧 Creating a new dataset...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/datasets"
    payload = {
        "name": "DeerFlow Test Dataset",
        "description": "A test dataset created via DeerFlow API",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-zh-v1.5@BAAI"
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            dataset = result.get("dataset", {})
            dataset_id = dataset.get("id")
            print(f"✅ Dataset created successfully!")
            print(f"   Dataset ID: {dataset_id}")
            print(f"   Dataset Name: {dataset.get('name')}")
            return dataset_id
        else:
            print(f"❌ Failed to create dataset: {result.get('message')}")
            return None
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return None

def upload_documents(dataset_id: str):
    """Upload sample documents to the dataset."""
    print("📄 Uploading documents...")
    
    # Create sample documents
    sample_docs = [
        {
            "name": "sample_doc1.txt",
            "content": "This is a sample document about artificial intelligence. AI is transforming various industries including healthcare, finance, and transportation."
        },
        {
            "name": "sample_doc2.txt", 
            "content": "DeerFlow is an AI-powered research assistant that helps users conduct comprehensive research efficiently. It can analyze documents, generate reports, and provide insights."
        },
        {
            "name": "sample_doc3.md",
            "content": "# RAGFlow Integration\n\nRAGFlow is a powerful document management and retrieval system. It supports:\n\n- Document upload and parsing\n- Vector embeddings\n- Semantic search\n- Question answering"
        }
    ]
    
    # Convert content to bytes and prepare files
    files = []
    for doc in sample_docs:
        content_bytes = doc["content"].encode('utf-8')
        files.append({
            "name": doc["name"],
            "content": content_bytes
        })
    
    url = f"{DEERFLOW_API_URL}/api/rag/upload"
    payload = {
        "dataset_id": dataset_id,
        "files": files
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print("✅ Documents uploaded successfully!")
            data = result.get("data", [])
            for i, doc_info in enumerate(data):
                print(f"   Document {i+1}: {doc_info.get('name')} (ID: {doc_info.get('id')})")
            return [doc.get('id') for doc in data]
        else:
            print(f"❌ Failed to upload documents: {result.get('message')}")
            return []
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return []

def list_documents(dataset_id: str):
    """List documents in the dataset."""
    print("📋 Listing documents in dataset...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/datasets/{dataset_id}/documents"
    params = {"page": 1, "page_size": 10}
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        result = response.json()
        documents = result.get("documents", [])
        print(f"✅ Found {len(documents)} documents:")
        for doc in documents:
            print(f"   - {doc.get('name')} (Status: {doc.get('run', 'Unknown')})")
        return documents
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return []

def parse_documents(dataset_id: str, document_ids: list):
    """Start parsing the uploaded documents."""
    print("⚙️ Starting document parsing...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/documents/parse"
    payload = {
        "dataset_id": dataset_id,
        "document_ids": document_ids
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print("✅ Document parsing started successfully!")
            print("   Note: Parsing may take a few moments to complete.")
            return True
        else:
            print(f"❌ Failed to start parsing: {result.get('message')}")
            return False
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return False

def test_retrieval(dataset_id: str):
    """Test document retrieval from the dataset."""
    print("🔍 Testing document retrieval...")
    
    # We'll use the RAG resources endpoint to test retrieval
    url = f"{DEERFLOW_API_URL}/api/rag/resources"
    params = {"query": "DeerFlow"}
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        result = response.json()
        resources = result.get("resources", [])
        print(f"✅ Found {len(resources)} resources matching 'DeerFlow'")
        for resource in resources:
            print(f"   - {resource.get('title')}: {resource.get('description')}")
        return True
    else:
        print(f"❌ API Error: {response.status_code} - {response.text}")
        return False

def main():
    """Main function to demonstrate RAGFlow upload workflow."""
    print("🚀 DeerFlow RAGFlow Upload Example")
    print("=" * 50)
    
    # Check if environment variables are set
    if not os.getenv("RAGFLOW_API_URL"):
        print("❌ Please set RAGFLOW_API_URL environment variable")
        return
    
    if not os.getenv("RAGFLOW_API_KEY"):
        print("❌ Please set RAGFLOW_API_KEY environment variable")
        return
    
    print(f"🌐 DeerFlow API: {DEERFLOW_API_URL}")
    print(f"🔗 RAGFlow API: {os.getenv('RAGFLOW_API_URL')}")
    print()
    
    # Step 1: Create dataset
    dataset_id = create_dataset()
    if not dataset_id:
        return
    
    print()
    
    # Step 2: Upload documents
    document_ids = upload_documents(dataset_id)
    if not document_ids:
        return
    
    print()
    
    # Step 3: List documents
    list_documents(dataset_id)
    print()
    
    # Step 4: Start parsing
    if parse_documents(dataset_id, document_ids):
        print()
        print("⏳ Waiting for parsing to complete...")
        print("   You can check the document status via the list documents endpoint.")
        print()
    
    # Step 5: Test retrieval (optional)
    test_retrieval(dataset_id)
    
    print()
    print("🎉 RAGFlow upload example completed!")
    print(f"📋 Dataset ID for future reference: {dataset_id}")
    print()
    print("Next steps:")
    print("1. Wait for document parsing to complete")
    print("2. Use the dataset in your research workflows")
    print("3. Query documents via the chat interface")

if __name__ == "__main__":
    main() 