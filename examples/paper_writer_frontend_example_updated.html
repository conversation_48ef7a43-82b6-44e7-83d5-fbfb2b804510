<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paper Writer Frontend Example - Updated</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .section-content {
            margin: 15px 0;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .section-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-complete {
            background: #4CAF50;
            color: white;
        }
        .status-writing {
            background: #FF9800;
            color: white;
        }
        .status-pending {
            background: #9E9E9E;
            color: white;
        }
        .streaming-content {
            margin: 10px 0;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .event-log {
            margin-top: 20px;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        .event-item {
            margin: 5px 0;
            padding: 8px;
            background: white;
            border-radius: 3px;
            border-left: 3px solid #2196F3;
            font-size: 12px;
        }
        .event-complete {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        .button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1976D2;
        }
        .metadata-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Paper Writer Frontend Example - Updated</h1>
        <p>This example demonstrates handling the new paper_writer completion message format with streaming support.</p>
        
        <div class="progress-section">
            <h3>Writing Progress</h3>
            <div id="progress-info">
                <span id="current-section">Section: Not started</span>
                <span id="section-count" style="float: right;">0 / 0 sections</span>
            </div>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>

        <div id="sections-container">
            <!-- Sections will be populated here -->
        </div>

        <div>
            <button class="button" onclick="simulateStreaming()">Simulate Streaming</button>
            <button class="button" onclick="simulateCompletion()">Simulate Completion</button>
            <button class="button" onclick="clearAll()">Clear All</button>
        </div>

        <div class="event-log">
            <h4>Event Log</h4>
            <div id="event-log"></div>
        </div>
    </div>

    <script>
        class PaperWriterHandler {
            constructor() {
                this.sections = [];
                this.currentSectionIndex = 0;
                this.totalSections = 0;
                this.isStreaming = false;
                this.currentStreamingContent = '';
            }

            // Handle streaming content during writing
            handleStreamingChunk(eventData) {
                if (eventData.agent === 'paper_writer' && eventData.content) {
                    this.currentStreamingContent += eventData.content;
                    this.updateStreamingDisplay();
                    this.logEvent('Streaming chunk received', eventData);
                }
            }

            // Handle completion message with new format
            handleCompletionMessage(eventData) {
                if (eventData.is_complete_response && eventData.agent === 'paper_writer') {
                    const metadata = eventData.section_metadata;
                    
                    this.logEvent('Section completed', eventData, true);
                    
                    // Update section data
                    this.sections[metadata.section_index] = {
                        title: metadata.section_title,
                        content: eventData.content,
                        status: 'complete',
                        index: metadata.section_index
                    };
                    
                    this.totalSections = metadata.total_sections;
                    this.currentSectionIndex = metadata.section_index + 1;
                    this.isStreaming = false;
                    this.currentStreamingContent = '';
                    
                    // Update UI
                    this.updateProgress();
                    this.updateSectionsDisplay();
                    this.updateProgressInfo();
                }
            }

            // Handle general message processing
            processMessage(eventData) {
                // Check if this is a completion message first
                if (eventData.is_complete_response && eventData.response_type === 'section_content') {
                    this.handleCompletionMessage(eventData);
                } else if (eventData.agent === 'paper_writer' && eventData.content) {
                    // Handle streaming content
                    this.handleStreamingChunk(eventData);
                }
            }

            updateProgress() {
                const progressPercent = this.totalSections > 0 
                    ? (this.currentSectionIndex / this.totalSections) * 100 
                    : 0;
                
                document.getElementById('progress-fill').style.width = `${progressPercent}%`;
            }

            updateProgressInfo() {
                const currentText = this.currentSectionIndex < this.totalSections 
                    ? `Writing: Section ${this.currentSectionIndex + 1}` 
                    : 'Writing Complete';
                
                document.getElementById('current-section').textContent = currentText;
                document.getElementById('section-count').textContent = 
                    `${this.currentSectionIndex} / ${this.totalSections} sections`;
            }

            updateSectionsDisplay() {
                const container = document.getElementById('sections-container');
                container.innerHTML = '';

                // Create section displays
                for (let i = 0; i < Math.max(this.totalSections, this.sections.length); i++) {
                    const section = this.sections[i];
                    const sectionDiv = document.createElement('div');
                    sectionDiv.className = 'section-content';
                    
                    let status, statusClass;
                    if (section && section.status === 'complete') {
                        status = 'Complete';
                        statusClass = 'status-complete';
                    } else if (i === this.currentSectionIndex && this.isStreaming) {
                        status = 'Writing...';
                        statusClass = 'status-writing';
                    } else {
                        status = 'Pending';
                        statusClass = 'status-pending';
                    }

                    sectionDiv.innerHTML = `
                        <div class="section-header">
                            <span>Section ${i + 1}: ${section ? section.title : 'TBD'}</span>
                            <span class="section-status ${statusClass}">${status}</span>
                        </div>
                        <div class="streaming-content" id="section-content-${i}">
                            ${section ? section.content : (i === this.currentSectionIndex && this.isStreaming ? this.currentStreamingContent : 'Not started')}
                        </div>
                    `;
                    
                    container.appendChild(sectionDiv);
                }
            }

            updateStreamingDisplay() {
                if (this.isStreaming) {
                    const contentElement = document.getElementById(`section-content-${this.currentSectionIndex}`);
                    if (contentElement) {
                        contentElement.textContent = this.currentStreamingContent;
                    }
                }
            }

            logEvent(message, eventData, isComplete = false) {
                const logContainer = document.getElementById('event-log');
                const eventDiv = document.createElement('div');
                eventDiv.className = `event-item ${isComplete ? 'event-complete' : ''}`;
                
                const timestamp = new Date().toLocaleTimeString();
                const metadata = eventData.section_metadata || {};
                
                eventDiv.innerHTML = `
                    <strong>[${timestamp}]</strong> ${message}<br>
                    <div class="metadata-display">
                        Agent: ${eventData.agent || 'N/A'}<br>
                        ${isComplete ? `Section: ${metadata.section_index + 1}/${metadata.total_sections} - "${metadata.section_title}"<br>` : ''}
                        Content Length: ${eventData.content ? eventData.content.length : 0}<br>
                        ${isComplete ? 'Complete Response: Yes' : 'Streaming: Yes'}
                    </div>
                `;
                
                logContainer.insertBefore(eventDiv, logContainer.firstChild);
                
                // Keep only last 10 events
                while (logContainer.children.length > 10) {
                    logContainer.removeChild(logContainer.lastChild);
                }
            }

            // Simulation methods for testing
            startStreaming(sectionIndex, sectionTitle, totalSections) {
                this.currentSectionIndex = sectionIndex;
                this.totalSections = totalSections;
                this.isStreaming = true;
                this.currentStreamingContent = '';
                this.updateSectionsDisplay();
                this.updateProgressInfo();
            }

            clear() {
                this.sections = [];
                this.currentSectionIndex = 0;
                this.totalSections = 0;
                this.isStreaming = false;
                this.currentStreamingContent = '';
                this.updateSectionsDisplay();
                this.updateProgress();
                this.updateProgressInfo();
                document.getElementById('event-log').innerHTML = '';
            }
        }

        // Initialize handler
        const paperWriter = new PaperWriterHandler();

        // Simulation functions
        function simulateStreaming() {
            paperWriter.startStreaming(0, "Introduction", 3);
            
            // Simulate streaming chunks
            const chunks = [
                "# Introduction\n\n",
                "The field of Artificial Intelligence has ",
                "witnessed remarkable growth in recent years. ",
                "This paper explores the current trends and ",
                "future implications of AI development...\n\n",
                "## Background\n\n",
                "AI research has evolved significantly since..."
            ];
            
            let chunkIndex = 0;
            const streamInterval = setInterval(() => {
                if (chunkIndex < chunks.length) {
                    paperWriter.processMessage({
                        agent: 'paper_writer',
                        content: chunks[chunkIndex],
                        thread_id: 'test_thread'
                    });
                    chunkIndex++;
                } else {
                    clearInterval(streamInterval);
                    
                    // Simulate completion after streaming
                    setTimeout(() => {
                        simulateCompletion();
                    }, 1000);
                }
            }, 200);
        }

        function simulateCompletion() {
            const fullContent = `# Introduction

The field of Artificial Intelligence has witnessed remarkable growth in recent years. This paper explores the current trends and future implications of AI development.

## Background

AI research has evolved significantly since its inception, with breakthrough developments in machine learning, neural networks, and deep learning architectures.

## Research Objectives

This study aims to analyze the current state of AI technology and its potential impact on various industries and society as a whole.`;

            paperWriter.processMessage({
                agent: 'paper_writer',
                content: fullContent,
                is_complete_response: true,
                response_type: 'section_content',
                section_metadata: {
                    section_index: 0,
                    section_title: 'Introduction',
                    total_sections: 3
                },
                thread_id: 'test_thread'
            });
        }

        function clearAll() {
            paperWriter.clear();
        }

        // Initialize display
        paperWriter.updateSectionsDisplay();
        paperWriter.updateProgress();
        paperWriter.updateProgressInfo();
    </script>
</body>
</html> 