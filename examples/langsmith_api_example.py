#!/usr/bin/env python3
"""
LangSmith API 使用示例脚本

此脚本演示如何使用 DeerFlow 的 LangSmith API 端点来查询任务历史记录和 token 使用信息。

使用方法:
    python examples/langsmith_api_example.py

确保在运行之前：
1. DeerFlow API 服务器正在运行 (python server.py)
2. 正确配置了 LangSmith 环境变量
"""

import requests
import json
import sys
from datetime import datetime, timedelta


class LangSmithAPIClient:
    """LangSmith API 客户端类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_prefix = f"{base_url}/api/langsmith"
    
    def check_status(self):
        """检查 LangSmith 集成状态"""
        try:
            response = requests.get(f"{self.api_prefix}/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Error checking LangSmith status: {e}")
            return None
    
    def get_recent_traces(self, hours=24, limit=10):
        """获取最近的任务追踪记录"""
        try:
            params = {"hours": hours, "limit": limit}
            response = requests.get(f"{self.api_prefix}/traces", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Error fetching recent traces: {e}")
            return []
    
    def get_task_detail(self, thread_id):
        """获取特定任务的详细信息"""
        try:
            response = requests.get(f"{self.api_prefix}/traces/{thread_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Error fetching task detail for {thread_id}: {e}")
            return None
    
    def get_token_usage_summary(self, days=7):
        """获取 token 使用统计"""
        try:
            params = {"days": days}
            response = requests.get(f"{self.api_prefix}/token-usage/summary", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Error fetching token usage summary: {e}")
            return None
    
    def get_recent_activity(self, hours=24):
        """获取最近活动概览"""
        try:
            params = {"hours": hours}
            response = requests.get(f"{self.api_prefix}/recent-activity", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Error fetching recent activity: {e}")
            return None


def print_section(title):
    """打印分节标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)


def print_json(data, max_items=None):
    """格式化打印 JSON 数据"""
    if isinstance(data, list) and max_items:
        data = data[:max_items]
    print(json.dumps(data, indent=2, ensure_ascii=False))


def format_datetime(iso_string):
    """格式化日期时间字符串"""
    if not iso_string:
        return "N/A"
    try:
        dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return iso_string


def format_currency(amount):
    """格式化货币金额"""
    if amount is None:
        return "N/A"
    return f"${amount:.4f}"


def main():
    """主函数"""
    print("🚀 DeerFlow LangSmith API 示例")
    print("=" * 60)
    
    # 初始化客户端
    client = LangSmithAPIClient()
    
    # 1. 检查状态
    print_section("1. 检查 LangSmith 集成状态")
    status = client.check_status()
    if status:
        print(f"✅ LangSmith 集成状态:")
        print(f"   - 可用: {'是' if status.get('available') else '否'}")
        print(f"   - 项目名称: {status.get('project_name', 'N/A')}")
        print(f"   - 客户端配置: {'是' if status.get('client_configured') else '否'}")
        
        if not status.get('available'):
            print("\n❌ LangSmith 集成不可用，请检查配置!")
            print("确保 .env 文件包含以下配置:")
            print("   LANGSMITH_TRACING=true")
            print("   LANGSMITH_API_KEY=your_api_key")
            print("   LANGSMITH_PROJECT=your_project_name")
            sys.exit(1)
    else:
        print("❌ 无法连接到 LangSmith API")
        sys.exit(1)
    
    # 2. 获取最近活动概览
    print_section("2. 最近 24 小时活动概览")
    activity = client.get_recent_activity(hours=24)
    if activity:
        summary = activity.get('summary', {})
        print(f"📊 活动统计:")
        print(f"   - 总任务数: {summary.get('total_traces', 0)}")
        print(f"   - 完成任务: {summary.get('completed_traces', 0)}")
        print(f"   - 失败任务: {summary.get('failed_traces', 0)}")
        print(f"   - 运行中任务: {summary.get('running_traces', 0)}")
        print(f"   - 成功率: {summary.get('success_rate', 0):.1%}")
        print(f"   - 总 token 数: {summary.get('total_tokens', 0):,}")
        print(f"   - 总费用: {format_currency(summary.get('total_cost_usd'))}")
        
        # 显示最近的任务
        recent_traces = activity.get('recent_traces', [])
        if recent_traces:
            print(f"\n📝 最近 {len(recent_traces)} 个任务:")
            for i, trace in enumerate(recent_traces[:5], 1):  # 只显示前5个
                print(f"   {i}. {trace.get('thread_id', 'N/A')[:12]}... - "
                      f"{trace.get('status', 'N/A')} - "
                      f"{trace.get('total_tokens', {}).get('total_tokens', 0)} tokens")
    else:
        print("❌ 无法获取最近活动数据")
    
    # 3. 获取任务追踪记录
    print_section("3. 最近 48 小时任务追踪记录")
    traces = client.get_recent_traces(hours=48, limit=5)
    if traces:
        print(f"📋 找到 {len(traces)} 个任务追踪记录:")
        for i, trace in enumerate(traces, 1):
            print(f"\n   任务 {i}:")
            print(f"     - Thread ID: {trace.get('thread_id', 'N/A')}")
            print(f"     - 状态: {trace.get('status', 'N/A')}")
            print(f"     - 开始时间: {format_datetime(trace.get('start_time'))}")
            print(f"     - 结束时间: {format_datetime(trace.get('end_time'))}")
            print(f"     - 运行步骤: {trace.get('total_runs', 0)}")
            tokens = trace.get('total_tokens', {})
            print(f"     - Token 使用: {tokens.get('total_tokens', 0)} "
                  f"(输入: {tokens.get('prompt_tokens', 0)}, "
                  f"输出: {tokens.get('completion_tokens', 0)})")
            print(f"     - 费用: {format_currency(trace.get('total_cost_usd'))}")
    else:
        print("❌ 没有找到最近的任务追踪记录")
    
    # 4. 获取 token 使用统计
    print_section("4. 最近 7 天 Token 使用统计")
    usage_summary = client.get_token_usage_summary(days=7)
    if usage_summary:
        period = usage_summary.get('period', {})
        summary = usage_summary.get('summary', {})
        
        print(f"📈 统计期间: {format_datetime(period.get('start_time'))} 至 "
              f"{format_datetime(period.get('end_time'))}")
        print(f"   ({period.get('duration_hours', 0):.1f} 小时)")
        
        print(f"\n💰 总体统计:")
        print(f"   - 总任务数: {summary.get('total_traces', 0)}")
        print(f"   - 成功率: {summary.get('success_rate', 0):.1%}")
        print(f"   - 总 token 数: {summary.get('total_tokens', 0):,}")
        print(f"     * 输入 tokens: {summary.get('total_prompt_tokens', 0):,}")
        print(f"     * 输出 tokens: {summary.get('total_completion_tokens', 0):,}")
        print(f"   - 平均每任务 tokens: {summary.get('avg_tokens_per_trace', 0):.0f}")
        print(f"   - 总费用: {format_currency(summary.get('total_cost_usd'))}")
        
        # 显示模型使用情况
        model_usage = usage_summary.get('model_usage', {})
        if model_usage:
            print(f"\n🤖 按模型统计 (前3名):")
            sorted_models = sorted(model_usage.items(), 
                                 key=lambda x: x[1].get('total_tokens', 0), 
                                 reverse=True)
            for model, stats in sorted_models[:3]:
                print(f"   - {model}:")
                print(f"     * 调用次数: {stats.get('calls', 0)}")
                print(f"     * Token 总数: {stats.get('prompt_tokens', 0) + stats.get('completion_tokens', 0):,}")
                print(f"     * 费用: {format_currency(stats.get('cost_usd'))}")
    else:
        print("❌ 无法获取 token 使用统计")
    
    # 5. 获取具体任务详情（如果有任务的话）
    if traces and len(traces) > 0:
        print_section("5. 任务详细信息示例")
        first_trace = traces[0]
        thread_id = first_trace.get('thread_id')
        
        if thread_id:
            print(f"🔍 获取任务 {thread_id[:12]}... 的详细信息:")
            detail = client.get_task_detail(thread_id)
            if detail:
                print(f"   - 总运行步骤: {detail.get('total_runs', 0)}")
                print(f"   - 会话名称: {detail.get('session_name', 'N/A')}")
                
                runs = detail.get('runs', [])
                if runs:
                    print(f"   - 运行步骤详情 (前3步):")
                    for i, run in enumerate(runs[:3], 1):
                        print(f"     {i}. {run.get('name', 'N/A')} ({run.get('run_type', 'N/A')})")
                        print(f"        状态: {run.get('status', 'N/A')}")
                        run_tokens = run.get('token_usage')
                        if run_tokens:
                            print(f"        Tokens: {run_tokens.get('total_tokens', 0)}")
            else:
                print("   ❌ 无法获取任务详细信息")
    
    print_section("✅ 示例完成")
    print("💡 提示:")
    print("   - 使用 'curl' 命令可以直接测试 API 端点")
    print("   - 查看 docs/langsmith_api_guide.md 了解详细文档")
    print("   - 在生产环境中建议添加适当的错误处理和重试机制")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1) 