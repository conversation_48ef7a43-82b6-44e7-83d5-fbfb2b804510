#!/usr/bin/env python3
"""
数据库初始化脚本

用于创建数据库和表结构
"""

import os
import sys
import pymysql
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database import engine, create_tables

def create_database_if_not_exists():
    """
    如果数据库不存在则创建
    """
    db_host = os.getenv("DB_HOST", "localhost")
    db_port = int(os.getenv("DB_PORT", "3306"))
    db_user = os.getenv("DB_USER", "root")
    db_password = os.getenv("DB_PASSWORD", "player666")
    db_name = os.getenv("DB_NAME", "deerflow")
    
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库（如果不存在）
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 '{db_name}' 已确保存在")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def init_database():
    """
    初始化数据库
    """
    print("🚀 开始初始化DeerFlow数据库...")
    
    # 1. 创建数据库
    if not create_database_if_not_exists():
        return False
    
    # 2. 创建表结构
    try:
        create_tables()
        print("✅ 数据库表结构创建成功")
        
        # 3. 验证表是否创建成功
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]
            print(f"📋 已创建的表: {', '.join(tables)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 创建表结构失败: {e}")
        return False

def check_database_connection():
    """
    检查数据库连接
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ 数据库连接正常")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("=" * 50)
    print("DeerFlow 数据库初始化脚本")
    print("=" * 50)
    
    # 检查环境变量
    required_env_vars = ["DB_HOST", "DB_USER", "DB_PASSWORD", "DB_NAME"]
    missing_vars = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
        print("请确保已设置 .env 文件或环境变量")
        return False
    
    # 显示配置信息
    print(f"📊 数据库配置:")
    print(f"   主机: {os.getenv('DB_HOST')}")
    print(f"   端口: {os.getenv('DB_PORT')}")
    print(f"   用户: {os.getenv('DB_USER')}")
    print(f"   数据库: {os.getenv('DB_NAME')}")
    print()
    
    # 初始化数据库
    success = init_database()
    
    if success:
        print("\n🎉 数据库初始化完成!")
        print("\n📝 后续步骤:")
        print("1. 启动Redis服务器（用于验证码存储）")
        print("2. 配置邮箱SMTP设置（用于发送验证码）")
        print("3. 配置Google OAuth（用于Google登录）")
        print("4. 启动DeerFlow服务器")
        
        # 检查连接
        check_database_connection()
        
    else:
        print("\n❌ 数据库初始化失败!")
        print("请检查数据库配置和权限")
    
    return success

if __name__ == "__main__":
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️ 未安装python-dotenv，请手动设置环境变量")
    
    success = main()
    sys.exit(0 if success else 1) 