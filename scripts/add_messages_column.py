#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为ChatHistory表添加messages字段
用于存储JSON格式的消息历史记录，以支持历史记录查询功能
"""

import os
import sys
import logging
from sqlalchemy import create_engine, text, JSON, Column
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database.database import DATABASE_URL, SessionLocal
from src.database.models import ChatHistory

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def add_messages_column():
    """
    为ChatHistory表添加messages字段
    """
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # 检查messages字段是否已存在
            check_column_sql = """
            SELECT COUNT(*) as count 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'chat_histories' 
            AND COLUMN_NAME = 'messages'
            """
            
            result = connection.execute(text(check_column_sql))
            column_exists = result.fetchone()[0] > 0
            
            if column_exists:
                logger.info("messages字段已存在，跳过添加")
                return
            
            # 添加messages字段
            add_column_sql = """
            ALTER TABLE chat_histories 
            ADD COLUMN messages JSON NULL COMMENT '消息历史记录JSON格式'
            """
            
            logger.info("开始添加messages字段到chat_histories表...")
            connection.execute(text(add_column_sql))
            connection.commit()
            logger.info("messages字段添加成功")
            
    except SQLAlchemyError as e:
        logger.error(f"数据库操作失败: {e}")
        raise
    except Exception as e:
        logger.error(f"添加messages字段时发生错误: {e}")
        raise


def verify_migration():
    """
    验证迁移是否成功
    """
    try:
        db = SessionLocal()
        
        # 尝试查询一个聊天历史记录并检查messages字段
        chat_history = db.query(ChatHistory).first()
        if chat_history:
            logger.info(f"验证成功：找到聊天记录，messages字段值: {chat_history.messages}")
        else:
            logger.info("验证成功：没有现有聊天记录，但表结构正确")
            
        db.close()
        
    except Exception as e:
        logger.error(f"验证迁移时发生错误: {e}")
        raise


if __name__ == "__main__":
    logger.info("开始数据库迁移：添加messages字段")
    
    try:
        add_messages_column()
        verify_migration()
        logger.info("数据库迁移完成")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        sys.exit(1) 