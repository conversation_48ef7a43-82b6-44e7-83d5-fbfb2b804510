#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：诊断messages字段更新问题
"""

import os
import sys
import json
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database import SessionLocal
from src.database.models import ChatHistory
from sqlalchemy import text

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def debug_raw_sql():
    """
    使用原始SQL直接查询和更新，绕过SQLAlchemy
    """
    logger.info("🔍 使用原始SQL进行调试...")
    
    db = SessionLocal()
    try:
        # 1. 查看现有数据
        result = db.execute(text("SELECT thread_id, messages FROM chat_histories LIMIT 5"))
        rows = result.fetchall()
        
        logger.info(f"📊 数据库中现有数据:")
        for row in rows:
            logger.info(f"  Thread ID: {row[0]}, Messages: {row[1]}")
        
        # 2. 找一个测试用的thread_id
        if rows:
            test_thread_id = rows[0][0]
            logger.info(f"🎯 使用thread_id进行测试: {test_thread_id}")
            
            # 3. 查看当前messages
            result = db.execute(
                text("SELECT messages FROM chat_histories WHERE thread_id = :thread_id"),
                {"thread_id": test_thread_id}
            )
            current_messages = result.fetchone()[0]
            logger.info(f"📝 当前messages: {current_messages}")
            
            # 4. 准备新的messages
            if current_messages:
                new_messages = list(current_messages)  # 复制现有的
            else:
                new_messages = []
            
            new_message = {
                "type": "user",
                "content": "SQL直接测试消息",
                "agent": "debug_script"
            }
            new_messages.append(new_message)
            
            logger.info(f"🔄 准备更新为: {new_messages}")
            
            # 5. 使用原始SQL更新
            db.execute(
                text("UPDATE chat_histories SET messages = :messages WHERE thread_id = :thread_id"),
                {"messages": json.dumps(new_messages), "thread_id": test_thread_id}
            )
            db.commit()
            
            # 6. 验证更新结果
            result = db.execute(
                text("SELECT messages FROM chat_histories WHERE thread_id = :thread_id"),
                {"thread_id": test_thread_id}
            )
            updated_messages = result.fetchone()[0]
            logger.info(f"✅ 更新后messages: {updated_messages}")
            
            if updated_messages and len(updated_messages) == len(new_messages):
                logger.info("🎉 SQL直接更新成功！")
                return True
            else:
                logger.error("❌ SQL直接更新也失败了")
                return False
        else:
            logger.warning("⚠️  数据库中没有现有数据进行测试")
            return False
            
    except Exception as e:
        logger.error(f"❌ SQL调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        db.close()


def debug_sqlalchemy_detection():
    """
    调试SQLAlchemy是否检测到字段变化
    """
    logger.info("🔍 调试SQLAlchemy字段变化检测...")
    
    db = SessionLocal()
    try:
        # 找一个测试记录
        chat_history = db.query(ChatHistory).first()
        if not chat_history:
            logger.warning("⚠️  没有找到测试记录")
            return False
        
        logger.info(f"🎯 使用记录: {chat_history.thread_id}")
        logger.info(f"📝 当前messages: {chat_history.messages}")
        
        # 检查SQLAlchemy的dirty状态
        from sqlalchemy import inspect
        inspector = inspect(chat_history)
        logger.info(f"🔍 修改前dirty状态: {inspector.dirty}")
        logger.info(f"🔍 修改前pending状态: {inspector.pending}")
        
        # 修改messages
        if chat_history.messages:
            new_messages = list(chat_history.messages)
        else:
            new_messages = []
        
        new_message = {
            "type": "user",
            "content": "SQLAlchemy检测测试",
            "agent": "debug_script"
        }
        new_messages.append(new_message)
        
        # 方法1：直接赋值
        logger.info("🔄 方法1：直接赋值")
        chat_history.messages = new_messages
        logger.info(f"🔍 直接赋值后dirty状态: {inspector.dirty}")
        
        # 方法2：使用flag_modified
        logger.info("🔄 方法2：添加flag_modified")
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(chat_history, 'messages')
        logger.info(f"🔍 flag_modified后dirty状态: {inspector.dirty}")
        
        # 检查修改的属性
        logger.info(f"🔍 修改的属性: {inspector.attrs.messages.history}")
        
        # 提交并验证
        db.commit()
        db.refresh(chat_history)
        
        logger.info(f"✅ 提交后messages: {chat_history.messages}")
        
        if chat_history.messages and len(chat_history.messages) >= len(new_messages):
            logger.info("🎉 SQLAlchemy检测成功！")
            return True
        else:
            logger.error("❌ SQLAlchemy检测失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ SQLAlchemy调试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        db.close()


def check_mysql_json_support():
    """
    检查MySQL的JSON支持
    """
    logger.info("🔍 检查MySQL JSON支持...")
    
    db = SessionLocal()
    try:
        # 检查MySQL版本
        result = db.execute(text("SELECT VERSION()"))
        version = result.fetchone()[0]
        logger.info(f"📊 MySQL版本: {version}")
        
        # 检查JSON函数支持
        try:
            result = db.execute(text("SELECT JSON_VALID('{}')"))
            json_support = result.fetchone()[0]
            logger.info(f"📊 JSON支持: {'是' if json_support else '否'}")
        except Exception as e:
            logger.warning(f"⚠️  JSON函数测试失败: {e}")
        
        # 查看表结构
        result = db.execute(text("DESCRIBE chat_histories"))
        columns = result.fetchall()
        logger.info("📊 表结构:")
        for col in columns:
            if col[0] == 'messages':
                logger.info(f"  messages字段: {col}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查MySQL支持失败: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("🚀 开始调试messages字段更新问题")
    
    try:
        # 运行所有调试
        logger.info("\n" + "="*50)
        result1 = check_mysql_json_support()
        
        logger.info("\n" + "="*50)
        result2 = debug_raw_sql()
        
        logger.info("\n" + "="*50)
        result3 = debug_sqlalchemy_detection()
        
        logger.info("\n" + "="*50)
        if result1 and result2 and result3:
            logger.info("🎉 所有调试测试通过！")
        else:
            logger.error("❌ 某些调试测试失败，请检查具体问题")
            
    except Exception as e:
        logger.error(f"❌ 调试脚本运行失败: {e}")
        sys.exit(1) 