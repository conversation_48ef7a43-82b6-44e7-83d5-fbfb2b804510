#!/usr/bin/env python3
"""
简单的上传测试，逐步验证每个环节
"""

import requests
import json
import os
import io

# 配置
os.environ["RAGFLOW_API_URL"] = "http://localhost"
os.environ["RAGFLOW_API_KEY"] = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"
os.environ["RAG_PROVIDER"] = "ragflow"

def test_step_by_step():
    print("🧪 逐步测试RAG功能")
    print("=" * 40)
    
    # 步骤1: 创建数据集
    print("1️⃣ 创建数据集...")
    dataset_payload = {
        "name": f"Simple Test {int(__import__('time').time())}",
        "description": "Simple test dataset"
    }
    
    response = requests.post("http://localhost:8000/api/rag/datasets", json=dataset_payload)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success") and result.get("dataset", {}).get("id"):
            dataset_id = result["dataset"]["id"]
            print(f"✅ 数据集创建成功! ID: {dataset_id}")
        else:
            print("❌ 数据集创建失败")
            return
    else:
        print("❌ 请求失败")
        return
    
    # 步骤2: 上传文件
    print(f"\n2️⃣ 上传文件到数据集 {dataset_id}...")
    
    test_content = "这是一个测试文档，包含一些中文内容用于验证上传功能。"
    
    files = {
        'file': ('test.txt', io.BytesIO(test_content.encode('utf-8')), 'text/plain')
    }
    data = {
        'dataset_id': dataset_id
    }
    
    response = requests.post("http://localhost:8000/api/rag/upload", files=files, data=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print("✅ 文件上传成功!")
        else:
            print("❌ 文件上传失败")
            return
    else:
        print("❌ 上传请求失败")
        return
    
    # 步骤3: 检查RAGFlow后台
    print(f"\n3️⃣ 检查RAGFlow后台...")
    headers = {
        "Authorization": "Bearer ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"http://localhost/api/v1/datasets/{dataset_id}/documents", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        documents = result.get("data", [])
        print(f"RAGFlow后台找到 {len(documents)} 个文档:")
        for doc in documents:
            print(f"  - {doc.get('name')} ({doc.get('size')} bytes)")
        
        if documents:
            print("✅ 成功！文件已经出现在RAGFlow后台")
        else:
            print("❌ RAGFlow后台还没有文档")
    else:
        print(f"❌ 查询失败: {response.text}")

if __name__ == "__main__":
    test_step_by_step() 