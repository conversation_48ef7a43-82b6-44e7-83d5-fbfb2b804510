#!/usr/bin/env python3
"""
Complete RAG Functionality Test

This script tests the full RAG workflow in DeerFlow:
1. Upload documents to create a dataset
2. Test direct retrieval functionality
3. Test retrieval through chat interface
"""

import requests
import json
import time
import os

# Set environment variables for mock server
os.environ["RAGFLOW_API_URL"] = "http://localhost:9380"
os.environ["RAGFLOW_API_KEY"] = "mock-api-key-for-testing"

# Test configuration
DEERFLOW_API_URL = "http://localhost:8000"

def create_test_dataset_with_documents():
    """Create a test dataset and upload sample documents."""
    print("🆕 Creating test dataset with documents...")
    
    # Step 1: Create dataset
    url = f"{DEERFLOW_API_URL}/api/rag/datasets"
    payload = {
        "name": f"Complete RAG Test Dataset {int(time.time())}",
        "description": "A comprehensive test dataset for RAG functionality testing",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
    }
    
    response = requests.post(url, json=payload)
    if response.status_code != 200:
        print(f"❌ Failed to create dataset: {response.status_code} - {response.text}")
        return None, []
    
    result = response.json()
    if not result.get("success"):
        print(f"❌ Dataset creation failed: {result.get('message')}")
        return None, []
    
    dataset = result.get("dataset", {})
    dataset_id = dataset.get("id")
    print(f"✅ Dataset created: {dataset.get('name')} (ID: {dataset_id})")
    
    # Step 2: Upload documents
    sample_docs = [
        {
            "name": "ai_fundamentals.txt",
            "content": """
Artificial Intelligence and Machine Learning Fundamentals

Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines 
that work and react like humans. Machine learning is a subset of AI that enables computers to learn 
automatically from experience without being explicitly programmed.

Key AI concepts include:
- Neural networks and deep learning
- Natural language processing
- Computer vision and image recognition
- Reinforcement learning algorithms
- Expert systems and knowledge representation

Applications of AI include healthcare diagnostics, autonomous vehicles, recommendation systems, 
and intelligent virtual assistants.
            """.strip()
        },
        {
            "name": "deerflow_features.md",
            "content": """
# DeerFlow Research Platform Features

DeerFlow is a comprehensive AI-powered research assistant with the following capabilities:

## Core Features
- **Intelligent Research Planning**: Automatically generates research plans and strategies
- **Multi-Source Data Integration**: Combines web search, academic papers, and personal documents
- **RAG Integration**: Upload and search through your own document collections
- **Automated Report Generation**: Creates detailed research reports with proper citations

## Technical Architecture
- FastAPI backend with LangGraph workflow orchestration
- React frontend with real-time streaming updates
- SQLite/PostgreSQL persistent storage
- Support for multiple LLM providers

## Use Cases
- Academic research and literature reviews
- Business intelligence and market analysis
- Technical documentation creation
- Content creation and writing assistance
            """.strip()
        },
        {
            "name": "system_requirements.txt",
            "content": """
System Requirements and Configuration

DeerFlow requires the following system components:

SOFTWARE REQUIREMENTS:
- Python 3.9 or higher
- Node.js 18+ for frontend development
- FastAPI web framework
- PostgreSQL or SQLite database

HARDWARE REQUIREMENTS:
- Minimum 4GB RAM
- 2GB free disk space
- Multi-core processor recommended

CONFIGURATION ENVIRONMENT VARIABLES:
- RAGFLOW_API_URL: RAG provider endpoint
- RAGFLOW_API_KEY: Authentication credentials
- BASIC_MODEL settings for LLM configuration
- Database connection parameters

DEPLOYMENT OPTIONS:
- Docker containerized deployment
- Direct Python execution
- Cloud platform deployment (AWS, GCP, Azure)
            """.strip()
        }
    ]
    
    files = []
    for doc in sample_docs:
        content_bytes = doc["content"].encode('utf-8')
        files.append({
            "name": doc["name"],
            "content": list(content_bytes)
        })
    
    url = f"{DEERFLOW_API_URL}/api/rag/upload"
    payload = {
        "dataset_id": dataset_id,
        "files": files
    }
    
    response = requests.post(url, json=payload)
    if response.status_code != 200:
        print(f"❌ Failed to upload documents: {response.status_code} - {response.text}")
        return dataset_id, []
    
    result = response.json()
    if not result.get("success"):
        print(f"❌ Document upload failed: {result.get('message')}")
        return dataset_id, []
    
    data = result.get("data", {})
    documents = data.get("documents", []) if isinstance(data, dict) else data
    document_ids = [doc.get('id') for doc in documents if isinstance(doc, dict)]
    
    print(f"✅ Uploaded {len(document_ids)} documents")
    
    # Step 3: Parse documents
    url = f"{DEERFLOW_API_URL}/api/rag/documents/parse"
    payload = {
        "dataset_id": dataset_id,
        "document_ids": document_ids
    }
    
    response = requests.post(url, json=payload)
    if response.status_code == 200 and response.json().get("success"):
        print("✅ Document parsing initiated")
        # Wait a moment for parsing to complete (mock server processes instantly)
        time.sleep(1)
    
    return dataset_id, document_ids


def test_direct_retrieval(dataset_id):
    """Test direct document retrieval using the retriever."""
    print("\n🔍 Testing Direct Document Retrieval")
    print("=" * 50)
    
    try:
        from src.rag.builder import build_retriever
        from src.rag.retriever import Resource
        
        retriever = build_retriever()
        if not retriever:
            print("❌ No retriever available")
            return False
        
        # Create resource for the test dataset
        test_resource = Resource(
            uri=f"rag://dataset/{dataset_id}",
            title="Test Dataset",
            description="Test dataset for retrieval"
        )
        
        test_queries = [
            "artificial intelligence machine learning",
            "DeerFlow research features",
            "system requirements configuration"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: {query}")
            
            try:
                documents = retriever.query_relevant_documents(query, [test_resource])
                print(f"✅ Found {len(documents)} relevant documents")
                
                for doc in documents:
                    print(f"   📄 {doc.title} - {len(doc.chunks)} chunks")
                    for chunk in doc.chunks[:1]:  # Show first chunk
                        preview = chunk.content[:150] + "..." if len(chunk.content) > 150 else chunk.content
                        print(f"      - Similarity: {chunk.similarity:.3f}")
                        print(f"      - Content: {preview}")
                        
            except Exception as e:
                print(f"❌ Search error: {str(e)}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Direct retrieval test failed: {str(e)}")
        return False


def test_chat_retrieval(dataset_id):
    """Test document retrieval through chat interface."""
    print("\n💬 Testing Chat-based Retrieval")
    print("=" * 50)
    
    # Get the resource information
    url = f"{DEERFLOW_API_URL}/api/rag/resources"
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"❌ Failed to get resources: {response.status_code}")
        return False
    
    resources = response.json().get("resources", [])
    test_resource = None
    
    for resource in resources:
        if dataset_id in resource.get('uri', ''):
            test_resource = resource
            break
    
    if not test_resource:
        print("❌ Test dataset not found in resources")
        return False
    
    print(f"✅ Using resource: {test_resource['title']}")
    
    # Test simple query
    query = "What are the key features of DeerFlow?"
    print(f"\n📝 Testing query: {query}")
    
    chat_request = {
        "messages": [
            {"role": "user", "content": query}
        ],
        "thread_id": f"test_chat_retrieval_{int(time.time())}",
        "resources": [test_resource],
        "max_plan_iterations": 1,
        "max_step_num": 3,
        "max_search_results": 5,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": False
    }
    
    url = f"{DEERFLOW_API_URL}/api/chat/stream"
    try:
        response = requests.post(url, json=chat_request, stream=True, timeout=30)
        
        if response.status_code == 200:
            print("✅ Chat query processed successfully")
            
            # Collect some streaming response
            response_content = ""
            for line_count, line in enumerate(response.iter_lines()):
                if line_count > 20:  # Limit to avoid infinite loop
                    break
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        try:
                            event_data = json.loads(data)
                            if 'content' in event_data and event_data['content']:
                                response_content += event_data['content']
                        except json.JSONDecodeError:
                            continue
            
            if response_content:
                preview = response_content[:200] + "..." if len(response_content) > 200 else response_content
                print(f"📄 Response preview: {preview}")
                return True
            else:
                print("📄 No content received, but request was successful")
                return True
                
        else:
            print(f"❌ Chat query failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chat retrieval error: {str(e)}")
        return False


def main():
    """Main test function."""
    print("🚀 Complete RAG Functionality Test")
    print("=" * 60)
    
    # Step 1: Create test dataset and upload documents
    dataset_id, document_ids = create_test_dataset_with_documents()
    if not dataset_id:
        print("❌ Failed to create test dataset. Exiting.")
        return
    
    print(f"\n📋 Test Dataset ID: {dataset_id}")
    print(f"📄 Document IDs: {document_ids}")
    
    # Step 2: Test direct retrieval
    direct_success = test_direct_retrieval(dataset_id)
    
    # Step 3: Test chat-based retrieval
    chat_success = test_chat_retrieval(dataset_id)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print(f"   ✅ Dataset Creation: ✓")
    print(f"   ✅ Document Upload: ✓")
    print(f"   {'✅' if direct_success else '❌'} Direct Retrieval: {'✓' if direct_success else '✗'}")
    print(f"   {'✅' if chat_success else '❌'} Chat Retrieval: {'✓' if chat_success else '✗'}")
    
    if direct_success and chat_success:
        print("\n🎉 All RAG functionality tests PASSED!")
        print("\n📋 Next Steps:")
        print("1. The RAG upload and retrieval system is working correctly")
        print("2. Users can now upload documents and search through them")
        print("3. Integration with chat interface is functional")
        print("4. Ready for production use with real RAGFlow server")
    else:
        print("\n⚠️ Some tests failed. Please check the logs above.")


if __name__ == "__main__":
    main() 