#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证JSON字段更新修复
专门测试messages字段是否能正确更新到数据库
"""

import os
import sys
import json
import logging
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.graph.nodes import update_messages_to_database
from src.database.database import SessionLocal
from src.database.models import ChatHistory, User

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_messages_persistence():
    """
    测试消息持久化是否正常工作
    """
    logger.info("🔄 开始测试消息持久化...")
    
    # 使用唯一的thread_id
    thread_id = f"test-persistence-{int(time.time())}"
    
    try:
        # 1. 添加第一条消息
        logger.info("📝 添加第一条消息...")
        update_messages_to_database(
            thread_id=thread_id,
            agent="test_agent_1",
            content="这是第一条测试消息",
            msg_type="user"
        )
        
        # 验证第一条消息
        db = SessionLocal()
        try:
            chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
            if chat_history and chat_history.messages:
                logger.info(f"✅ 第一条消息成功保存，数据库中有 {len(chat_history.messages)} 条消息")
                logger.info(f"   内容: {chat_history.messages[0]}")
            else:
                logger.error("❌ 第一条消息保存失败")
                return False
        finally:
            db.close()
        
        # 2. 添加第二条消息
        logger.info("📝 添加第二条消息...")
        update_messages_to_database(
            thread_id=thread_id,
            agent="test_agent_2", 
            content="这是第二条测试消息",
            msg_type="assistant"
        )
        
        # 验证第二条消息
        db = SessionLocal()
        try:
            chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
            if chat_history and chat_history.messages and len(chat_history.messages) == 2:
                logger.info(f"✅ 第二条消息成功保存，数据库中有 {len(chat_history.messages)} 条消息")
                logger.info(f"   第一条: {chat_history.messages[0]}")
                logger.info(f"   第二条: {chat_history.messages[1]}")
            else:
                logger.error(f"❌ 第二条消息保存失败，当前消息数: {len(chat_history.messages) if chat_history and chat_history.messages else 0}")
                if chat_history and chat_history.messages:
                    logger.error(f"   当前消息列表: {chat_history.messages}")
                return False
        finally:
            db.close()
        
        # 3. 连续添加多条消息
        logger.info("📝 连续添加多条消息...")
        for i in range(3, 6):
            update_messages_to_database(
                thread_id=thread_id,
                agent=f"test_agent_{i}",
                content=f"这是第{i}条测试消息",
                msg_type="assistant" if i % 2 == 0 else "user"
            )
        
        # 最终验证
        db = SessionLocal()
        try:
            chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
            if chat_history and chat_history.messages and len(chat_history.messages) == 5:
                logger.info(f"✅ 所有消息保存成功！数据库中有 {len(chat_history.messages)} 条消息")
                for i, msg in enumerate(chat_history.messages):
                    logger.info(f"   消息 {i+1}: {msg}")
                return True
            else:
                logger.error(f"❌ 最终验证失败，期望5条消息，实际: {len(chat_history.messages) if chat_history and chat_history.messages else 0}")
                if chat_history and chat_history.messages:
                    logger.error(f"   当前消息列表: {chat_history.messages}")
                return False
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_concurrent_updates():
    """
    测试并发更新是否会导致消息丢失
    """
    logger.info("🔄 开始测试并发更新...")
    
    thread_id = f"test-concurrent-{int(time.time())}"
    
    try:
        # 快速连续添加多条消息（模拟并发）
        for i in range(10):
            update_messages_to_database(
                thread_id=thread_id,
                agent=f"concurrent_agent_{i}",
                content=f"并发消息 {i}",
                msg_type="assistant"
            )
        
        # 验证所有消息都保存了
        db = SessionLocal()
        try:
            chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
            if chat_history and chat_history.messages and len(chat_history.messages) == 10:
                logger.info(f"✅ 并发测试成功！所有 {len(chat_history.messages)} 条消息都保存了")
                return True
            else:
                logger.error(f"❌ 并发测试失败，期望10条消息，实际: {len(chat_history.messages) if chat_history and chat_history.messages else 0}")
                return False
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 并发测试失败: {e}")
        return False


def cleanup_test_data():
    """
    清理测试数据
    """
    logger.info("🧹 清理测试数据...")
    
    try:
        db = SessionLocal()
        
        # 删除所有测试相关的聊天历史
        test_chats = db.query(ChatHistory).filter(
            ChatHistory.thread_id.like("test-persistence-%") |
            ChatHistory.thread_id.like("test-concurrent-%")
        ).all()
        
        for chat in test_chats:
            db.delete(chat)
        
        db.commit()
        logger.info(f"✅ 清理了 {len(test_chats)} 条测试数据")
        
    except Exception as e:
        logger.error(f"❌ 清理数据失败: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("🚀 开始测试消息持久化修复")
    
    try:
        # 运行测试
        test1_result = test_messages_persistence()
        test2_result = test_concurrent_updates()
        
        if test1_result and test2_result:
            logger.info("🎉 所有测试通过！消息持久化工作正常")
        else:
            logger.error("❌ 某些测试失败，请检查代码")
            
        # 询问是否清理数据
        cleanup = input("是否清理测试数据? (y/n): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_data()
        else:
            logger.info("保留测试数据以供进一步检查")
            
    except Exception as e:
        logger.error(f"❌ 测试运行失败: {e}")
        sys.exit(1) 