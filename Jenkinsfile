pipeline {
  agent any
  stages {
    stage('检出') {
      steps {
        checkout([$class: 'GitSCM',
        branches: [[name: GIT_BUILD_REF]],
        userRemoteConfigs: [[
          url: GIT_REPO_URL,
          credentialsId: CREDENTIALS_ID
        ]]])
      }
    }
    stage('构建镜像并推送到 CODING Docker 制品库') {
      steps {
        script {
          docker.withRegistry(
            "${CCI_CURRENT_WEB_PROTOCOL}://${CODING_DOCKER_REG_HOST}",
            "${CODING_ARTIFACTS_CREDENTIALS_ID}"
          ) {
            def dockerImage = docker.build("${CODING_DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_VERSION}", "-f ${DOCKERFILE_PATH} ${DOCKER_BUILD_CONTEXT}")
            dockerImage.push()
          }
        }
      }
    }
    stage('部署到测试环境（远端服务1）') {
      steps {
        script {
          deployToServer(
            serverHost: "${REMOTE_HOST}",
            credentialsId: "${REMOTE_CRED}",
            containerName: "thinkwriter-test",
            hostPort: 9071,
            containerPort: 8000,
            stageName: "测试环境"
          )
        }
      }
    }
    stage('确认测试结果') {
      steps {
        input message: '请确认测试环境是否正常？', ok: '确认部署生产环境'
      }
    }
    stage('部署到生产环境（远端服务）') {
      steps {
        script {
          deployToServer(
            serverHost: "${REMOTE_HOST}",
            credentialsId: "${REMOTE_CRED}",
            containerName: "thinkwriter-prod",
            hostPort: 9070,
            containerPort: 8000,
            stageName: "生产环境"
          )
        }
      }
    }
  }
  environment {
    CODING_DOCKER_REG_HOST = "${CCI_CURRENT_TEAM}-docker.pkg.${CCI_CURRENT_DOMAIN}"
    CODING_DOCKER_IMAGE_NAME = "${PROJECT_NAME.toLowerCase()}/${DOCKER_REPO_NAME}/${DOCKER_IMAGE_NAME}"
  }
}

// 封装部署函数
def deployToServer(config) {
  def remoteConfig = [:]
  remoteConfig.name = "remote-server-${UUID.randomUUID().toString()}"
  remoteConfig.host = config.serverHost
  remoteConfig.port = "${REMOTE_SSH_PORT}".toInteger()
  remoteConfig.allowAnyHosts = true

  withCredentials([
    usernamePassword(
      credentialsId: config.credentialsId,
      usernameVariable: 'REMOTE_USER',
      passwordVariable: 'REMOTE_PASS'
    ),
    usernamePassword(
      credentialsId: "${CODING_ARTIFACTS_CREDENTIALS_ID}",
      usernameVariable: 'CODING_DOCKER_REG_USERNAME',
      passwordVariable: 'CODING_DOCKER_REG_PASSWORD'
    )
  ]) {
    remoteConfig.user = "${REMOTE_USER}"
    remoteConfig.password = "${REMOTE_PASS}"

    sshCommand(
      remote: remoteConfig,
      command: "docker login -u ${CODING_DOCKER_REG_USERNAME} -p ${CODING_DOCKER_REG_PASSWORD} ${CODING_DOCKER_REG_HOST}",
      sudo: true,
    )

    sshCommand(
      remote: remoteConfig,
      command: "docker rm -f ${config.containerName} || true",
      sudo: true,
    )

    DOCKER_IMAGE_URL = sh(
      script: "echo ${CODING_DOCKER_REG_HOST}/${CODING_DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_VERSION}",
      returnStdout: true
    ).trim()

    sshCommand(
      remote: remoteConfig,
      command: "docker run -d -p ${config.hostPort}:${config.containerPort} --name ${config.containerName} ${DOCKER_IMAGE_URL}",
      sudo: true,
    )

    echo "${config.stageName}部署成功，请到 http://${remoteConfig.host}:${config.hostPort} 访问"
  }
}