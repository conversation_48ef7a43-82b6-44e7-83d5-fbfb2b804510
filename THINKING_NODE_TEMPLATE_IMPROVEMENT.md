# Thinking节点提示词模板变量处理改进

## 问题描述

在原有实现中，thinking节点在填充提示词模板时，`user_query`和`research_results`两个关键变量的处理不够完善：

1. **user_query**: 应该从plan的title字段获取，代表用户的原始查询意图
2. **research_results**: 应该从plan中已完成steps的execution_res中获取，提供研究背景

## 解决方案

### 1. 改进的变量获取逻辑

在`src/graph/nodes.py`的`_setup_and_execute_agent_step`函数中，为thinking节点添加了专门的变量处理逻辑，通过`create_agent_with_locale`函数的`extra_context`参数传递给提示词模板：

```python
# Prepare extra context for thinking agent template variables
extra_context = {}
if agent_type == "thinking":
    current_plan = state.get("current_plan")
    
    # Get user_query from plan title
    plan_title = current_plan.title if current_plan and hasattr(current_plan, 'title') else "Deep analysis required"
    extra_context["user_query"] = plan_title
    
    # Get research_results from completed steps
    research_results_list = []
    if current_plan and current_plan.steps:
        for step in current_plan.steps:
            if step.execution_res:
                research_results_list.append({
                    "title": step.title,
                    "description": step.description,
                    "result": step.execution_res
                })
    
    # Format research results as JSON for thinking template
    research_results_json = json.dumps(research_results_list, ensure_ascii=False) if research_results_list else "No external research provided - rely on internal knowledge"
    extra_context["research_results"] = research_results_json

# Create agent with extra_context
agent = create_agent_with_locale(agent_type, agent_type, loaded_tools, agent_type, locale, extra_context)
```

### 2. 变量来源说明

#### user_query变量
- **来源**: `current_plan.title`
- **作用**: 提供用户的原始查询意图，让thinking节点了解整体任务目标
- **示例**: "人工智能在医疗诊断中的应用研究"
- **fallback**: 如果plan没有title，使用"Deep analysis required"

#### research_results变量
- **来源**: 已完成steps的`execution_res`字段
- **格式**: JSON数组，包含每个已完成研究步骤的详细信息
- **结构**:
  ```json
  [
    {
      "title": "步骤标题",
      "description": "步骤描述", 
      "result": "执行结果内容"
    }
  ]
  ```
- **fallback**: 如果没有已完成的研究步骤，使用"No external research provided - rely on internal knowledge"

### 3. 提示词模板集成

thinking节点的提示词模板(`src/prompts/thinking.md`)中使用这些变量：

```markdown
**User Query**: {{ user_query if user_query else "Deep analysis required" }}

**Available Research Context**: 
{{ research_results if research_results else "No external research provided - rely on internal knowledge" }}
```

### 4. 技术实现细节

#### 数据流
```
State → _setup_and_execute_agent_step → extra_context → create_agent_with_locale → enhanced_prompt → apply_prompt_template
```

#### 关键改进点
1. **正确的位置**: 在`_setup_and_execute_agent_step`中处理，而不是在`_execute_agent_step`中
2. **正确的方式**: 通过`extra_context`参数传递给`create_agent_with_locale`
3. **模板集成**: 变量自动合并到模板状态中，无需手动处理

## 改进效果

### 1. 更准确的任务理解
- thinking节点现在能够准确理解用户的原始查询意图
- 基于plan的title提供一致的任务上下文

### 2. 更丰富的研究背景
- 自动收集所有已完成研究步骤的结果
- 以结构化JSON格式提供给thinking节点
- 支持多个研究步骤的综合分析

### 3. 更好的工作流集成
- 支持thinking步骤在工作流中的不同位置（开始、中间、结束）
- 自动适应不同数量的前置研究步骤
- 提供合理的fallback机制

### 4. 更清晰的架构
- 变量处理逻辑集中在agent创建阶段
- 通过标准的`extra_context`机制传递
- 符合系统的整体设计模式

## 测试验证

### 1. 基础功能测试
- ✅ 有研究数据的情况下正确提取和格式化
- ✅ 无研究数据的情况下使用fallback
- ✅ 模板变量正确填充

### 2. Agent创建测试
- ✅ `create_agent_with_locale`正确接收`extra_context`
- ✅ 变量正确合并到模板状态中
- ✅ 模板应用成功且包含预期内容

### 3. 实际场景验证
- ✅ 学术论文写作工作流
- ✅ 技术报告分析工作流
- ✅ 市场研究分析工作流

## 使用示例

### 场景1: 学术论文写作
```
用户查询: "撰写AI在教育中应用的学术论文"
plan.title: "人工智能在教育领域的应用与发展趋势研究"

已完成研究步骤:
1. AI教育技术现状调研 → 技术栈和应用领域信息
2. AI教育应用案例收集 → 成功案例和效果分析

thinking节点接收:
- user_query: "人工智能在教育领域的应用与发展趋势研究"
- research_results: JSON格式的两个研究步骤结果
```

### 场景2: 理论分析优先
```
用户查询: "分析AI伦理框架"
plan.title: "Theoretical Framework for AI Ethics"

已完成研究步骤: 无（thinking步骤在开始）

thinking节点接收:
- user_query: "Theoretical Framework for AI Ethics"
- research_results: "No external research provided - rely on internal knowledge"
```

## 技术细节

### 1. 数据流
```
Plan.title → user_query → extra_context → create_agent_with_locale → thinking template
Plan.steps[].execution_res → research_results_list → JSON → extra_context → thinking template
```

### 2. 错误处理
- Plan不存在时使用默认值
- execution_res为空时跳过该步骤
- 确保JSON序列化安全（ensure_ascii=False支持中文）

### 3. 性能考虑
- 只处理已完成的步骤，避免处理未执行的步骤
- JSON序列化一次性完成，避免重复处理
- 合理的日志记录，便于调试

## 兼容性

### 1. 向后兼容
- 不影响其他节点的功能
- 保持原有的agent创建和执行流程
- 只在thinking节点中添加特殊处理

### 2. 扩展性
- 可以轻松为其他节点添加类似的变量处理
- 模板变量系统支持更多自定义字段
- 支持未来的新节点类型

## 总结

这次改进显著提升了thinking节点的智能化程度：

1. **更准确的任务理解**: 通过plan.title获取用户原始意图
2. **更丰富的分析基础**: 通过已完成研究步骤提供充分的背景信息
3. **更好的工作流集成**: 支持各种工作流场景和步骤序列
4. **更强的容错能力**: 完善的fallback机制确保稳定运行
5. **更清晰的架构**: 通过标准的`extra_context`机制实现，符合系统设计模式

thinking节点现在能够更好地发挥其深度分析和综合思考的作用，为后续的研究和写作提供更有价值的洞察。 