#!/usr/bin/env python3

import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from langchain_core.messages import HumanMessage

def test_message_format():
    """Test message format with LangChain objects."""
    
    # Test with LangChain message objects (like in real scenario)
    state = {
        'messages': [HumanMessage(content='写一篇AI论文')],
        'user_query': '写一篇AI论文',
        'current_plan': '{}',
        'research_thinking': '',
        'research_results': '[]',
        'locale': 'zh-CN'
    }

    try:
        messages = apply_prompt_template('outline_writer', state, Configuration())
        print(f'Messages type: {type(messages)}')
        print(f'First message type: {type(messages[0])}')
        print(f'First message keys: {list(messages[0].keys()) if isinstance(messages[0], dict) else "Not dict"}')
        print(f'Messages length: {len(messages)}')
        
        # Check if second message is LangChain object
        if len(messages) > 1:
            print(f'Second message type: {type(messages[1])}')
            if hasattr(messages[1], 'content'):
                print(f'Second message content length: {len(messages[1].content)}')
            else:
                print(f'Second message: {messages[1]}')
        
        return True
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_message_format()
    sys.exit(0 if success else 1) 