#!/usr/bin/env python3
"""
测试checkpoint API端点的脚本
"""

import requests
import json
import time
from uuid import uuid4

# API基础URL
BASE_URL = "http://localhost:8000"

def test_checkpoint_api():
    """测试checkpoint相关的API端点"""
    
    # 模拟一个thread_id（在实际使用中这会是真实的对话ID）
    thread_id = str(uuid4())
    
    print(f"测试thread_id: {thread_id}")
    
    # 1. 测试获取checkpoint状态
    print("\n1. 测试获取checkpoint状态...")
    try:
        response = requests.get(f"{BASE_URL}/api/thread/{thread_id}/checkpoint")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Checkpoint状态: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("需要认证，请先登录")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 2. 测试获取checkpoint历史
    print("\n2. 测试获取checkpoint历史...")
    try:
        response = requests.get(f"{BASE_URL}/api/thread/{thread_id}/history")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Checkpoint历史: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("需要认证，请先登录")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_with_real_conversation():
    """使用真实对话测试checkpoint功能"""
    
    print("\n3. 创建真实对话并测试checkpoint...")
    
    # 创建一个对话
    thread_id = str(uuid4())
    
    # 发送消息（这需要认证，所以可能会失败）
    conversation_data = {
        "messages": [
            {
                "role": "user",
                "content": "请帮我研究一下人工智能的发展历史"
            }
        ],
        "thread_id": thread_id,
        "resources": [],
        "max_plan_iterations": 3,
        "max_step_num": 20,
        "max_search_results": 10,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": True
    }
    
    try:
        print(f"发送对话请求到thread_id: {thread_id}")
        response = requests.post(
            f"{BASE_URL}/api/chat/stream",
            json=conversation_data,
            headers={"Content-Type": "application/json"},
            stream=True
        )
        
        print(f"对话请求状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("需要认证，无法创建对话")
            return
        
        # 读取一些流数据
        count = 0
        for line in response.iter_lines():
            if line:
                count += 1
                if count > 5:  # 只读取前几行
                    break
                print(f"流数据: {line.decode('utf-8')}")
        
        # 等待一下让对话有时间执行
        time.sleep(2)
        
        # 现在测试checkpoint API
        print(f"\n检查thread_id {thread_id} 的checkpoint状态...")
        
        checkpoint_response = requests.get(f"{BASE_URL}/api/thread/{thread_id}/checkpoint")
        if checkpoint_response.status_code == 200:
            data = checkpoint_response.json()
            print(f"Checkpoint状态: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"获取checkpoint状态失败: {checkpoint_response.status_code}")
        
        history_response = requests.get(f"{BASE_URL}/api/thread/{thread_id}/history")
        if history_response.status_code == 200:
            data = history_response.json()
            print(f"Checkpoint历史: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"获取checkpoint历史失败: {history_response.status_code}")
            
    except Exception as e:
        print(f"对话请求失败: {e}")

def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器正在运行: uv run uvicorn src.server.app:app --reload")
        return False

if __name__ == "__main__":
    print("测试Checkpoint API端点...")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        exit(1)
    
    # 测试基本API
    test_checkpoint_api()
    
    # 测试真实对话（可能需要认证）
    test_with_real_conversation()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n注意事项:")
    print("1. 如果看到401错误，说明需要用户认证")
    print("2. 如果看到500错误，可能是checkpoint不存在（正常情况）")
    print("3. 要测试完整功能，需要先通过Web界面创建对话")
