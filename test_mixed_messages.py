#!/usr/bin/env python3

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.llms.llm import get_llm_by_type
from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from langchain_core.messages import HumanMessage

def test_mixed_messages():
    """Test if mixed message formats cause LLM issues."""
    
    # Test with LangChain message objects (like in real scenario)
    state = {
        'messages': [HumanMessage(content='写一篇AI论文')],
        'user_query': '写一篇AI论文',
        'current_plan': '{}',
        'research_thinking': '',
        'research_results': '[]',
        'locale': 'zh-CN'
    }

    try:
        messages = apply_prompt_template('outline_writer', state, Configuration())
        print(f'Generated {len(messages)} messages')
        print(f'Message types: {[type(msg) for msg in messages]}')
        
        # Test with LLM
        llm = get_llm_by_type('basic')
        print("Testing LLM with mixed message formats...")
        
        # Test streaming
        full_response = ""
        try:
            response = llm.stream(messages)
            for i, chunk in enumerate(response):
                full_response += chunk.content
                if i < 3:  # Print first few chunks
                    print(f"Chunk {i}: {chunk.content[:30]}...")
        except Exception as e:
            print(f"Streaming failed: {e}")
            # Try invoke
            try:
                response = llm.invoke(messages)
                full_response = response.content
                print(f"Invoke succeeded: {len(full_response)} chars")
            except Exception as e2:
                print(f"Invoke also failed: {e2}")
                return False
        
        print(f"Total response length: {len(full_response)}")
        
        if not full_response.strip():
            print("ERROR: Empty response with mixed message formats!")
            return False
        
        return True
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_mixed_messages()
    sys.exit(0 if success else 1) 