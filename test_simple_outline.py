#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_simple_outline():
    """Test the simplified outline writer template."""
    
    # Create test state
    plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="人工智能在医疗诊断中应用的学术论文信息收集计划",
        thought="用户希望撰写一篇关于人工智能在医疗诊断中应用的学术论文，需要收集文献综述、技术分析和未来展望所需的信息。",
        steps=[
            Step(
                need_web_search=True,
                title="收集人工智能在医疗诊断应用的文献综述信息",
                description="收集关于人工智能在医疗诊断领域应用的现有学术文献、研究论文和综述。",
                step_type=StepType.RESEARCH,
                execution_res=None
            )
        ]
    )
    
    state = {
        "messages": [
            HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望"),
        ],
        "locale": "zh-CN",
        "current_plan": plan,
    }
    
    try:
        # Prepare template state
        template_state = {
            **state,
            "user_query": "撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望",
            "research_results": json.dumps([], ensure_ascii=False),
            "locale": "zh-CN"
        }
        
        # Apply simplified template
        messages = apply_prompt_template("outline_writer_simple", template_state, Configuration())
        
        print(f"Generated {len(messages)} messages")
        print(f"System prompt length: {len(messages[0]['content'])}")
        print(f"System prompt preview: {messages[0]['content'][:300]}...")
        
        # Test with LLM
        llm = get_llm_by_type("basic")
        
        print("Testing simplified template with LLM...")
        response = llm.invoke(messages)
        
        print(f"Response length: {len(response.content)}")
        print(f"Response: {response.content}")
        
        if not response.content.strip():
            print("ERROR: Empty response!")
            return False
        
        # Try to parse JSON
        try:
            from src.utils.json_utils import repair_json_output
            outline_data = json.loads(repair_json_output(response.content))
            print("JSON parsing successful!")
            print(f"Outline title: {outline_data.get('title', 'N/A')}")
            print(f"Number of sections: {len(outline_data.get('sections', []))}")
            return True
        except json.JSONDecodeError as e:
            print(f"JSON parsing failed: {e}")
            return False
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_outline()
    sys.exit(0 if success else 1) 