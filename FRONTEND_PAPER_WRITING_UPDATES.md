# Frontend Updates for Paper Writing Functionality

## Overview
This document outlines the frontend changes made to support the paper writing workflow in DeerFlow.

## Changes Made

### 1. Message Types Update
- **File**: `web/src/core/messages/types.ts`
- **Changes**: Added `outline_writer` and `paper_writer` to the agent type union
- **Purpose**: Support new agent types in the message system

### 2. Message List View Updates
- **File**: `web/src/app/chat/components/message-list-view.tsx`
- **Changes**:
  - Added imports for `FileText` and `Edit3` icons from lucide-react
  - Updated MessageListItem to handle `outline_writer` and `paper_writer` agents
  - Added `OutlineCard` component for displaying paper outlines
  - Added `PaperWritingCard` component for displaying paper sections
  - Updated `PlanCard` to support paper writing option with `handlePaperWriting` callback

### 3. New UI Components

#### OutlineCard Component
- Displays academic paper outlines generated by the `outline_writer` agent
- Shows title, abstract, expected word count, and section breakdown
- Supports user feedback with accept/reject options
- Features animated loading states and proper styling

#### PaperWritingCard Component
- Displays individual paper sections generated by the `paper_writer` agent
- Shows section content and citations
- Features animated loading states during generation
- Responsive design with proper typography

### 4. Conversation Starters Update
- **File**: `web/src/app/chat/components/conversation-starter.tsx`
- **Changes**: Added Chinese paper writing example to help users test the functionality

## User Interaction Flow

1. **Plan Generation**: User receives a research plan from the planner
2. **Paper Writing Option**: User can choose "Write Paper" option alongside "Accept" and "Edit Plan"
3. **Outline Generation**: System generates and displays paper outline using `OutlineCard`
4. **Outline Review**: User can accept or request modifications to the outline
5. **Paper Writing**: System writes paper sections iteratively using `PaperWritingCard`
6. **Final Output**: Complete paper is generated and displayed

## Technical Details

### Message Flow
- `planner` → User feedback → `[PAPER_WRITING]` → `outline_writer` → User feedback → `paper_writer` (iterative)

### State Management
- Paper writing agents are handled in the main message flow
- No separate research state management needed (unlike research workflow)
- Standard message streaming and feedback mechanisms apply

### Styling
- Consistent with existing DeerFlow design system
- Uses Tailwind CSS classes and existing component patterns
- Responsive design with proper spacing and typography
- Animated loading states using framer-motion

## Testing
To test the paper writing functionality:
1. Start a conversation with a research query (e.g., the Chinese example in conversation starters)
2. Wait for the planner to generate a research plan
3. Click "Write Paper" option when presented
4. Review and accept the generated outline
5. Watch as paper sections are generated iteratively

## Future Enhancements
- Add download functionality for completed papers
- Support for different paper formats (APA, MLA, etc.)
- Integration with citation management systems
- Real-time collaboration features for paper editing 