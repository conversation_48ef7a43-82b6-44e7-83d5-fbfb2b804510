#!/usr/bin/env python3
"""
测试认证系统和聊天历史功能
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000/api"

def test_send_verification_code():
    """测试发送验证码"""
    print("\n=== 测试发送验证码 ===")
    
    response = requests.post(f"{BASE_URL}/auth/send-verification", json={
        "email": "<EMAIL>"
    })
    
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"响应: {data}")
        return True
    else:
        print(f"错误: {response.text}")
        return False

def test_email_login():
    """测试邮箱登录"""
    print("\n=== 测试邮箱登录 ===")
    
    # 这里需要手动输入验证码，实际测试时可以从Redis或数据库获取
    verification_code = input("请输入验证码 (或直接按回车跳过): ").strip()
    if not verification_code:
        print("跳过邮箱登录测试")
        return None
    
    response = requests.post(f"{BASE_URL}/auth/login/email", json={
        "email": "<EMAIL>",
        "verification_code": verification_code
    })
    
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"登录成功: {data['user']['email']}")
        print(f"Token: {data['access_token'][:50]}...")
        return data['access_token']
    else:
        print(f"登录失败: {response.text}")
        return None

def test_chat_stream_with_auth(token):
    """测试带认证的聊天流接口"""
    print("\n=== 测试聊天流接口（需要认证）===")
    
    if not token:
        print("没有有效token，跳过测试")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    chat_request = {
        "messages": [
            {
                "role": "user",
                "content": "你好，请介绍一下量子计算的基本原理和应用前景"
            }
        ],
        "thread_id": "__default__",
        "resources": [],
        "max_plan_iterations": 3,
        "max_step_num": 20,
        "max_search_results": 10,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/chat/stream", 
            json=chat_request,
            headers=headers,
            stream=True,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("聊天流响应 (前10行):")
            line_count = 0
            thread_id = None
            
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    print(line_text)
                    
                    # 提取thread_id
                    if 'thread_id' in line_text and not thread_id:
                        try:
                            if line_text.startswith('data: '):
                                data = json.loads(line_text[6:])
                                thread_id = data.get('thread_id')
                        except:
                            pass
                    
                    line_count += 1
                    if line_count >= 10:
                        break
            
            print(f"提取到的thread_id: {thread_id}")
            return thread_id
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def test_get_chat_threads(token):
    """测试获取对话线程列表"""
    print("\n=== 测试获取对话线程列表 ===")
    
    if not token:
        print("没有有效token，跳过测试")
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{BASE_URL}/auth/chat-threads", headers=headers)
    
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"对话线程数量: {data['total']}")
        print("对话线程列表:")
        for thread in data['threads']:
            print(f"  - {thread['thread_id']}: {thread['title']} (创建时间: {thread['created_at']})")
    else:
        print(f"获取失败: {response.text}")

def test_get_user_info(token):
    """测试获取用户信息"""
    print("\n=== 测试获取用户信息 ===")
    
    if not token:
        print("没有有效token，跳过测试")
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"用户信息: {data}")
    else:
        print(f"获取失败: {response.text}")

def main():
    """主测试函数"""
    print("开始测试认证系统和聊天历史功能")
    print(f"时间: {datetime.now()}")
    
    # 测试发送验证码
    if not test_send_verification_code():
        print("发送验证码失败，跳过后续测试")
        return
    
    # 测试登录
    token = test_email_login()
    
    # 测试获取用户信息
    test_get_user_info(token)
    
    # 测试聊天流接口
    thread_id = test_chat_stream_with_auth(token)
    
    # 测试获取对话线程列表
    test_get_chat_threads(token)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main() 