#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration

def test_outline_template():
    """Test the outline writer template with sample data."""
    
    # Sample state data
    test_state = {
        "messages": [
            {"role": "user", "content": "撰写一篇关于人工智能在医疗诊断中应用的学术论文"}
        ],
        "user_query": "撰写一篇关于人工智能在医疗诊断中应用的学术论文",
        "current_plan": json.dumps({
            "title": "AI在医疗诊断中的应用研究",
            "thought": "需要深入研究AI技术在医疗诊断领域的应用现状、技术挑战和发展前景",
            "steps": [
                {
                    "title": "AI医疗诊断技术调研",
                    "description": "调研当前AI在医疗诊断中的主要技术和应用",
                    "execution_res": "AI在医疗诊断中主要应用包括医学影像分析、病理诊断、药物发现等领域..."
                }
            ]
        }, ensure_ascii=False),
        "research_thinking": "",
        "research_results": json.dumps([
            {
                "title": "AI医疗诊断技术调研",
                "description": "调研当前AI在医疗诊断中的主要技术和应用",
                "result": "AI在医疗诊断中主要应用包括医学影像分析、病理诊断、药物发现等领域。深度学习技术在医学影像识别方面取得了显著进展，准确率已接近甚至超过专业医生水平。"
            }
        ], ensure_ascii=False),
        "locale": "zh-CN"
    }
    
    try:
        # Apply the template
        messages = apply_prompt_template("outline_writer", test_state, Configuration())
        
        print("Template application successful!")
        print(f"Number of messages: {len(messages)}")
        print(f"System prompt length: {len(messages[0]['content']) if messages else 0}")
        
        # Print first 500 characters of system prompt
        if messages:
            print("\nFirst 500 characters of system prompt:")
            print(messages[0]['content'][:500])
            print("...")
            
        return True
        
    except Exception as e:
        print(f"Template application failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_outline_template()
    sys.exit(0 if success else 1) 