#!/usr/bin/env python3
"""
Test RAG Document Retrieval Functionality

This script tests document retrieval through DeerFlow's RAG integration.
"""

import requests
import json
import os

# Set environment variables for mock server
os.environ["RAGFLOW_API_URL"] = "http://localhost:9380"
os.environ["RAGFLOW_API_KEY"] = "mock-api-key-for-testing"

# Test configuration
DEERFLOW_API_URL = "http://localhost:8000"

def test_document_retrieval():
    """Test document retrieval through chat interface."""
    print("🔍 Testing RAG Document Retrieval")
    print("=" * 50)
    
    # First, get available resources
    print("📋 Getting available resources...")
    url = f"{DEERFLOW_API_URL}/api/rag/resources"
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"❌ Failed to get resources: {response.status_code}")
        return
    
    resources = response.json().get("resources", [])
    if not resources:
        print("❌ No resources found. Please run test_rag_upload.py first.")
        return
    
    print(f"✅ Found {len(resources)} resources:")
    for i, resource in enumerate(resources):
        print(f"   {i+1}. {resource['title']}")
    
    # Use the first resource for testing
    test_resource = resources[0]
    print(f"\n🎯 Using resource: {test_resource['title']}")
    
    # Test queries
    test_queries = [
        "What is artificial intelligence?",
        "Tell me about DeerFlow features",
        "What are the technical requirements?",
        "How does machine learning work?"
    ]
    
    print("\n🔍 Testing document queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: {query}")
        
        # Create a simple chat request to test retrieval
        chat_request = {
            "messages": [
                {"role": "user", "content": query}
            ],
            "thread_id": f"test_retrieval_{i}",
            "resources": [test_resource],  # Use the test resource
            "max_plan_iterations": 1,
            "max_step_num": 3,
            "max_search_results": 5,
            "auto_accepted_plan": True,
            "interrupt_feedback": "",
            "mcp_settings": {},
            "enable_background_investigation": False
        }
        
        # Test streaming chat with RAG
        url = f"{DEERFLOW_API_URL}/api/chat/stream"
        try:
            response = requests.post(url, json=chat_request, stream=True, timeout=30)
            
            if response.status_code == 200:
                print("✅ Query processed successfully")
                
                # Collect streaming response
                response_content = ""
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix
                            try:
                                event_data = json.loads(data)
                                if 'content' in event_data and event_data['content']:
                                    response_content += event_data['content']
                            except json.JSONDecodeError:
                                continue
                
                # Show first 200 characters of response
                if response_content:
                    preview = response_content[:200] + "..." if len(response_content) > 200 else response_content
                    print(f"📄 Response preview: {preview}")
                else:
                    print("📄 No content in response")
                    
            else:
                print(f"❌ Query failed: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("⏰ Query timed out")
        except Exception as e:
            print(f"❌ Query error: {str(e)}")
    
    print("\n🎉 RAG retrieval test completed!")


def test_direct_retrieval():
    """Test direct document retrieval using the retriever."""
    print("\n🔍 Testing Direct Document Retrieval")
    print("=" * 50)
    
    try:
        # Import and test the retriever directly
        from src.rag.builder import build_retriever
        from src.rag.retriever import Resource
        
        retriever = build_retriever()
        if not retriever:
            print("❌ No retriever available")
            return
        
        print("✅ Retriever built successfully")
        
        # Get available resources
        resources = retriever.list_resources()
        if not resources:
            print("❌ No resources found")
            return
        
        print(f"✅ Found {len(resources)} resources")
        
        # Test queries
        test_queries = [
            "artificial intelligence machine learning",
            "DeerFlow research assistant",
            "technical specifications requirements"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Searching for: {query}")
            
            try:
                documents = retriever.query_relevant_documents(query, resources[:1])
                print(f"✅ Found {len(documents)} relevant documents")
                
                for doc in documents:
                    print(f"   📄 {doc.title} - {len(doc.chunks)} chunks")
                    for chunk in doc.chunks[:2]:  # Show first 2 chunks
                        preview = chunk.content[:100] + "..." if len(chunk.content) > 100 else chunk.content
                        print(f"      - Similarity: {chunk.similarity:.3f} | {preview}")
                        
            except Exception as e:
                print(f"❌ Search error: {str(e)}")
    
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
    except Exception as e:
        print(f"❌ Direct retrieval error: {str(e)}")


def main():
    """Main test function."""
    print("🚀 DeerFlow RAG Retrieval Test")
    print("=" * 60)
    
    # Test 1: Document retrieval through chat interface
    test_document_retrieval()
    
    # Test 2: Direct retrieval using the retriever
    test_direct_retrieval()
    
    print("\n" + "=" * 60)
    print("All RAG retrieval tests completed!")


if __name__ == "__main__":
    main() 