#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新的update_messages_to_database函数使用示例
演示如何使用新的参数格式追加消息到数据库
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.graph.nodes import update_messages_to_database


def example_conversation_flow():
    """
    演示一个完整的对话流程中如何记录消息
    """
    
    # 假设的thread_id
    thread_id = "example-thread-001"
    
    print("🔄 模拟对话流程...")
    
    # 1. 用户输入
    print("1. 记录用户输入")
    update_messages_to_database(
        thread_id=thread_id,
        agent="user",
        content="请帮我写一篇关于人工智能的研究报告",
        msg_type="human"
    )
    
    # 2. Coordinator处理
    print("2. 记录Coordinator响应")
    update_messages_to_database(
        thread_id=thread_id,
        agent="coordinator",
        content="我将帮您创建一个关于人工智能的研究报告计划",
        msg_type="ai"
    )
    
    # 3. Planner生成计划
    print("3. 记录Planner计划")
    planner_output = """
    {
        "title": "人工智能研究报告",
        "steps": [
            {"type": "research", "description": "收集AI发展历史"},
            {"type": "research", "description": "分析当前AI技术趋势"},
            {"type": "processing", "description": "生成最终报告"}
        ]
    }
    """
    update_messages_to_database(
        thread_id=thread_id,
        agent="planner",
        content=planner_output.strip(),
        msg_type="ai"
    )
    
    # 4. Researcher执行研究
    print("4. 记录Researcher研究结果")
    research_results = [
        "AI发展始于1950年代，经历了多次繁荣与低谷期",
        "当前AI技术主要集中在深度学习、自然语言处理和计算机视觉",
        "GPT、BERT等大语言模型推动了AI的最新发展"
    ]
    
    for i, result in enumerate(research_results, 1):
        update_messages_to_database(
            thread_id=thread_id,
            agent="researcher",
            content=f"研究发现{i}: {result}",
            msg_type="ai"
        )
    
    # 5. Reporter生成最终报告
    print("5. 记录Reporter最终报告")
    final_report = """
    # 人工智能研究报告
    
    ## 概述
    人工智能(AI)作为21世纪最重要的技术革命之一...
    
    ## 发展历史
    AI发展始于1950年代...
    
    ## 当前趋势
    当前AI技术主要集中在...
    
    ## 结论
    AI技术将继续推动社会进步...
    """
    
    update_messages_to_database(
        thread_id=thread_id,
        agent="reporter",
        content=final_report.strip(),
        msg_type="ai"
    )
    
    print("✅ 对话流程记录完成!")
    print(f"📝 Thread ID: {thread_id}")
    print("💡 您可以通过ChatHistory API查询这些消息历史")


def example_error_handling():
    """
    演示错误处理情况
    """
    print("\n🚨 测试错误处理...")
    
    # 测试空thread_id
    print("1. 测试空thread_id")
    update_messages_to_database("", "test_agent", "测试内容", "human")
    
    # 测试空内容
    print("2. 测试空内容") 
    update_messages_to_database("test-thread", "test_agent", "", "human")
    
    # 测试不存在的thread_id
    print("3. 测试不存在的thread_id")
    update_messages_to_database("nonexistent-thread", "test_agent", "测试内容", "human")
    
    print("✅ 错误处理测试完成!")


def example_agent_types():
    """
    演示不同类型的代理记录
    """
    thread_id = "agent-types-demo"
    
    print("\n🤖 演示不同类型的代理...")
    
    agents_demo = [
        ("user", "用户原始查询", "human"),
        ("coordinator", "协调器分析结果", "ai"),
        ("planner", "规划器生成的计划", "ai"),
        ("researcher", "研究员收集的信息", "ai"),
        ("coder", "代码分析结果", "ai"),
        ("thinking", "思考分析内容", "ai"),
        ("outline_writer", "大纲生成结果", "ai"),
        ("paper_writer", "论文章节内容", "ai"),
        ("references_writer", "参考文献列表", "ai"),
        ("reporter", "最终报告", "ai"),
        ("system", "系统状态信息", "system")
    ]
    
    for agent, content, msg_type in agents_demo:
        print(f"  📝 记录 {agent} 消息")
        update_messages_to_database(
            thread_id=thread_id,
            agent=agent,
            content=f"{content} - 来自{agent}的输出",
            msg_type=msg_type
        )
    
    print(f"✅ 已记录 {len(agents_demo)} 个不同代理的消息!")
    print(f"📝 Thread ID: {thread_id}")


if __name__ == "__main__":
    print("🚀 新的update_messages_to_database函数使用示例\n")
    
    try:
        # 运行示例
        example_conversation_flow()
        example_error_handling()
        example_agent_types()
        
        print("\n🎉 所有示例运行完成!")
        print("\n📚 使用说明:")
        print("- 每次调用都会追加新消息到数据库")
        print("- 支持记录任何代理的输出")
        print("- 自动处理错误情况")
        print("- 完整保留对话历史")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        sys.exit(1) 