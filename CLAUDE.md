# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Python)
```bash
# Run console UI (quickest way to test functionality)
uv run main.py

# Run FastAPI development server with auto-reload
uv run server.py

# Run tests with coverage
uv run pytest tests/ --cov=src --cov-report=term-missing

# Format code with Black
uv run black --preview .

# Check code formatting
uv run black --check .

# Install dependencies
uv sync
```

### Frontend (Next.js)
```bash
# Navigate to web directory first
cd web

# Start Next.js development server
pnpm dev

# Build for production
pnpm build

# Type checking
pnpm typecheck

# Lint and fix issues
pnpm lint:fix

# Format code
pnpm format:write
```

### Full Stack Development
```bash
# Run both backend and frontend in development mode
./bootstrap.sh -d    # macOS/Linux
bootstrap.bat -d     # Windows
```

### Testing & Code Quality
```bash
# Backend testing
make test           # Run all tests
make coverage       # Run tests with coverage report
make lint          # Check code formatting
make format        # Format code

# Frontend testing
cd web && pnpm typecheck  # Type checking
cd web && pnpm lint       # Lint checking
```

## Architecture Overview

**DeerFlow** is a multi-agent research framework built on LangGraph with a Next.js frontend. The system orchestrates AI agents to conduct deep research, write comprehensive reports, and generate multimedia content.

### Core Workflow Components

1. **Coordinator** (`src/graph/nodes.py:coordinator_node`) - Entry point that manages workflow lifecycle
2. **Planner** (`src/graph/nodes.py:planner_node`) - Creates structured research plans and manages execution flow
3. **Research Team**:
   - **Researcher** (`src/graph/nodes.py:researcher_node`) - Conducts web searches using tools like Tavily, Brave Search
   - **Coder** (`src/graph/nodes.py:coder_node`) - Handles code analysis and Python execution
4. **Reporter** (`src/graph/nodes.py:reporter_node`) - Generates final research reports

### Key Directories

- `src/graph/` - LangGraph workflow definitions and node implementations
- `src/agents/` - AI agent configurations and prompt management
- `src/tools/` - Research tools (web search, crawling, Python REPL)
- `src/prompts/` - Jinja2 prompt templates for different agents
- `src/server/` - FastAPI backend with streaming endpoints
- `web/src/` - Next.js frontend with React components and API integration

### Configuration Management

- `conf.yaml` - LLM model configurations, API endpoints, system settings
- `.env` - API keys and secrets (Tavily, Brave Search, OpenAI, etc.)
- `src/config/` - Configuration loading and validation modules

### Specialized Features

- **RAG Integration**: Supports RAGFlow for private knowledge base queries
- **MCP Integration**: Model Context Protocol for extended capabilities
- **Podcast Generation**: Text-to-speech workflow in `src/podcast/`
- **PowerPoint Generation**: Automated presentation creation in `src/ppt/`
- **Human-in-the-Loop**: Interactive plan review and modification

## Development Guidelines

### Python Code Style
- **Formatter**: Black with 88-character line length
- **Python Version**: 3.12+
- **Dependency Management**: Use `uv` for package management
- **Configuration**: All settings in `pyproject.toml`

### Frontend Standards
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS with Radix UI components
- **State Management**: Zustand for global state
- **Rich Text Editor**: Tiptap with Novel integration

### Testing Strategy
- **Backend**: pytest with minimum 25% coverage requirement
- **Test Location**: `tests/` directory for unit and integration tests
- **Frontend**: TypeScript compilation as primary testing method
- **API Testing**: FastAPI automatic documentation at `/docs`

### Environment Setup
- **Python**: Managed by `uv` with automatic virtual environment creation
- **Node.js**: Version 22+ with `pnpm` for package management
- **Additional Tools**: `marp-cli` for PowerPoint generation

## Important Files and Patterns

### Entry Points
- `main.py` - Console UI entry point for quick testing
- `server.py` - FastAPI backend server
- `src/workflow.py` - Main LangGraph workflow definition

### Configuration Files
- `langgraph.json` - Defines available workflows for LangGraph CLI
- `bootstrap.sh`/`bootstrap.bat` - Development environment setup scripts
- `Dockerfile` and `docker-compose.yml` - Container deployment

### Development Tools
- **LangGraph Studio**: Use `langgraph dev` for workflow visualization and debugging
- **LangSmith Tracing**: Optional tracing for workflow monitoring
- **Docker Support**: Full containerization for development and deployment

## Workflow Debugging

When debugging LangGraph workflows:
1. Use `langgraph dev` to start LangGraph Studio
2. Access Studio UI at the provided URL for real-time workflow visualization
3. Enable LangSmith tracing in `.env` for detailed execution traces
4. Use FastAPI `/docs` endpoint for API debugging