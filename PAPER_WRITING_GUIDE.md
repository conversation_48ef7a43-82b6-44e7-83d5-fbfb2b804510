# 📝 DeerFlow 论文写作功能指南

DeerFlow 现在支持学术论文写作功能，可以基于深度调研结果生成高质量的学术论文。该功能包括两个主要阶段：大纲撰写和论文写作。

## 🌟 功能特性

### 📋 大纲撰写 (Outline Writer)
- **智能大纲生成**：基于调研结果自动生成详细的论文大纲
- **多种写作场景**：支持学术写作、深度研究、营销文案、视频脚本等
- **字数分配**：为每个章节分配合理的字数
- **引用管理**：自动整理和格式化引用内容
- **人工审核**：支持用户审核和修改大纲

### ✍️ 论文写作 (Paper Writer)
- **分段写作**：按章节逐步生成论文内容
- **学术规范**：遵循学术写作标准和引用格式
- **图表支持**：支持插入图表和图片增强可读性
- **引用追踪**：准确追踪和引用研究资料
- **多语言支持**：支持中文、英文等多种语言

## 🚀 使用方法

### 1. 启动工作流
```bash
# 启动控制台界面
uv run main.py

# 或启动Web界面
./bootstrap.sh -d
```

### 2. 输入研究需求
输入您的论文写作需求，例如：
```
撰写一篇关于人工智能技术在医疗诊断领域应用的系统性文献综述，涵盖2018-2023年间的研究进展，重点分析当前技术成果、临床应用案例、存在的挑战以及未来发展方向。
```

### 3. 选择论文写作模式
在计划审核阶段，选择以下选项之一：
- `[ACCEPTED]` - 生成研究报告（默认）
- `[PAPER_WRITING]` - 进入论文写作流程
- `[EDIT_PLAN]` - 修改研究计划

### 4. 审核大纲
系统会生成详细的论文大纲，您可以：
- `[ACCEPTED]` - 接受大纲并开始写作
- `[EDIT_OUTLINE]` - 修改大纲内容

### 5. 自动生成论文
系统会按章节自动生成完整的学术论文。

## 📁 工作流程图

```mermaid
graph TD
    A[用户输入] --> B[背景调研]
    B --> C[制定计划]
    C --> D[人工审核]
    D -->|[PAPER_WRITING]| E[大纲撰写]
    D -->|[ACCEPTED]| F[研究报告]
    E --> G[大纲审核]
    G -->|[ACCEPTED]| H[论文写作]
    G -->|[EDIT_OUTLINE]| E
    H --> I[章节生成]
    I --> J{所有章节完成?}
    J -->|否| I
    J -->|是| K[最终论文]
    K --> L[报告输出]
```

## 🔧 技术实现

### 核心组件

#### 1. 状态管理 (`src/graph/types.py`)
```python
# 论文写作相关状态变量
paper_outline: str = None           # 生成的大纲
approved_outline: str = None        # 审核通过的大纲
current_section_index: int = 0      # 当前写作章节索引
paper_sections: list[str] = []      # 已完成的章节
final_paper: str = ""              # 最终论文
auto_accepted_outline: bool = False # 自动接受大纲
```

#### 2. 工作流节点 (`src/graph/nodes.py`)

**大纲撰写节点 (`outline_writer_node`)**
- 基于调研结果生成详细大纲
- 支持多种写作场景和风格
- 自动分配字数和整理引用

**大纲审核节点 (`outline_feedback_node`)**
- 支持人工审核和修改
- 提供交互式反馈机制

**论文写作节点 (`paper_writer_node`)**
- 按章节逐步生成内容
- 支持图表插入和引用管理
- 自动组装最终论文

#### 3. 提示词模板

**大纲写作模板 (`src/prompts/outline_writer.md`)**
- 专业的学术大纲生成指导
- 支持多种写作场景
- 详细的输出格式要求

**论文写作模板 (`src/prompts/paper_writer.md`)**
- 学术写作标准和规范
- 引用格式和图表支持
- 分段写作指导

### 配置文件

#### Agent配置 (`src/config/agents.py`)
```python
AGENT_LLM_MAP: dict[str, LLMType] = {
    # ... 其他agent
    "outline_writer": "basic",
    "paper_writer": "basic",
}
```

#### 工作流配置 (`langgraph.json`)
```json
{
  "graphs": {
    "deep_research": "./src/workflow.py:graph",
    "paper_writing": "./src/workflow.py:graph"
  }
}
```

## 🧪 测试和验证

### 运行测试
```bash
# 运行论文写作功能测试
python test_paper_writing.py
```

### 测试用例
测试脚本包含以下验证：
- 完整的论文写作工作流
- 大纲生成功能
- 章节写作功能
- 错误处理机制

## 📝 输出格式

### 大纲格式 (JSON)
```json
{
  "title": "论文标题",
  "abstract": "论文摘要",
  "totalWords": "预期总字数",
  "sections": [
    {
      "sectionTitle": "章节标题",
      "sectionSummary": "章节摘要",
      "detailedWords": "分配字数",
      "quotes": [
        {
          "text": "引用内容",
          "source_id": "引用ID",
          "citation": "引用格式"
        }
      ],
      "subsections": [
        {
          "subheadingTitle": "子章节标题",
          "content": "子章节内容"
        }
      ]
    }
  ]
}
```

### 论文内容格式 (Markdown)
```markdown
# 论文标题

## Abstract
论文摘要内容...

## 第一章 引言
章节内容...

[[Image:"图表描述"]]

根据研究显示(Smith et al., 2020)...

## 参考文献
- [Source Title](URL)
- [Another Source](URL)
```

## 🔍 故障排除

### 常见问题

1. **大纲生成失败**
   - 检查调研结果是否充分
   - 验证提示词模板格式
   - 确认LLM配置正确

2. **论文写作中断**
   - 检查大纲JSON格式
   - 验证章节索引状态
   - 确认内存和资源充足

3. **引用格式错误**
   - 检查调研阶段的引用信息
   - 验证引用模板格式
   - 确认引用ID追踪正确

### 调试方法
```bash
# 启用调试日志
export LOG_LEVEL=DEBUG

# 检查工作流状态
langgraph dev

# 验证配置文件
python -c "import json; json.load(open('langgraph.json'))"
```

## 🚀 扩展和定制

### 添加新的写作场景
1. 修改 `src/prompts/outline_writer.md` 添加新场景
2. 更新 `src/prompts/paper_writer.md` 添加对应写作风格
3. 测试新场景的效果

### 自定义引用格式
1. 修改提示词模板中的引用格式要求
2. 更新引用处理逻辑
3. 验证引用格式输出

### 集成新的LLM模型
1. 在 `src/config/agents.py` 中配置新模型
2. 更新 `conf.yaml` 中的模型设置
3. 测试新模型的写作效果

## 📚 相关文档

- [项目概览](.cursor/rules/project-overview.mdc)
- [后端开发指南](.cursor/rules/backend-development.mdc)
- [配置管理指南](.cursor/rules/configuration-guide.mdc)
- [开发工作流](.cursor/rules/development-workflow.mdc)
- [故障排除指南](.cursor/rules/troubleshooting-guide.mdc)

## 🤝 贡献

欢迎为论文写作功能贡献代码和改进建议：

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建 Pull Request

## 📄 许可证

本功能遵循项目的 MIT 许可证。 