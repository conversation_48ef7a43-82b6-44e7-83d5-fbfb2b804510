#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage

def test_outline_empty_research():
    """Test outline writer with empty research results."""
    
    # Create a plan with NO research results (empty execution_res)
    plan = Plan(
        locale="zh-CN",
        has_enough_context=True,
        title="人工智能在医疗诊断中的应用研究",
        thought="需要深入研究AI技术在医疗诊断领域的应用现状、技术挑战和发展前景",
        steps=[
            Step(
                need_web_search=True,
                title="AI医疗诊断技术调研",
                description="调研当前AI在医疗诊断中的主要技术和应用",
                step_type=StepType.RESEARCH,
                execution_res=""  # Empty result
            ),
            Step(
                need_web_search=False,
                title="技术挑战分析", 
                description="分析AI医疗诊断面临的技术挑战",
                step_type=StepType.PROCESSING,
                execution_res=None  # None result
            )
        ]
    )
    
    # Create state similar to what outline_writer_node receives
    state = {
        "messages": [HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文")],
        "current_plan": plan,
        "locale": "zh-CN"
    }
    
    try:
        # Same logic as outline_writer_node
        current_plan = state.get("current_plan")
        research_results = ""
        research_thinking = ""
        
        # Collect research results from completed steps
        if current_plan and current_plan.steps:
            research_results_list = []
            for step in current_plan.steps:
                if step.execution_res:  # This will be False for empty/None
                    research_results_list.append({
                        "title": step.title,
                        "description": step.description,
                        "result": step.execution_res
                    })
            research_results = json.dumps(research_results_list, ensure_ascii=False)
            print(f"Found {len(research_results_list)} research results")
        else:
            print("No current plan or no steps found")
            research_results = json.dumps([], ensure_ascii=False)
        
        # Get user query
        user_query = ""
        if state["messages"]:
            for msg in state["messages"]:
                if hasattr(msg, 'content'):
                    user_query = msg.content
                    break
        
        print(f"User query: {user_query}")
        print(f"Research results: {research_results}")
        
        # Apply template
        template_state = {
            **state,
            "user_query": user_query,
            "current_plan": json.dumps(current_plan.model_dump() if current_plan else {}, ensure_ascii=False),
            "research_thinking": research_thinking,
            "research_results": research_results,
            "locale": state.get("locale", "en-US")
        }
        
        messages = apply_prompt_template("outline_writer", template_state, Configuration())
        
        print(f"Generated {len(messages)} messages")
        print(f"System prompt length: {len(messages[0]['content'])}")
        
        # Test with LLM
        llm = get_llm_by_type("basic")
        print("Calling LLM with empty research results...")
        
        full_response = ""
        response = llm.stream(messages)
        for chunk in response:
            full_response += chunk.content
        
        print(f"Response length: {len(full_response)}")
        print(f"Response: {full_response[:500]}...")
        
        if not full_response.strip():
            print("ERROR: Response is empty with empty research results!")
            return False
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_outline_empty_research()
    sys.exit(0 if success else 1) 