#!/usr/bin/env python3
"""
RAG自动解析功能简单示例

展示如何使用DeerFlow的自动解析功能上传和处理文档
"""

import requests
import io
import time

# 配置
DEERFLOW_URL = "http://localhost:8000"

def demo_auto_parse():
    """演示自动解析功能"""
    print("🚀 RAG自动解析功能演示")
    print("=" * 40)
    
    # 步骤1: 创建数据集
    print("1️⃣ 创建测试数据集...")
    dataset_response = requests.post(f"{DEERFLOW_URL}/api/rag/datasets", json={
        "name": f"演示数据集 {int(time.time())}",
        "description": "自动解析功能演示"
    })
    
    if dataset_response.status_code != 200:
        print(f"❌ 数据集创建失败: {dataset_response.text}")
        return
    
    dataset_id = dataset_response.json()["dataset"]["id"]
    print(f"✅ 数据集创建成功: {dataset_id}")
    
    # 步骤2: 上传文档并启用自动解析
    print(f"\n2️⃣ 上传文档（启用自动解析）...")
    
    # 创建示例文档
    document_content = """
RAG系统使用指南

本文档介绍了如何使用RAG（检索增强生成）系统。

主要功能：
1. 文档上传和管理
2. 自动文档解析和索引
3. 语义检索和问答

自动解析功能：
- 上传后立即开始解析
- 支持多种文档格式
- 自动建立语义索引

使用方法：
1. 创建数据集
2. 上传文档（启用auto_parse=true）
3. 等待解析完成
4. 开始使用检索功能

优势：
- 简化工作流程
- 提高处理效率
- 减少手动操作
    """.strip()
    
    # 准备上传
    files = {
        'file': ('demo_guide.txt', io.BytesIO(document_content.encode('utf-8')), 'text/plain')
    }
    data = {
        'dataset_id': dataset_id,
        'auto_parse': 'true'  # 启用自动解析
    }
    
    upload_response = requests.post(f"{DEERFLOW_URL}/api/rag/upload", files=files, data=data)
    
    if upload_response.status_code != 200:
        print(f"❌ 文档上传失败: {upload_response.text}")
        return
    
    result = upload_response.json()
    print(f"✅ 文档上传成功!")
    
    # 检查自动解析状态
    if result["data"].get("parse_started"):
        print(f"🔄 自动解析已启动，文档ID: {result['data']['parsed_document_ids']}")
    else:
        print(f"⚠️ 自动解析未启动")
    
    # 步骤3: 等待解析完成
    print(f"\n3️⃣ 等待解析完成...")
    document_id = result["data"]["documents"][0]["id"]
    
    # 简单等待
    time.sleep(10)
    
    # 检查解析状态
    docs_response = requests.get(f"{DEERFLOW_URL}/api/rag/datasets/{dataset_id}/documents")
    if docs_response.status_code == 200:
        docs = docs_response.json()["documents"]
        target_doc = None
        for doc in docs:
            if doc["id"] == document_id:
                target_doc = doc
                break
        
        if target_doc:
            status = target_doc["run"]
            if status == "DONE":
                print(f"✅ 解析完成! 分块数: {target_doc['chunk_count']}")
            else:
                print(f"⏳ 解析状态: {status}")
    
    # 步骤4: 测试检索
    print(f"\n4️⃣ 测试文档检索...")
    
    chat_response = requests.post(f"{DEERFLOW_URL}/api/chat/stream", json={
        "messages": [
            {
                "role": "user",
                "content": "这个文档介绍了什么主要功能？"
            }
        ],
        "resources": [
            {
                "uri": f"rag://dataset/{dataset_id}",
                "title": "演示数据集",
                "description": "自动解析功能演示"
            }
        ],
        "stream": False
    })
    
    if chat_response.status_code == 200:
        print(f"✅ 检索测试成功!")
        print(f"📝 可以开始使用数据集进行问答了")
    else:
        print(f"❌ 检索测试失败: {chat_response.status_code}")
    
    print(f"\n🎉 自动解析功能演示完成!")
    print(f"💡 要点总结：")
    print(f"   - 设置 auto_parse=true 启用自动解析")
    print(f"   - 上传后系统自动调用解析API")
    print(f"   - 解析完成后即可进行检索")
    print(f"   - 大大简化了文档处理流程")

if __name__ == "__main__":
    try:
        demo_auto_parse()
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        print(f"💡 请确保DeerFlow服务器正在运行: python -m uvicorn src.server.app:app --reload --host 0.0.0.0 --port 8000") 