#!/usr/bin/env python3
"""
Token管理功能测试脚本

测试token统计、积分检查和API端点功能
"""

import requests
import json
import time
import os
from typing import Dict

# 配置
DEERFLOW_API_URL = "http://localhost:8000"

def test_login_and_get_token():
    """测试登录并获取token"""
    print("🔐 测试用户登录...")
    
    # 使用邮箱验证码登录
    email = "<EMAIL>"
    
    # 发送验证码
    send_code_response = requests.post(f"{DEERFLOW_API_URL}/api/auth/send-verification", 
                                     json={"email": email})
    
    if send_code_response.status_code != 200:
        print(f"❌ 发送验证码失败: {send_code_response.status_code}")
        print(f"响应: {send_code_response.text}")
        return None
    
    print("✅ 验证码发送成功")
    
    # 模拟验证码（实际场景中需要从邮箱获取）
    verification_code = "123456"  # 这需要根据实际发送的验证码进行调整
    
    # 登录
    login_response = requests.post(f"{DEERFLOW_API_URL}/api/auth/login/email",
                                 json={"email": email, "verification_code": verification_code})
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"响应: {login_response.text}")
        return None
    
    login_data = login_response.json()
    access_token = login_data.get("access_token")
    
    if not access_token:
        print("❌ 未获取到访问令牌")
        return None
    
    print("✅ 登录成功")
    return access_token

def test_user_credits(access_token: str):
    """测试用户积分查询"""
    print("\n💰 测试用户积分查询...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{DEERFLOW_API_URL}/api/user/credits", headers=headers)
    
    if response.status_code != 200:
        print(f"❌ 查询积分失败: {response.status_code}")
        return None
    
    credits_data = response.json()
    print(f"✅ 用户积分信息:")
    print(f"   总积分: {credits_data['total_credits']}")
    print(f"   剩余积分: {credits_data['remaining_credits']}")
    print(f"   已使用积分: {credits_data['used_credits']}")
    print(f"   每积分token数: {credits_data['tokens_per_credit']}")
    print(f"   最小积分阈值: {credits_data['min_credits_threshold']}")
    
    return credits_data

def test_chat_with_token_tracking(access_token: str):
    """测试聊天流并跟踪token使用量"""
    print("\n💬 测试聊天流和token统计...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # 构造聊天请求
    chat_request = {
        "messages": [
            {"role": "user", "content": "你好，请简单介绍一下人工智能的基本概念。"}
        ],
        "thread_id": "__default__",
        "resources": [],
        "max_plan_iterations": 1,
        "max_step_num": 2,
        "max_search_results": 3,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": False
    }
    
    print("📤 发送聊天请求...")
    response = requests.post(f"{DEERFLOW_API_URL}/api/chat/stream", 
                           headers=headers, 
                           json=chat_request,
                           stream=True)
    
    if response.status_code != 200:
        print(f"❌ 聊天请求失败: {response.status_code}")
        print(f"响应: {response.text}")
        return None
    
    # 解析流响应
    thread_id = None
    node_count = 0
    
    print("📥 接收流响应...")
    for line in response.iter_lines(decode_unicode=True):
        if line.startswith("data: "):
            try:
                data = json.loads(line[6:])  # 去掉 "data: " 前缀
                
                if not thread_id and data.get("thread_id"):
                    thread_id = data["thread_id"]
                    print(f"🔗 线程ID: {thread_id}")
                
                # 统计节点完成情况
                if data.get("finish_reason"):
                    node_count += 1
                    print(f"✅ 节点 {node_count} 完成: {data.get('agent', 'unknown')}")
                
                # 检查是否有积分不足消息
                if data.get("finish_reason") == "credits_insufficient":
                    print(f"⚠️ 积分不足: {data.get('content', '')}")
                    break
                    
            except json.JSONDecodeError:
                continue
            except Exception as e:
                print(f"解析响应错误: {e}")
                continue
    
    print(f"🎯 聊天完成，共处理 {node_count} 个节点")
    return thread_id

def test_token_usage_query(access_token: str, thread_id: str):
    """测试token使用量查询"""
    print(f"\n📊 测试token使用量查询 (线程: {thread_id})...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{DEERFLOW_API_URL}/api/token/usage/{thread_id}", headers=headers)
    
    if response.status_code != 200:
        print(f"❌ 查询token使用量失败: {response.status_code}")
        return None
    
    usage_data = response.json()
    print(f"✅ Token使用量统计:")
    print(f"   当前会话 - 输入: {usage_data['current_session']['input_tokens']}, 输出: {usage_data['current_session']['output_tokens']}")
    print(f"   历史记录 - 输入: {usage_data['historical']['input_tokens']}, 输出: {usage_data['historical']['output_tokens']}")
    print(f"   总计 - 输入: {usage_data['total']['input_tokens']}, 输出: {usage_data['total']['output_tokens']}")
    print(f"   总token数: {usage_data['total_tokens']}")
    print(f"   消耗积分: {usage_data['credits_used']:.2f}")
    print(f"   用户剩余积分: {usage_data['user_remaining_credits']}")
    
    return usage_data

def test_credits_insufficient_scenario(access_token: str):
    """测试积分不足场景"""
    print("\n⚠️ 测试积分不足场景...")
    
    # 首先查询当前积分
    headers = {"Authorization": f"Bearer {access_token}"}
    credits_response = requests.get(f"{DEERFLOW_API_URL}/api/user/credits", headers=headers)
    
    if credits_response.status_code != 200:
        print("❌ 无法获取当前积分信息")
        return
    
    credits_data = credits_response.json()
    current_credits = credits_data["remaining_credits"]
    
    print(f"当前剩余积分: {current_credits}")
    
    if current_credits >= 10:
        print("💡 当前积分充足，无法测试积分不足场景")
        print("提示：可以手动在数据库中将用户剩余积分设置为5以下来测试此功能")
        return
    
    # 尝试发起聊天请求
    chat_request = {
        "messages": [
            {"role": "user", "content": "请详细介绍机器学习的基本原理和应用。"}
        ],
        "thread_id": "__default__",
        "resources": [],
        "max_plan_iterations": 1,
        "max_step_num": 3,
        "max_search_results": 5,
        "auto_accepted_plan": True,
        "interrupt_feedback": "",
        "mcp_settings": {},
        "enable_background_investigation": False
    }
    
    print("📤 发送聊天请求（积分不足测试）...")
    response = requests.post(f"{DEERFLOW_API_URL}/api/chat/stream", 
                           headers=headers, 
                           json=chat_request,
                           stream=True)
    
    if response.status_code == 403:
        print("✅ 正确拦截：积分不足，无法开始聊天")
        print(f"响应: {response.json()}")
        return
    
    print("⚠️ 聊天请求已接受，等待积分检查...")

def main():
    """主测试函数"""
    print("🧪 Token管理功能测试")
    print("=" * 50)
    
    # 1. 登录获取token
    access_token = test_login_and_get_token()
    if not access_token:
        print("❌ 登录失败，无法继续测试")
        return
    
    # 2. 查询用户积分
    credits_data = test_user_credits(access_token)
    if not credits_data:
        print("❌ 无法获取积分信息")
        return
    
    # 3. 测试聊天和token统计
    thread_id = test_chat_with_token_tracking(access_token)
    if not thread_id:
        print("❌ 聊天测试失败")
        return
    
    # 等待一段时间确保token统计完成
    print("⏳ 等待token统计完成...")
    time.sleep(2)
    
    # 4. 查询token使用量
    usage_data = test_token_usage_query(access_token, thread_id)
    if not usage_data:
        print("❌ 无法查询token使用量")
        return
    
    # 5. 测试积分不足场景
    test_credits_insufficient_scenario(access_token)
    
    print("\n🎉 Token管理功能测试完成！")
    print("\n📋 测试总结:")
    print("✅ 用户登录和认证")
    print("✅ 积分查询API")
    print("✅ 聊天流处理")
    print("✅ Token使用量统计")
    print("✅ Token使用量查询API")
    print("✅ 积分不足检查")

if __name__ == "__main__":
    main() 