# 大纲引用规划功能更新

本文档总结了对DeerFlow论文写作工作流程中大纲生成器的重要更新，实现了有URL来源的引用规划功能。

## 🎯 更新背景

用户反馈希望撤回之前对大纲的修改，认为大纲里面可以规划要引用的内容，但必须是有具体URL来源的引用内容，这对于引用的连贯性有参考作用。

## 📝 更新内容

### 1. 大纲生成器更新 (`src/prompts/outline_writer_simple.md`)

#### 主要变化：
- ✅ 允许规划有具体URL来源的引用内容
- ✅ 避免规划宽泛的思考分析或一般性知识
- ✅ 在输出格式中增加`plannedCitations`字段
- ✅ 添加详细的引用规划指导原则

#### 新增功能：

1. **引用规划指导原则**：
```markdown
## Citation Planning Guidelines
- **DO plan citations for**: Research materials with URLs, specific studies, data sources with links
- **DO NOT plan citations for**: General knowledge, thinking-stage analysis, broad conceptual frameworks
- Citations in outline are for planning purposes - to ensure coherent reference flow
- Each planned citation should correspond to a specific research finding with a verifiable source
```

2. **新的输出格式**：
```json
{
  "sections": [
    {
      "sectionTitle": "Section Name",
      "sectionSummary": "What this section covers",
      "detailedWords": "800-1200",
      "subsections": [...],
      "plannedCitations": [
        {
          "title": "Source Title from Research Materials",
          "url": "https://example.com",
          "relevance": "How this source supports this section's content"
        }
      ]
    }
  ]
}
```

### 2. 论文写作器更新 (`src/prompts/paper_writer_simple.md`)

#### 增强功能：
- ✅ 增加对大纲中`plannedCitations`的引用指导
- ✅ 优先使用大纲中规划的引用以保持连贯性
- ✅ 保持严格的URL验证要求

#### 新增指导：
```markdown
2. **Citation Requirements**:
   - **REFERENCE**: Check the approved outline for plannedCitations in the current section for guidance
   - Prioritize using citations that were planned in the outline for coherent reference flow

# Notes
- **Reference the plannedCitations in the current section of the approved outline for citation guidance**
- The outline's plannedCitations help maintain coherent reference flow throughout the paper
```

## 🔄 工作流程改进

### 新的引用规划流程：

1. **研究阶段**：收集有URL来源的研究材料
2. **大纲阶段**：
   - 规划论文结构
   - 为每个section规划相关的引用（仅限有URL的来源）
   - 确保引用与section内容的相关性
3. **论文写作阶段**：
   - 参考大纲中的`plannedCitations`
   - 优先使用规划的引用
   - 保持引用的连贯性和一致性

### 引用质量控制：

#### 大纲阶段：
- ✅ 只规划有具体URL的研究材料
- ✅ 避免规划思考分析或一般性概念
- ✅ 确保引用与section内容相关

#### 写作阶段：
- ✅ 参考大纲中的引用规划
- ✅ 严格验证URL来源
- ✅ 保持引用序号的连续性

## 📊 验证结果

通过 `test_outline_citation_planning.py` 测试脚本验证：

### 引用规划逻辑测试：
- ✅ 正确识别有URL的研究材料（3/3）
- ✅ 正确排除一般性思考分析
- ✅ 所有规划的引用都有有效URL

### 大纲结构测试：
- ✅ 大纲包含所有必要字段
- ✅ `plannedCitations`正确集成到sections中
- ✅ 引用结构完整（title, url, relevance）

### 引用连贯性测试：
- ✅ 引用与section内容相关
- ✅ 引用分布合理

## 🚀 使用效果

### 对于用户：
1. **引用规划**：在大纲阶段就能看到每个section计划使用的引用
2. **连贯性保证**：引用在整篇论文中保持逻辑连贯
3. **质量控制**：只有有URL来源的材料才会被规划为引用

### 对于开发者：
1. **结构化引用管理**：引用规划和使用分离，便于管理
2. **质量保证**：多层次的URL验证机制
3. **可追溯性**：从大纲到最终论文的引用路径清晰

## 📁 相关文件

- `src/prompts/outline_writer_simple.md` - 更新的大纲生成器
- `src/prompts/paper_writer_simple.md` - 更新的论文写作器
- `test_outline_citation_planning.py` - 引用规划功能测试
- `verify_prompt_changes.py` - 提示词验证脚本（已更新）
- `OUTLINE_CITATION_PLANNING_UPDATE.md` - 本文档

## 🔮 未来改进方向

1. **智能引用推荐**：基于section内容自动推荐最相关的引用
2. **引用重复检测**：避免在不同section中重复使用相同引用
3. **引用质量评分**：对引用来源的权威性和相关性进行评分
4. **引用格式标准化**：支持不同的学术引用格式（APA、MLA等）

## 📋 使用建议

### 对于用户：
1. 在大纲审核时，重点关注`plannedCitations`的相关性和质量
2. 确保规划的引用确实支持对应section的内容
3. 如需调整引用，可以在大纲阶段进行修改

### 对于开发者：
1. 监控大纲生成的引用规划质量
2. 观察论文写作阶段对规划引用的使用情况
3. 根据使用反馈进一步优化引用规划算法 