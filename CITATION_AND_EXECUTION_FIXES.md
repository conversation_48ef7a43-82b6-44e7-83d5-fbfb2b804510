# 引用序号和执行结果更新修复

本文档总结了对DeerFlow论文写作工作流程中两个关键问题的修复：

## 🎯 修复的问题

### 1. 引用序号重复问题
**问题描述**：paper_writing节点产出的引用序号有问题，正文中的引用很多都是同样的序号（比如都是2和都是4）

**根本原因**：
- LLM生成的引用序号可能不连续或重复
- 没有对引用序号进行验证和修正
- 多个section之间的引用序号没有正确衔接

### 2. execution_res更新问题
**问题描述**：thinking和research节点执行完成有结果后step.execution_res没有更新

**根本原因**：
- 虽然在内存中更新了`current_step.execution_res`，但没有将更新后的plan保存回state
- 导致后续节点无法访问到执行结果

## 🔧 修复方案

### 1. 引用序号修复

#### 修复位置：`src/graph/nodes.py` - `paper_writer_node`函数

#### 修复内容：

1. **引用序号验证和重新分配**：
```python
# Validate and fix citation numbers to ensure they are sequential
validated_citations = []
actual_next_number = current_citation_number
citation_mapping = {}  # Map old citation numbers to new ones

for i, citation in enumerate(section_citations):
    old_number = citation.get("number", i + 1)
    citation_mapping[old_number] = actual_next_number
    
    # Ensure citation number is correct and sequential
    citation["number"] = actual_next_number
    validated_citations.append(citation)
    actual_next_number += 1
```

2. **正文中引用序号修正**：
```python
# Fix citation numbers in the section content
fixed_content = section_content
for old_num, new_num in citation_mapping.items():
    # Replace citation references in the text
    import re
    fixed_content = re.sub(rf'\[{old_num}\]', f'[{new_num}]', fixed_content)
```

3. **使用修正后的内容**：
```python
# Add section content to paper sections (use fixed content with corrected citations)
updated_sections = paper_sections + [fixed_content]

# Update state with corrected citation number
"current_citation_number": actual_next_number,  # Use the validated next number
"messages": [AIMessage(content=fixed_content, name="paper_writer")],
```

### 2. execution_res更新修复

#### 修复位置：`src/graph/nodes.py` - `_execute_agent_step`函数

#### 修复内容：

**保存更新后的plan到state**：
```python
# Update the step with the execution result
current_step.execution_res = response_content
logger.info(f"Step '{current_step.title}' execution completed by {agent_name}")

# Preserve paper_writing_mode state during research execution
update_dict = {
    "messages": [
        HumanMessage(
            content=response_content,
            name=agent_name,
        )
    ],
    "observations": observations + [response_content],
    "current_plan": current_plan,  # Save the updated plan back to state
}
```

## 📊 修复验证

通过 `test_citation_and_execution_fixes.py` 测试脚本验证：

### 引用序号修复测试：
- ✅ 顺序引用（从1开始）正确处理
- ✅ 中间序号引用（从5开始）正确处理  
- ✅ 非顺序引用序号正确映射和修正

### execution_res更新测试：
- ✅ 正确找到未执行的步骤
- ✅ 内存中正确更新execution_res
- ✅ plan对象包含更新后的结果
- ✅ 正确找到下一个未执行步骤

### 状态保存测试：
- ✅ current_plan包含在更新字典中
- ✅ current_plan值正确
- ✅ paper_writing_mode正确保留
- ✅ observations正确更新

## 🔄 工作流程改进

### 修复前的问题：
1. **引用序号混乱**：多个section使用相同的引用序号，导致引用列表混乱
2. **执行结果丢失**：thinking和research节点的结果无法被后续节点访问
3. **状态不一致**：内存中的更新没有持久化到state中

### 修复后的改进：
1. **引用序号连续**：所有section的引用序号严格按顺序递增
2. **正文引用正确**：正文中的引用序号与引用列表完全对应
3. **执行结果持久化**：所有节点的执行结果都正确保存到state中
4. **状态一致性**：内存更新和state更新保持同步

## 🚀 使用效果

### 对于用户：
1. 论文中的引用序号现在是连续和正确的
2. 可以看到thinking和research节点的完整执行结果
3. 后续节点可以正确引用之前的研究成果

### 对于开发者：
1. 引用管理更加可靠和可预测
2. 状态管理更加一致
3. 调试和问题排查更加容易

## 📁 相关文件

- `src/graph/nodes.py` - 主要修复文件
- `test_citation_and_execution_fixes.py` - 验证测试脚本
- `CITATION_AND_EXECUTION_FIXES.md` - 本文档

## 🔮 未来改进方向

1. 考虑添加引用格式验证（APA、MLA等）
2. 增加引用重复检测和去重功能
3. 优化大型论文的引用管理性能
4. 添加引用质量评估机制 