# DeerFlow 环境配置文件示例
# 复制此文件为 .env 并填入实际配置值

# ===== 数据库配置 =====
# MySQL数据库连接信息
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=deerflow

# ===== Redis配置 =====
# Redis用于存储验证码
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# ===== JWT配置 =====
# JWT密钥（请使用强密码）
JWT_SECRET_KEY=your-very-secure-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# ===== 邮箱配置 =====
# SMTP服务器配置（用于发送验证码）
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# ===== Google OAuth配置 =====
# Google登录客户端ID
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com

# ===== RAGFlow配置 =====
# RAGFlow API配置
RAG_PROVIDER=ragflow
RAGFLOW_API_URL=http://localhost
RAGFLOW_API_KEY=your-ragflow-api-key
RAGFLOW_PAGE_SIZE=10

# ===== LangSmith配置 =====
# LangSmith API密钥（可选）
LANGCHAIN_API_KEY=your_langsmith_api_key
LANGCHAIN_TRACING_V2=true

# ===== TTS配置 =====
# 火山引擎TTS配置（可选）
VOLCENGINE_TTS_APPID=your_volcengine_appid
VOLCENGINE_TTS_ACCESS_TOKEN=your_volcengine_access_token
VOLCENGINE_TTS_CLUSTER=volcano_tts
VOLCENGINE_TTS_VOICE_TYPE=BV700_V2_streaming

# ===== 其他配置 =====
# 开发环境配置
DEBUG=false
LOG_LEVEL=INFO 