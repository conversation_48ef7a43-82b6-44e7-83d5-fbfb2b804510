#!/usr/bin/env python3

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.llms.llm import get_llm_by_type
from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration

def test_minimal():
    """Test with minimal state."""
    
    # Test with minimal state
    state = {
        'messages': [{'role': 'user', 'content': '写一篇AI论文'}],
        'user_query': '写一篇AI论文',
        'current_plan': '{}',
        'research_thinking': '',
        'research_results': '[]',
        'locale': 'zh-CN'
    }

    try:
        messages = apply_prompt_template('outline_writer', state, Configuration())
        print(f'Template OK: {len(messages)} messages')
        
        llm = get_llm_by_type('basic')
        response = llm.invoke(messages)
        print(f'LLM response length: {len(response.content)}')
        print(f'First 100 chars: {response.content[:100]}')
        
        if not response.content.strip():
            print("ERROR: Empty response!")
            return False
        
        return True
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal()
    sys.exit(0 if success else 1) 