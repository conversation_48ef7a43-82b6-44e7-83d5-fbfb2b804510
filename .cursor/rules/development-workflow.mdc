---
description:
globs:
alwaysApply: false
---
# Development Workflow Guide

## Quick Start Development

### Initial Setup
```bash
# Clone and setup the project
git clone https://github.com/bytedance/deer-flow.git
cd deer-flow

# Install Python dependencies with uv (recommended)
uv sync

# Setup configuration files
cp .env.example .env
cp conf.yaml.example conf.yaml

# Install frontend dependencies
cd web
pnpm install
cd ..

# Install additional tools
brew install marp-cli  # For PPT generation
```

### Development Servers

#### Backend Development
```bash
# Console UI (quickest way to test)
uv run main.py

# FastAPI development server
uv run server.py

# LangGraph CLI development
langgraph dev
```

#### Frontend Development
```bash
# Navigate to web directory
cd web

# Start Next.js development server
pnpm dev

# Alternative: with Turbo for faster builds
pnpm dev --turbo
```

#### Full Stack Development
```bash
# Run both backend and frontend (macOS/Linux)
./bootstrap.sh -d

# Run both backend and frontend (Windows)
bootstrap.bat -d
```

## Code Quality and Standards

### Python Code Quality
- **Formatter**: Black with 88-character line length
- **Configuration**: Defined in [pyproject.toml](mdc:pyproject.toml)
- **Target Version**: Python 3.12+

```bash
# Format Python code
black src/ tests/

# Check formatting
black --check src/ tests/
```

### Frontend Code Quality
- **Linting**: ESLint with Next.js configuration
- **Formatting**: Prettier with Tailwind CSS plugin
- **Type Checking**: TypeScript strict mode

```bash
# Navigate to web directory
cd web

# Lint and fix issues
pnpm lint
pnpm lint:fix

# Format code
pnpm format:write
pnpm format:check

# Type checking
pnpm typecheck
```

### Pre-commit Hooks
- **Setup**: [pre-commit](mdc:pre-commit) script for automated checks
- **Validation**: Code formatting, linting, and type checking
- **Integration**: Run before each commit

## Testing Strategy

### Backend Testing
- **Framework**: pytest with coverage reporting
- **Test Directory**: [tests/](mdc:tests/) - Unit and integration tests
- **Coverage Target**: Minimum 25% coverage
- **Configuration**: Defined in [pyproject.toml](mdc:pyproject.toml)

```bash
# Run tests with coverage
pytest

# Run specific test file
pytest tests/test_specific.py

# Generate coverage report
pytest --cov=src --cov-report=html
```

### Frontend Testing
- **Unit Tests**: Component and utility testing
- **Integration Tests**: API integration and workflow testing
- **Type Tests**: TypeScript compilation verification

```bash
cd web

# Run tests (when configured)
pnpm test

# Type checking as testing
pnpm typecheck
```

### Test Organization
- **Unit Tests**: Individual component/function testing
- **Integration Tests**: Multi-component workflow testing
- **E2E Tests**: Full application flow testing
- **API Tests**: Backend endpoint testing

## Git Workflow

### Branch Strategy
- **Main Branch**: `main` - Production-ready code
- **Feature Branches**: `feature/description` - New features
- **Bug Fixes**: `fix/description` - Bug fixes
- **Hotfixes**: `hotfix/description` - Critical production fixes

### Commit Guidelines
- **Conventional Commits**: Use conventional commit format
- **Clear Messages**: Descriptive commit messages
- **Atomic Commits**: Single logical change per commit
- **Pre-commit Checks**: Automated quality validation

### Pull Request Process
1. **Create Feature Branch**: From latest main
2. **Implement Changes**: Follow coding standards
3. **Run Tests**: Ensure all tests pass
4. **Update Documentation**: Update relevant docs
5. **Create PR**: With clear description and context
6. **Code Review**: Address feedback and suggestions
7. **Merge**: After approval and CI checks

## Dependency Management

### Python Dependencies
- **Tool**: uv for fast dependency resolution
- **Lock File**: [uv.lock](mdc:uv.lock) - Exact dependency versions
- **Configuration**: [pyproject.toml](mdc:pyproject.toml) - Project metadata and dependencies

```bash
# Add new dependency
uv add package-name

# Add development dependency
uv add --dev package-name

# Update dependencies
uv sync

# Install from lock file
uv sync --frozen
```

### Node.js Dependencies
- **Tool**: pnpm for efficient package management
- **Lock File**: [web/pnpm-lock.yaml](mdc:web/pnpm-lock.yaml) - Exact dependency versions
- **Configuration**: [web/package.json](mdc:web/package.json) - Project metadata and dependencies

```bash
cd web

# Add new dependency
pnpm add package-name

# Add development dependency
pnpm add -D package-name

# Update dependencies
pnpm update

# Install from lock file
pnpm install --frozen-lockfile
```

## Environment Management

### Development Environments
- **Local Development**: Full stack on local machine
- **Docker Development**: Containerized development environment
- **Cloud Development**: Remote development environments

### Configuration Management
- **Environment Files**: `.env` for secrets, `conf.yaml` for system config
- **Environment Validation**: Automatic validation of required variables
- **Multiple Environments**: Development, staging, production configurations

### Docker Development
```bash
# Build and run with Docker Compose
docker-compose up -d

# Build specific service
docker-compose build backend
docker-compose build frontend

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

## Deployment Process

### Build Process
```bash
# Backend build (if needed)
uv build

# Frontend build
cd web
pnpm build

# Verify build
pnpm start
```

### Docker Deployment
- **Backend Container**: [Dockerfile](mdc:Dockerfile) - Python backend
- **Frontend Container**: [web/Dockerfile](mdc:web/Dockerfile) - Next.js frontend
- **Orchestration**: [docker-compose.yml](mdc:docker-compose.yml) - Multi-service setup

### Production Deployment
1. **Environment Setup**: Configure production environment variables
2. **Build Images**: Create optimized Docker images
3. **Database Migration**: Run any necessary migrations
4. **Health Checks**: Verify service health
5. **Gradual Rollout**: Deploy with monitoring
6. **Monitoring**: Set up logging and monitoring

## Monitoring and Debugging

### Development Debugging
- **Python Debugging**: Use debugger with IDE integration
- **Frontend Debugging**: Browser dev tools and React dev tools
- **API Debugging**: FastAPI automatic documentation at `/docs`
- **Workflow Debugging**: LangGraph visualization and logging

### Logging Strategy
- **Structured Logging**: JSON format for production
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Context**: Include request IDs and user context
- **Sensitive Data**: Avoid logging secrets and PII

### Performance Monitoring
- **Backend Metrics**: API response times, error rates
- **Frontend Metrics**: Page load times, user interactions
- **Workflow Metrics**: LangGraph execution times and success rates
- **Resource Usage**: Memory, CPU, and storage utilization

## Maintenance Procedures

### Regular Maintenance
- **Dependency Updates**: Regular security and feature updates
- **Security Audits**: Regular security vulnerability scans
- **Performance Reviews**: Regular performance optimization
- **Documentation Updates**: Keep documentation current

### Security Maintenance
- **API Key Rotation**: Regular rotation of API keys
- **Dependency Scanning**: Automated vulnerability scanning
- **Security Headers**: Proper security headers configuration
- **Access Reviews**: Regular access permission reviews

### Backup and Recovery
- **Configuration Backup**: Backup of configuration files
- **Data Backup**: Regular backup of user data
- **Recovery Testing**: Regular recovery procedure testing
- **Disaster Recovery**: Documented disaster recovery procedures
