---
description:
globs:
alwaysApply: false
---
# DeerFlow Cursor Rules Index

This directory contains comprehensive Cursor Rules for the DeerFlow project, designed to help with efficient development, maintenance, and troubleshooting of this LangGraph + Next.js deep research framework.

## Available Rules

### 📋 [Project Overview](mdc:project-overview.mdc)
**Essential starting point** - Comprehensive overview of the DeerFlow architecture, technology stack, and project structure. Read this first to understand the overall system design.

**Key Topics:**
- Architecture overview (Backend + Frontend)
- Technology stack (LangGraph, Next.js, Python, TypeScript)
- Directory structure and organization
- Key configuration files and entry points

### 🐍 [Backend Development](mdc:backend-development.mdc)
**Python/LangGraph development guidelines** - Everything you need to know about developing the backend components, workflows, and AI agents.

**Key Topics:**
- LangGraph workflow development
- Agent and tool development
- FastAPI server development
- Configuration management
- Testing and code quality

### ⚛️ [Frontend Development](mdc:frontend-development.mdc)
**Next.js/React development guidelines** - Comprehensive guide for frontend development including UI components, state management, and performance optimization.

**Key Topics:**
- Next.js App Router architecture
- Component development with Radix UI
- TypeScript configuration and type safety
- Rich text editing with Tiptap
- Performance optimization

### ⚙️ [Configuration Guide](mdc:configuration-guide.mdc)
**Environment and system configuration** - Detailed guide for setting up and managing all configuration aspects of the project.

**Key Topics:**
- Environment variables and API keys
- LLM model configuration
- Search engine setup
- Docker configuration
- Security best practices

### 🔄 [Development Workflow](mdc:development-workflow.mdc)
**Development processes and procedures** - Complete workflow guide covering setup, development, testing, and deployment procedures.

**Key Topics:**
- Quick start development setup
- Code quality standards
- Testing strategies
- Git workflow and branching
- Deployment processes

### 🔧 [Troubleshooting Guide](mdc:troubleshooting-guide.mdc)
**Problem-solving and debugging** - Comprehensive troubleshooting guide for common issues and debugging strategies.

**Key Topics:**
- Common setup issues
- Runtime problem resolution
- API integration debugging
- Performance optimization
- Docker troubleshooting

## How to Use These Rules

### For New Developers
1. **Start with [Project Overview](mdc:project-overview.mdc)** to understand the system architecture
2. **Review [Configuration Guide](mdc:configuration-guide.mdc)** to set up your development environment
3. **Follow [Development Workflow](mdc:development-workflow.mdc)** for initial setup
4. **Refer to specific development guides** ([Backend](mdc:backend-development.mdc) or [Frontend](mdc:frontend-development.mdc)) based on your focus area

### For Maintenance and Updates
1. **Use [Development Workflow](mdc:development-workflow.mdc)** for standard development procedures
2. **Consult [Configuration Guide](mdc:configuration-guide.mdc)** when updating dependencies or environment settings
3. **Reference [Troubleshooting Guide](mdc:troubleshooting-guide.mdc)** when encountering issues

### For Debugging Issues
1. **Check [Troubleshooting Guide](mdc:troubleshooting-guide.mdc)** first for common solutions
2. **Review relevant development guide** for component-specific issues
3. **Verify configuration** using the [Configuration Guide](mdc:configuration-guide.mdc)

## Quick Reference

### Key Files and Directories
- **Backend Entry**: [main.py](mdc:main.py) (console) | [server.py](mdc:server.py) (web API)
- **Frontend Entry**: [web/src/app/](mdc:web/src/app/) (Next.js App Router)
- **Configuration**: [conf.yaml](mdc:conf.yaml) (system) | `.env` (secrets)
- **Dependencies**: [pyproject.toml](mdc:pyproject.toml) (Python) | [web/package.json](mdc:web/package.json) (Node.js)

### Development Commands
```bash
# Backend development
uv run main.py          # Console UI
uv run server.py        # API server

# Frontend development
cd web && pnpm dev      # Next.js dev server

# Full stack development
./bootstrap.sh -d       # Both backend and frontend
```

### Common Tasks
- **Add Python dependency**: `uv add package-name`
- **Add Node.js dependency**: `cd web && pnpm add package-name`
- **Run tests**: `pytest` (backend) | `cd web && pnpm test` (frontend)
- **Format code**: `black src/` (Python) | `cd web && pnpm format:write` (TypeScript)

## Project Context

DeerFlow is a **community-driven Deep Research framework** that combines:
- **LangGraph** for AI workflow orchestration
- **Next.js** for modern web interface
- **Multiple LLM providers** through litellm
- **Various search engines** (Tavily, Brave, DuckDuckGo, Arxiv)
- **MCP integration** for extended capabilities
- **Rich content generation** (reports, podcasts, presentations)

The project emphasizes **open source collaboration**, **modular architecture**, and **comprehensive research automation** with human-in-the-loop capabilities.

## Contributing

When contributing to the project:
1. **Follow the development workflow** outlined in [Development Workflow](mdc:development-workflow.mdc)
2. **Maintain code quality** standards from respective development guides
3. **Update documentation** and rules when making architectural changes
4. **Test thoroughly** using the testing strategies in the development guides

## Support

If these rules don't cover your specific issue:
1. **Check the main project documentation**: [README.md](mdc:README.md)
2. **Review examples**: [examples/](mdc:examples/) directory
3. **Consult community resources**: GitHub issues and discussions
4. **Follow troubleshooting procedures**: [Troubleshooting Guide](mdc:troubleshooting-guide.mdc)
