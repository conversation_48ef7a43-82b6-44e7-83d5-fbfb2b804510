---
description: 
globs: 
alwaysApply: false
---
# Backend Development Guidelines

## LangGraph Workflow Development

### Core Workflow Structure
- **Main Workflow**: [src/workflow.py](mdc:src/workflow.py) - Entry point for deep research workflow
- **Graph Builder**: [src/graph/builder.py](mdc:src/graph/builder.py) - Constructs the LangGraph workflow
- **Graph Nodes**: [src/graph/nodes.py](mdc:src/graph/nodes.py) - Individual workflow step implementations
- **Graph Types**: [src/graph/types.py](mdc:src/graph/types.py) - Type definitions for workflow state

### Specialized Workflows
- **Podcast Generation**: [src/podcast/graph/builder.py](mdc:src/podcast/graph/builder.py)
- **PPT Generation**: [src/ppt/graph/builder.py](mdc:src/ppt/graph/builder.py)
- **Paper Writing**: Integrated into main workflow with outline_writer and paper_writer nodes

### Agent Development
- **Agent Definitions**: [src/agents/agents.py](mdc:src/agents/agents.py) - AI agent configurations
- **Agent Initialization**: [src/agents/__init__.py](mdc:src/agents/__init__.py) - Agent exports

## Configuration Management

### System Configuration
- **Main Config**: [conf.yaml](mdc:conf.yaml) - LLM models, API keys, system settings
- **Example Config**: [conf.yaml.example](mdc:conf.yaml.example) - Template for configuration
- **Config Module**: [src/config/](mdc:src/config) - Configuration loading and validation

### Environment Variables
- **Environment File**: `.env` - API keys and secrets (not tracked in git)
- **Python Dotenv**: Used for loading environment variables

## Tool Development

### Research Tools
- **Tools Directory**: [src/tools/](mdc:src/tools) - Research and utility tools
- **Web Crawling**: [src/crawler/](mdc:src/crawler) - Web scraping and content extraction
- **LLM Integration**: [src/llms/](mdc:src/llms) - Language model wrappers and utilities

### Supported Search Engines
- Tavily (default) - Specialized AI search API
- DuckDuckGo - Privacy-focused search
- Brave Search - Advanced search features
- Arxiv - Academic paper search

## Server Development

### FastAPI Backend
- **Main Server**: [server.py](mdc:server.py) - FastAPI application setup
- **Server Components**: [src/server/](mdc:src/server) - API endpoints and middleware

### API Endpoints
- Research workflow execution
- Real-time streaming responses
- Configuration management
- File upload/download

## Development Workflow

### Environment Setup
```bash
# Install dependencies with uv
uv sync

# Activate virtual environment (if needed)
source .venv/bin/activate

# Run console application
uv run main.py

# Run development server
uv run server.py
```

### Code Quality
- **Formatting**: Black for Python code formatting
- **Configuration**: [pyproject.toml](mdc:pyproject.toml) - Black and other tool configurations
- **Line Length**: 88 characters (Black default)

### Testing
- **Test Framework**: pytest
- **Test Directory**: [tests/](mdc:tests) - Unit and integration tests
- **Coverage**: Minimum 25% coverage requirement
- **Test Configuration**: Defined in [pyproject.toml](mdc:pyproject.toml)

### Dependencies
- **Core**: LangGraph, LangChain, FastAPI, Uvicorn
- **AI/ML**: litellm, langchain-openai, langchain-community
- **Utilities**: httpx, pandas, numpy, python-dotenv
- **Development**: black, pytest, langgraph-cli

## Prompt Management

### Prompt Templates
- **Prompts Directory**: [src/prompts/](mdc:src/prompts) - AI prompt templates
- **Jinja2 Templates**: Used for dynamic prompt generation
- **Template Variables**: Research context, user queries, system instructions
- **Paper Writing Templates**: 
  - [src/prompts/outline_writer.md](mdc:src/prompts/outline_writer.md) - Academic outline generation
  - [src/prompts/paper_writer.md](mdc:src/prompts/paper_writer.md) - Academic paper content writing

## Utility Functions

### Common Utilities
- **Utils Directory**: [src/utils/](mdc:src/utils) - Shared utility functions
- **Content Processing**: Text extraction, formatting, validation
- **File Operations**: Reading, writing, temporary file management

## Docker Support

### Containerization
- **Dockerfile**: [Dockerfile](mdc:Dockerfile) - Python backend container
- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - Multi-service setup
- **Docker Ignore**: [.dockerignore](mdc:.dockerignore) - Files to exclude from build

## MCP Integration

### Model Context Protocol
- **MCP Support**: Integration with MCP services for extended capabilities
- **Dependencies**: `mcp` and `langchain-mcp-adapters` packages
- **Use Cases**: Private domain access, knowledge graphs, web browsing
