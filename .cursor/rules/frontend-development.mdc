---
description:
globs:
alwaysApply: false
---
# Frontend Development Guidelines

## Next.js Application Structure

### App Router Architecture
- **App Directory**: [web/src/app/](mdc:web/src/app/) - Next.js 15+ App Router pages and layouts
- **Root Layout**: [web/src/app/layout.tsx](mdc:web/src/app/layout.tsx) - Global layout and providers
- **Page Components**: [web/src/app/page.tsx](mdc:web/src/app/page.tsx) - Route-specific pages

### Component Architecture
- **Components**: [web/src/components/](mdc:web/src/components/) - Reusable React components
- **UI Components**: Radix UI primitives with custom styling
- **Component Organization**: Feature-based and shared components

### State Management
- **Zustand**: Primary state management solution
- **Local State**: React hooks for component-specific state
- **Server State**: React Query patterns for API data

## Development Environment

### Package Management
- **Package Manager**: pnpm (specified in [web/package.json](mdc:web/package.json))
- **Lock File**: [web/pnpm-lock.yaml](mdc:web/pnpm-lock.yaml) - Dependency lock file
- **Node Version**: 22+ (specified in project requirements)

### Development Scripts
```bash
# Development server with Turbo
pnpm dev

# Production build
pnpm build

# Type checking
pnpm typecheck

# Linting and formatting
pnpm lint
pnpm format:check
pnpm format:write
```

### Configuration Files
- **TypeScript**: [web/tsconfig.json](mdc:web/tsconfig.json) - TypeScript configuration
- **Next.js**: [web/next.config.js](mdc:web/next.config.js) - Next.js build configuration
- **ESLint**: [web/eslint.config.js](mdc:web/eslint.config.js) - Linting rules
- **Prettier**: [web/prettier.config.js](mdc:web/prettier.config.js) - Code formatting

## UI Framework and Styling

### Design System
- **Base Components**: Radix UI primitives
- **Styling**: Tailwind CSS with custom configuration
- **Component Variants**: class-variance-authority for component variants
- **Icons**: Lucide React and Radix UI icons

### Styling Configuration
- **Tailwind Config**: Integrated with PostCSS
- **PostCSS**: [web/postcss.config.js](mdc:web/postcss.config.js) - CSS processing
- **Global Styles**: [web/src/styles/](mdc:web/src/styles/) - Global CSS and Tailwind imports

### Theme System
- **Dark/Light Mode**: next-themes for theme switching
- **CSS Variables**: Custom properties for consistent theming
- **Component Theming**: Tailwind classes with theme-aware variants

## Rich Text Editing

### Editor Framework
- **Tiptap**: Rich text editor with React integration
- **Novel**: Enhanced Tiptap editor with additional features
- **Markdown Support**: tiptap-markdown for Markdown compatibility

### Editor Features
- **Table Support**: @tiptap/extension-table and related packages
- **Syntax Highlighting**: highlight.js and lowlight
- **Math Rendering**: KaTeX for mathematical expressions
- **Markdown**: react-markdown with remark and rehype plugins

## Core Libraries

### React Ecosystem
- **React**: Version 19.0.0
- **React DOM**: Version 19.0.0
- **React Hook Form**: Form handling with validation
- **Framer Motion**: Animation library

### Utility Libraries
- **clsx**: Conditional className utility
- **nanoid**: Unique ID generation
- **immer**: Immutable state updates
- **use-debounce**: Debounced values and callbacks
- **lru-cache**: Caching utility

### Data Visualization
- **React Flow**: [@xyflow/react](mdc:web/src/components/) - Node-based diagrams and workflows
- **Charts**: Integration ready for data visualization needs

## Type Safety

### TypeScript Configuration
- **Strict Mode**: Enabled for maximum type safety
- **Path Mapping**: Configured for clean imports
- **Type Definitions**: [web/src/typings/](mdc:web/src/typings/) - Custom type definitions

### Environment Variables
- **T3 Env**: [@t3-oss/env-nextjs](mdc:web/src/env.js) - Type-safe environment variables
- **Validation**: Zod schemas for runtime validation
- **Environment File**: [web/src/env.js](mdc:web/src/env.js) - Environment configuration

## Custom Hooks

### Hook Organization
- **Hooks Directory**: [web/src/hooks/](mdc:web/src/hooks/) - Custom React hooks
- **Reusable Logic**: Shared stateful logic across components
- **API Integration**: Hooks for backend communication

### Common Hook Patterns
- **Data Fetching**: Custom hooks for API calls
- **Local Storage**: Persistent state hooks
- **Form Handling**: Enhanced form management
- **UI State**: Modal, drawer, and overlay management

## Core Utilities

### Utility Libraries
- **Lib Directory**: [web/src/lib/](mdc:web/src/lib/) - Utility functions and configurations
- **Validation**: Zod schemas for data validation
- **API Clients**: HTTP client configurations
- **Helpers**: Common utility functions

### Core Logic
- **Core Directory**: [web/src/core/](mdc:web/src/core/) - Business logic and domain models
- **API Integration**: Backend communication logic
- **Data Transformation**: Response/request data mapping

## Development Workflow

### Code Quality
- **ESLint**: Comprehensive linting rules
- **Prettier**: Consistent code formatting
- **TypeScript**: Strict type checking
- **Pre-commit Hooks**: Automated quality checks

### Build Process
- **Next.js Build**: Optimized production builds
- **Static Analysis**: Type checking and linting
- **Bundle Analysis**: Performance optimization
- **Docker Support**: [web/Dockerfile](mdc:web/Dockerfile) for containerization

### Testing Strategy
- **Unit Tests**: Component and utility testing
- **Integration Tests**: API and workflow testing
- **Type Tests**: TypeScript compilation verification
- **E2E Tests**: Full application flow testing

## Performance Optimization

### Next.js Features
- **App Router**: Latest routing paradigm
- **Server Components**: Reduced client-side JavaScript
- **Image Optimization**: Next.js Image component
- **Bundle Splitting**: Automatic code splitting

### Optimization Techniques
- **Lazy Loading**: Dynamic imports for large components
- **Memoization**: React.memo and useMemo for expensive operations
- **Debouncing**: User input optimization
- **Caching**: LRU cache for computed values

## Deployment

### Build Configuration
- **Production Build**: `pnpm build` for optimized builds
- **Static Export**: Support for static site generation
- **Environment Variables**: Production environment configuration
- **Docker**: Containerized deployment support
