---
description:
globs:
alwaysApply: false
---
# Configuration Management Guide

## Environment Configuration

### Environment Files
- **Main Environment**: `.env` - API keys, secrets, and environment-specific settings
- **Example Template**: `.env.example` - Template showing required environment variables
- **Git Ignore**: Environment files are excluded from version control for security

### Required Environment Variables

#### Search Engine APIs
```bash
# Primary search API (choose one)
SEARCH_API=tavily  # Options: tavily, duckduckgo, brave_search, arxiv

# Tavily API (default, recommended)
TAVILY_API_KEY=your_tavily_api_key

# Brave Search API (alternative)
BRAVE_SEARCH_API_KEY=your_brave_search_api_key

# Other search engines (duckduckgo, arxiv) require no API keys
```

#### Text-to-Speech Integration
```bash
# Volcengine TTS (optional)
VOLCENGINE_ACCESS_KEY=your_volcengine_access_key
VOLCENGINE_SECRET_KEY=your_volcengine_secret_key
VOLCENGINE_APP_ID=your_volcengine_app_id
```

#### LLM API Keys
```bash
# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# Other LLM providers (as needed)
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key
```

## System Configuration

### Main Configuration File
- **Config File**: [conf.yaml](mdc:conf.yaml) - Primary system configuration
- **Example Config**: [conf.yaml.example](mdc:conf.yaml.example) - Configuration template
- **Config Module**: [src/config/](mdc:src/config/) - Configuration loading and validation

### LLM Model Configuration
```yaml
# Example LLM configuration structure
llm:
  model: "gpt-4"
  temperature: 0.7
  max_tokens: 4000
  
# Multiple model tiers for different tasks
models:
  fast: "gpt-3.5-turbo"
  standard: "gpt-4"
  advanced: "gpt-4-turbo"
```

### Research Configuration
```yaml
# Research workflow settings
research:
  max_search_results: 10
  crawl_timeout: 30
  max_content_length: 50000
  
# Search engine preferences
search:
  primary_engine: "tavily"
  fallback_engines: ["duckduckgo", "brave_search"]
```

## LangGraph Configuration

### Workflow Definitions
- **LangGraph Config**: [langgraph.json](mdc:langgraph.json) - Defines available workflows and their entry points
- **Python Version**: 3.12 (specified in configuration)
- **Environment**: Points to `.env` file for secrets

### Available Workflows
```json
{
  "graphs": {
    "deep_research": "./src/workflow.py:graph",
    "podcast_generation": "./src/podcast/graph/builder.py:workflow",
    "ppt_generation": "./src/ppt/graph/builder.py:workflow"
  }
}
```

### Workflow Configuration
- **State Management**: Type-safe state definitions in [src/graph/types.py](mdc:src/graph/types.py)
- **Node Configuration**: Individual step configurations in [src/graph/nodes.py](mdc:src/graph/nodes.py)
- **Graph Building**: Workflow construction in [src/graph/builder.py](mdc:src/graph/builder.py)

## Development Environment Setup

### Python Environment
```bash
# Install dependencies with uv (recommended)
uv sync

# Alternative: pip with virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -e .
```

### Node.js Environment
```bash
# Navigate to web directory
cd web

# Install dependencies with pnpm (recommended)
pnpm install

# Alternative: npm
npm install
```

### Development Tools
```bash
# Install marp for PPT generation
brew install marp-cli  # macOS
# or follow installation guide for other platforms
```

## Configuration Validation

### Environment Validation
- **Required Variables**: System validates presence of required API keys
- **Fallback Options**: Graceful degradation when optional services unavailable
- **Error Messages**: Clear feedback for missing or invalid configurations

### Configuration Loading
- **YAML Parsing**: Safe YAML loading with validation
- **Environment Override**: Environment variables can override YAML settings
- **Default Values**: Sensible defaults for optional configurations

## Security Best Practices

### API Key Management
- **Environment Variables**: Store sensitive data in `.env` files
- **Git Ignore**: Never commit API keys to version control
- **Key Rotation**: Regular rotation of API keys and secrets
- **Least Privilege**: Use API keys with minimal required permissions

### Configuration Security
- **Validation**: Input validation for all configuration values
- **Sanitization**: Clean and validate user-provided configuration
- **Encryption**: Consider encryption for highly sensitive configuration data

## Deployment Configuration

### Production Environment
```bash
# Production environment variables
NODE_ENV=production
PYTHON_ENV=production

# Production API endpoints
API_BASE_URL=https://api.yourapp.com
WEB_BASE_URL=https://yourapp.com
```

### Docker Configuration
- **Backend Dockerfile**: [Dockerfile](mdc:Dockerfile) - Python backend container
- **Frontend Dockerfile**: [web/Dockerfile](mdc:web/Dockerfile) - Next.js frontend container
- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - Multi-service orchestration

### Environment-Specific Configs
- **Development**: Local development with hot reloading
- **Staging**: Production-like environment for testing
- **Production**: Optimized for performance and security

## Configuration Troubleshooting

### Common Issues
1. **Missing API Keys**: Check `.env` file and required variables
2. **Invalid YAML**: Validate YAML syntax in `conf.yaml`
3. **Permission Errors**: Ensure proper file permissions for config files
4. **Path Issues**: Verify relative paths in configuration files

### Debugging Configuration
- **Verbose Logging**: Enable debug logging for configuration loading
- **Configuration Dump**: Print loaded configuration (excluding secrets)
- **Validation Errors**: Detailed error messages for invalid configurations

### Configuration Testing
- **Unit Tests**: Test configuration loading and validation
- **Integration Tests**: Test with various configuration combinations
- **Environment Tests**: Validate configuration in different environments
