---
description:
globs:
alwaysApply: false
---
# DeerFlow Project Overview

DeerFlow (**D**eep **E**xploration and **E**fficient **R**esearch **Flow**) is a community-driven Deep Research framework that combines LangGraph workflows with a Next.js web interface for comprehensive research automation.

## Architecture Overview

### Backend (Python + LangGraph)
- **Main Entry Point**: [main.py](mdc:main.py) - Console UI entry point
- **Server**: [server.py](mdc:server.py) - FastAPI backend server
- **Core Workflow**: [src/workflow.py](mdc:src/workflow.py) - Main LangGraph workflow definition
- **Configuration**: [conf.yaml](mdc:conf.yaml) - LLM and system configuration
- **Dependencies**: [pyproject.toml](mdc:pyproject.toml) - Python dependencies and project metadata

### Frontend (Next.js + TypeScript)
- **Web Application**: [web/](mdc:web/) - Complete Next.js application
- **Package Configuration**: [web/package.json](mdc:web/package.json) - Node.js dependencies and scripts
- **TypeScript Config**: [web/tsconfig.json](mdc:web/tsconfig.json) - TypeScript configuration

### LangGraph Configuration
- **Graph Definitions**: [langgraph.json](mdc:langgraph.json) - Defines available workflows
- **Available Graphs**:
  - `deep_research`: Main research workflow
  - `podcast_generation`: Audio content generation
  - `ppt_generation`: Presentation creation

## Key Directories

### Core Backend (`src/`)
- `agents/` - AI agent definitions and configurations
- `graph/` - LangGraph workflow nodes and builders
- `tools/` - Research tools and utilities
- `llms/` - Language model integrations
- `config/` - Configuration management
- `prompts/` - AI prompt templates
- `utils/` - Utility functions
- `server/` - FastAPI server components

### Specialized Features
- `podcast/` - Podcast generation workflows
- `ppt/` - PowerPoint generation workflows
- `prose/` - Text processing and generation
- `crawler/` - Web crawling capabilities

### Frontend (`web/src/`)
- `app/` - Next.js App Router pages and layouts
- `components/` - Reusable React components
- `hooks/` - Custom React hooks
- `lib/` - Utility libraries and configurations
- `styles/` - CSS and styling files
- `core/` - Core frontend logic
- `typings/` - TypeScript type definitions

## Technology Stack

### Backend
- **Framework**: LangGraph for workflow orchestration
- **API**: FastAPI for REST endpoints
- **Language**: Python 3.12+
- **Key Libraries**: langchain, litellm, httpx, pandas

### Frontend
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript
- **UI**: Radix UI components with Tailwind CSS
- **State Management**: Zustand
- **Rich Text**: Tiptap editor with Novel

### Development Tools
- **Python**: uv for dependency management
- **Node.js**: pnpm for package management
- **Code Quality**: ESLint, Prettier, Black
- **Testing**: pytest for Python, built-in Next.js testing

## Environment Setup
- **Python Environment**: Managed by uv with virtual environment
- **Configuration Files**: `.env` for secrets, `conf.yaml` for system config
- **Development Scripts**: [bootstrap.sh](mdc:bootstrap.sh) and [bootstrap.bat](mdc:bootstrap.bat)
