---
description:
globs:
alwaysApply: false
---
# Troubleshooting Guide

## Common Setup Issues

### Python Environment Issues

#### uv Installation Problems
```bash
# If uv is not installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# If uv sync fails
uv --version  # Check uv version
uv sync --verbose  # Verbose output for debugging
```

#### Python Version Issues
```bash
# Check Python version (requires 3.12+)
python --version

# If wrong version, install Python 3.12+
# macOS with Homebrew
brew install python@3.12

# Or use uv to manage Python versions
uv python install 3.12
```

#### Virtual Environment Issues
```bash
# If virtual environment is corrupted
rm -rf .venv
uv sync

# Manual virtual environment creation
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -e .
```

### Node.js Environment Issues

#### pnpm Installation Problems
```bash
# Install pnpm if not available
npm install -g pnpm

# Or use corepack (Node.js 16+)
corepack enable
corepack prepare pnpm@latest --activate
```

#### Node Version Issues
```bash
# Check Node version (requires 22+)
node --version

# Install correct version with nvm
nvm install 22
nvm use 22

# Or download from nodejs.org
```

#### Package Installation Issues
```bash
cd web

# Clear cache and reinstall
pnpm store prune
rm -rf node_modules pnpm-lock.yaml
pnpm install

# Alternative: use npm
npm install
```

## Configuration Issues

### Environment Variable Problems

#### Missing API Keys
```bash
# Check if .env file exists
ls -la .env

# Copy from example if missing
cp .env.example .env

# Edit with your API keys
nano .env  # or your preferred editor
```

#### Invalid Configuration Format
```bash
# Validate YAML syntax
python -c "import yaml; yaml.safe_load(open('conf.yaml'))"

# Check for common YAML issues
# - Incorrect indentation
# - Missing quotes for special characters
# - Invalid boolean values
```

#### Configuration Loading Errors
- **Check file permissions**: Ensure config files are readable
- **Validate paths**: Verify relative paths in configuration
- **Environment precedence**: Environment variables override YAML settings

### LangGraph Configuration Issues

#### Workflow Definition Errors
```bash
# Validate langgraph.json syntax
python -c "import json; json.load(open('langgraph.json'))"

# Check workflow entry points exist
ls -la src/workflow.py
ls -la src/podcast/graph/builder.py
ls -la src/ppt/graph/builder.py
```

#### Graph Building Failures
- **Import Errors**: Check Python import paths in workflow files
- **Missing Dependencies**: Ensure all required packages are installed
- **State Type Errors**: Verify type definitions in [src/graph/types.py](mdc:src/graph/types.py)

## Runtime Issues

### Backend Server Problems

#### FastAPI Server Won't Start
```bash
# Check if port is already in use
lsof -i :8000  # Default FastAPI port

# Kill existing process if needed
kill -9 <PID>

# Start with different port
uvicorn server:app --port 8001
```

#### Import Errors
```bash
# Check Python path
python -c "import sys; print(sys.path)"

# Install in development mode
pip install -e .

# Check specific import
python -c "from src.workflow import graph"
```

#### API Endpoint Errors
- **Check FastAPI docs**: Visit `http://localhost:8000/docs`
- **Verify request format**: Ensure correct JSON structure
- **Check logs**: Review server logs for detailed error messages

### Frontend Issues

#### Next.js Build Failures
```bash
cd web

# Clear Next.js cache
rm -rf .next

# Rebuild
pnpm build

# Check for TypeScript errors
pnpm typecheck
```

#### Development Server Issues
```bash
# Check if port 3000 is in use
lsof -i :3000

# Start on different port
pnpm dev -- --port 3001

# Clear cache and restart
rm -rf .next node_modules
pnpm install
pnpm dev
```

#### Component Rendering Errors
- **Check browser console**: Look for JavaScript errors
- **Verify imports**: Ensure all components are properly imported
- **Check TypeScript**: Run `pnpm typecheck` for type errors

## API Integration Issues

### Search Engine API Problems

#### Tavily API Issues
```bash
# Test API key
curl -X POST "https://api.tavily.com/search" \
  -H "Content-Type: application/json" \
  -d '{"api_key": "YOUR_API_KEY", "query": "test"}'
```

#### Brave Search API Issues
```bash
# Test API key
curl -H "X-Subscription-Token: YOUR_API_KEY" \
  "https://api.search.brave.com/res/v1/web/search?q=test"
```

#### Fallback Search Engines
- **DuckDuckGo**: No API key required, should work by default
- **Arxiv**: No API key required, for academic papers only

### LLM API Problems

#### OpenAI API Issues
```bash
# Test API key
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer YOUR_API_KEY"
```

#### Rate Limiting
- **Check rate limits**: Review API provider documentation
- **Implement backoff**: Use exponential backoff for retries
- **Monitor usage**: Track API usage to avoid limits

#### Model Availability
- **Check model names**: Verify correct model identifiers
- **Regional availability**: Some models may not be available in all regions
- **Permissions**: Ensure API key has access to requested models

## Performance Issues

### Slow Response Times

#### Backend Performance
```bash
# Profile Python code
python -m cProfile -o profile.stats main.py

# Monitor memory usage
python -m memory_profiler main.py

# Check database queries (if applicable)
# Enable SQL logging for database operations
```

#### Frontend Performance
- **Use React DevTools**: Profile component rendering
- **Check Network tab**: Monitor API request times
- **Lighthouse audit**: Run performance audits
- **Bundle analysis**: Analyze bundle size with Next.js

### Memory Issues

#### Python Memory Leaks
```bash
# Monitor memory usage
top -p $(pgrep -f python)

# Use memory profiler
pip install memory-profiler
python -m memory_profiler main.py
```

#### Node.js Memory Issues
```bash
# Monitor Node.js memory
node --inspect server.js

# Increase memory limit if needed
node --max-old-space-size=4096 server.js
```

## Docker Issues

### Container Build Problems

#### Docker Build Failures
```bash
# Build with verbose output
docker build --no-cache --progress=plain .

# Check Dockerfile syntax
docker build --dry-run .

# Build specific stage
docker build --target development .
```

#### Container Runtime Issues
```bash
# Check container logs
docker logs <container_id>

# Interactive debugging
docker run -it <image_name> /bin/bash

# Check resource usage
docker stats
```

### Docker Compose Issues

#### Service Communication Problems
```bash
# Check network connectivity
docker-compose exec backend ping frontend

# Verify service names in docker-compose.yml
docker-compose config

# Restart services
docker-compose restart
```

## Debugging Strategies

### Logging and Monitoring

#### Enable Debug Logging
```python
# In Python code
import logging
logging.basicConfig(level=logging.DEBUG)
```

```bash
# Environment variable
export LOG_LEVEL=DEBUG
```

#### Structured Logging
```python
# Use structured logging for better debugging
import structlog
logger = structlog.get_logger()
logger.info("Processing request", user_id=123, action="search")
```

### Development Tools

#### Python Debugging
```python
# Use debugger
import pdb; pdb.set_trace()

# Or with ipdb for better interface
import ipdb; ipdb.set_trace()
```

#### Frontend Debugging
- **React DevTools**: Install browser extension
- **Redux DevTools**: For state management debugging
- **Network tab**: Monitor API requests and responses

### Testing and Validation

#### Unit Testing for Debugging
```bash
# Run specific test
pytest tests/test_specific.py::test_function -v

# Run with debugging
pytest tests/test_specific.py --pdb

# Run with coverage
pytest --cov=src --cov-report=html
```

#### Integration Testing
```bash
# Test API endpoints
curl -X POST http://localhost:8000/api/research \
  -H "Content-Type: application/json" \
  -d '{"query": "test query"}'
```

## Getting Help

### Documentation Resources
- **Project README**: [README.md](mdc:README.md) - Main project documentation
- **Configuration Guide**: [docs/configuration_guide.md](mdc:docs/configuration_guide.md) - Detailed configuration
- **Examples**: [examples/](mdc:examples/) - Usage examples

### Community Support
- **GitHub Issues**: Report bugs and request features
- **Discussions**: Community discussions and Q&A
- **Contributing**: [CONTRIBUTING](mdc:CONTRIBUTING) - Contribution guidelines

### Professional Support
- **Code Review**: Request code review for complex issues
- **Architecture Guidance**: Consult on system architecture decisions
- **Performance Optimization**: Professional performance tuning services
