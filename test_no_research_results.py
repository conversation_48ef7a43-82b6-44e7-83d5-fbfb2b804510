#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_no_research_results():
    """Test outline writer with plan but no research results (matching the logs)."""
    
    # Create a plan that matches the logs exactly
    plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="人工智能在医疗诊断中应用的学术论文信息收集计划",
        thought="用户希望撰写一篇关于人工智能在医疗诊断中应用的学术论文，需要收集文献综述、技术分析和未来展望所需的信息。",
        steps=[
            Step(
                need_web_search=True,
                title="收集人工智能在医疗诊断应用的文献综述信息",
                description="收集关于人工智能在医疗诊断领域应用的现有学术文献、研究论文和综述。重点查找不同AI技术（如深度学习、机器学习）应用于各种疾病诊断的案例、研究成果、临床试验数据以及相关的挑战和局限性。包括但不限于影像诊断（如放射科、病理科）、疾病预测、辅助决策等方面。收集不同年份发表的重要文献以反映研究进展。",
                step_type=StepType.RESEARCH,
                execution_res=None  # No execution result
            ),
            Step(
                need_web_search=True,
                title="分析人工智能在医疗诊断中的技术细节和当前发展",
                description="收集关于在医疗诊断中使用的具体AI技术的信息。包括这些技术的原理、算法（如CNN, RNN, Transformer在医疗影像、文本分析中的应用）、数据处理方法（如数据标注、增强、隐私保护）、模型的性能评估指标（如准确率、召回率、特异性、AUROC）以及当前最先进的技术进展和热门研究方向。同时获取当前已应用于临床或处于应用阶段的AI诊断工具/平台的信息。",
                step_type=StepType.RESEARCH,
                execution_res=None  # No execution result
            ),
            Step(
                need_web_search=True,
                title="探讨人工智能在医疗诊断中的未来展望和影响",
                description="收集关于人工智能在医疗诊断领域未来发展趋势的信息。包括潜在的新技术、跨学科融合、监管和伦理方面的挑战与应对、对医疗体系和医患关系的影响、未来的研究方向和突破点。收集专家观点、行业报告和预测分析，形成对未来发展全面的视图。",
                step_type=StepType.RESEARCH,
                execution_res=None  # No execution result
            )
        ]
    )
    
    # Create state that matches the logs
    state = {
        "messages": [
            HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文，包括文献综述、技术分析和未来展望"),
            AIMessage(content='{\n    "locale": "zh-CN",\n    "has_enough_context": false,\n    "thought": "用户希望撰写一篇关于人工智能在医疗诊断中应用的学术论文，需要收集文献综述、技术分析和未来展望所需的信息。",\n    "title": "人工智能在医疗诊断中应用的学术论文信息收集计划",\n    "steps": [\n        {\n            "need_web_search": true,\n            "title": "收集人工智能在医疗诊断应用的文献综述信息",\n            "description": "收集关于人工智能在医疗诊断领域应用的现有学术文献、研究论文和综述。重点查找不同AI技术（如深度学习、机器学习）应用于各种疾病诊断的案例、研究成果、临床试验数据以及相关的挑战和局限性。包括但不限于影像诊断（如放射科、病理科）、疾病预测、辅助决策等方面。收集不同年份发表的重要文献以反映研究进展。",\n            "step_type": "research"\n        },\n        {\n            "need_web_search": true,\n            "title": "分析人工智能在医疗诊断中的技术细节和当前发展",\n            "description": "收集关于在医疗诊断中使用的具体AI技术的信息。包括这些技术的原理、算法（如CNN, RNN, Transformer在医疗影像、文本分析中的应用）、数据处理方法（如数据标注、增强、隐私保护）、模型的性能评估指标（如准确率、召回率、特异性、AUROC）以及当前最先进的技术进展和热门研究方向。同时获取当前已应用于临床或处于应用阶段的AI诊断工具/平台的信息。",\n            "step_type": "research"\n        },\n        {\n            "need_web_search": true,\n            "title": "探讨人工智能在医疗诊断中的未来展望和影响",\n            "description": "收集关于人工智能在医疗诊断领域未来发展趋势的信息。包括潜在的新技术、跨学科融合、监管和伦理方面的挑战与应对、对医疗体系和医患关系的影响、未来的研究方向和突破点。收集专家观点、行业报告和预测分析，形成对未来发展全面的视图。",\n            "step_type": "research"\n        }\n    ]\n}', name="planner")
        ],
        "locale": "zh-CN",
        "observations": [],
        "plan_iterations": 1,
        "current_plan": plan,
        "final_report": "",
        "auto_accepted_plan": False,
        "enable_background_investigation": True
    }
    
    try:
        # Same logic as outline_writer_node
        current_plan = state.get("current_plan")
        research_results = ""
        research_thinking = ""
        
        # Collect research results from completed steps
        if current_plan and current_plan.steps:
            research_results_list = []
            for step in current_plan.steps:
                if step.execution_res:  # This will be False for None
                    research_results_list.append({
                        "title": step.title,
                        "description": step.description,
                        "result": step.execution_res
                    })
            research_results = json.dumps(research_results_list, ensure_ascii=False)
            print(f"Found {len(research_results_list)} research results")
            print(f"Research results JSON length: {len(research_results)}")
        
        # Get user query
        user_query = ""
        if state["messages"]:
            for msg in state["messages"]:
                if hasattr(msg, 'content'):
                    user_query = msg.content
                    break
        
        print(f"User query: {user_query}")
        
        # Apply template
        template_state = {
            **state,
            "user_query": user_query,
            "current_plan": json.dumps(current_plan.model_dump() if current_plan else {}, ensure_ascii=False),
            "research_thinking": research_thinking,
            "research_results": research_results,
            "locale": state.get("locale", "en-US")
        }
        
        messages = apply_prompt_template("outline_writer", template_state, Configuration())
        
        print(f"Generated {len(messages)} messages")
        print(f"System prompt length: {len(messages[0]['content'])}")
        
        # Test with LLM using same configuration as the node
        if AGENT_LLM_MAP.get("outline_writer", "basic") == "basic":
            llm = get_llm_by_type("basic")
        else:
            llm = get_llm_by_type(AGENT_LLM_MAP.get("outline_writer", "basic"))
        
        # Configure LLM with same parameters
        llm = llm.bind(
            max_tokens=4000,
            temperature=0.7,
        )
        
        print("Testing LLM with exact scenario from logs...")
        
        # Test streaming (same as the node)
        full_response = ""
        chunk_count = 0
        try:
            response = llm.stream(messages)
            for chunk in response:
                chunk_count += 1
                chunk_content = chunk.content
                full_response += chunk_content
                print(f"Chunk {chunk_count}: '{chunk_content}' (length: {len(chunk_content)})")
                
                # Stop after a few chunks to see what's happening
                if chunk_count >= 10:
                    print("Stopping after 10 chunks for analysis...")
                    break
        except Exception as e:
            print(f"Streaming failed: {e}")
            
        print(f"Total response length: {len(full_response)}")
        print(f"Response content: '{full_response}'")
        
        if not full_response.strip():
            print("ERROR: Empty response!")
            return False
        elif full_response.strip() == "```":
            print("ERROR: Response is just opening code block!")
            return False
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_no_research_results()
    sys.exit(0 if success else 1) 