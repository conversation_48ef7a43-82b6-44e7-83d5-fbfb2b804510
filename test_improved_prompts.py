#!/usr/bin/env python3
"""
Test script for improved prompts:
1. Outline writer without references
2. Paper writer with strict URL citation requirements  
3. Enhanced thinking agent with deep analysis
"""

import json
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type

def test_outline_writer():
    """Test the improved outline writer that doesn't generate references."""
    print("🔍 Testing Outline Writer (No References)...")
    
    template_state = {
        "user_query": "Write a comprehensive paper on the impact of artificial intelligence on healthcare",
        "research_results": json.dumps([
            {
                "title": "AI in Medical Diagnosis",
                "description": "Research on AI diagnostic tools",
                "result": "AI diagnostic tools have shown 95% accuracy in detecting certain cancers. Source: https://example.com/ai-diagnosis"
            },
            {
                "title": "Healthcare Automation",
                "description": "Analysis of automated healthcare systems",
                "result": "Automated systems reduce administrative costs by 30%. Source: https://example.com/healthcare-automation"
            }
        ], ensure_ascii=False),
        "locale": "zh-CN",
        "messages": []
    }
    
    try:
        messages = apply_prompt_template("outline_writer_simple", template_state, Configuration())
        print("✅ Outline writer template applied successfully")
        print(f"📝 Template contains {len(messages)} messages")
        
        # Check if the template mentions not to include references
        template_content = str(messages)
        if "DO NOT generate references" in template_content:
            print("✅ Template correctly instructs to avoid references")
        else:
            print("❌ Template may not properly instruct to avoid references")
            
        return True
    except Exception as e:
        print(f"❌ Outline writer template failed: {e}")
        return False

def test_paper_writer():
    """Test the improved paper writer with strict citation requirements."""
    print("\n🔍 Testing Paper Writer (Strict Citations)...")
    
    template_state = {
        "user_query": "Write a paper on AI in healthcare",
        "research_results": json.dumps([
            {
                "title": "AI Diagnostic Study",
                "description": "Research with verifiable source",
                "result": "AI shows 95% accuracy. Source: https://nature.com/ai-study"
            },
            {
                "title": "General Analysis",
                "description": "Thinking stage analysis",
                "result": "AI will transform healthcare through various mechanisms (no specific source)"
            }
        ], ensure_ascii=False),
        "approved_outline": json.dumps({
            "title": "AI in Healthcare",
            "sections": [
                {
                    "sectionTitle": "Introduction",
                    "sectionSummary": "Overview of AI in healthcare",
                    "subsections": [
                        {
                            "subheadingTitle": "Current State",
                            "content": "Current applications of AI"
                        }
                    ]
                }
            ]
        }),
        "current_section": json.dumps({
            "sectionTitle": "Introduction",
            "sectionSummary": "Overview of AI in healthcare"
        }),
        "current_citation_number": 1,
        "locale": "zh-CN",
        "messages": []
    }
    
    try:
        messages = apply_prompt_template("paper_writer_simple", template_state, Configuration())
        print("✅ Paper writer template applied successfully")
        print(f"📝 Template contains {len(messages)} messages")
        
        # Check if the template includes strict citation guidelines
        template_content = str(messages)
        if "ONLY cite research materials that have verifiable URLs" in template_content:
            print("✅ Template correctly enforces strict citation requirements")
        else:
            print("❌ Template may not properly enforce citation requirements")
            
        if "DO NOT Cite" in template_content:
            print("✅ Template includes clear guidelines on what not to cite")
        else:
            print("❌ Template may be missing 'do not cite' guidelines")
            
        return True
    except Exception as e:
        print(f"❌ Paper writer template failed: {e}")
        return False

def test_thinking_agent():
    """Test the enhanced thinking agent with deep analysis framework."""
    print("\n🔍 Testing Enhanced Thinking Agent...")
    
    template_state = {
        "user_query": "Analyze the impact of AI on healthcare",
        "research_results": json.dumps([
            {
                "title": "AI Medical Applications",
                "description": "Overview of AI in medicine",
                "result": "AI is being used in diagnosis, treatment planning, and drug discovery"
            }
        ], ensure_ascii=False),
        "locale": "zh-CN",
        "messages": []
    }
    
    try:
        messages = apply_prompt_template("thinking", template_state, Configuration())
        print("✅ Thinking agent template applied successfully")
        print(f"📝 Template contains {len(messages)} messages")
        
        # Check if the template includes deep thinking strategies
        template_content = str(messages)
        if "Deep Deconstruction Analysis" in template_content:
            print("✅ Template includes deep analysis strategies")
        else:
            print("❌ Template may be missing deep analysis strategies")
            
        if "Multi-role Perspective Switching" in template_content:
            print("✅ Template includes multi-perspective analysis")
        else:
            print("❌ Template may be missing multi-perspective analysis")
            
        if "Cross-disciplinary Integration" in template_content:
            print("✅ Template includes cross-disciplinary thinking")
        else:
            print("❌ Template may be missing cross-disciplinary thinking")
            
        return True
    except Exception as e:
        print(f"❌ Thinking agent template failed: {e}")
        return False

def test_llm_integration():
    """Test if the templates work with actual LLM."""
    print("\n🔍 Testing LLM Integration...")
    
    try:
        llm = get_llm_by_type("basic")
        print("✅ LLM initialized successfully")
        
        # Test with a simple thinking prompt
        test_messages = [
            {
                "role": "user", 
                "content": "请进行深度思考：人工智能对医疗行业的影响"
            }
        ]
        
        response = llm.invoke(test_messages)
        if response and response.content:
            print("✅ LLM responds correctly")
            print(f"📝 Response length: {len(response.content)} characters")
            return True
        else:
            print("❌ LLM response is empty")
            return False
            
    except Exception as e:
        print(f"❌ LLM integration failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Improved Prompts for Paper Writing Workflow\n")
    
    results = []
    
    # Test individual components
    results.append(test_outline_writer())
    results.append(test_paper_writer())
    results.append(test_thinking_agent())
    results.append(test_llm_integration())
    
    # Summary
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All tests passed! The improved prompts are ready for use.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 