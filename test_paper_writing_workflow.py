#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.graph.nodes import research_team_node
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage, AIMessage

def test_paper_writing_workflow():
    """Test that research_team_node correctly routes to outline_writer in paper writing mode."""
    
    # Create a plan with completed research steps
    plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="基于AI的写作工具分析研究计划",
        thought="分析基于AI的写作工具，需要对其历史发展、当前市场状况、主要玩家及其产品、技术原理、优缺点、对用户和行业的影响以及未来发展趋势进行全面的信息收集。",
        steps=[
            Step(
                need_web_search=True,
                title="AI写作工具历史与市场概况研究",
                description="收集AI写作工具的发展历史、关键技术里程碑、全球及主要区域市场规模、增长趋势、驱动因素和主要垂直应用领域的数据和信息。",
                step_type=StepType.RESEARCH,
                execution_res="研究结果：AI写作工具市场在过去5年中快速发展，主要驱动因素包括自然语言处理技术的突破、GPT等大语言模型的出现，以及内容创作需求的增长。市场规模预计将在2025年达到XX亿美元。"
            ),
            Step(
                need_web_search=True,
                title="主流AI写作工具技术、功能与评价研究",
                description="研究当前市场上主流的AI写作工具，深入了解其底层AI技术、核心功能、定价模式、用户评价、优缺点及应用案例。",
                step_type=StepType.RESEARCH,
                execution_res="研究结果：主流工具包括Jasper、Copy.ai、Grammarly等，主要基于GPT系列模型，提供文本生成、润色、校对等功能。用户评价普遍积极，但也存在原创性和准确性方面的担忧。"
            ),
            Step(
                need_web_search=True,
                title="AI写作工具对行业、用户及伦理影响研究",
                description="收集AI写作工具对内容创作行业、个人用户的影响，探讨其带来的效率提升、创意启发等积极影响，以及可能引发的伦理和社会问题。",
                step_type=StepType.RESEARCH,
                execution_res="研究结果：AI写作工具显著提高了内容创作效率，但也引发了关于原创性、版权、就业影响等伦理问题。需要建立相应的监管框架和使用准则。"
            )
        ]
    )
    
    # Test 1: Normal mode (should go to planner)
    print("Test 1: Normal mode (paper_writing_mode=False)")
    state_normal = {
        "messages": [
            HumanMessage(content="基于AI的写作工具分析"),
        ],
        "locale": "zh-CN",
        "current_plan": plan,
        "paper_writing_mode": False,  # Normal mode
        "observations": ["研究观察1", "研究观察2", "研究观察3"]
    }
    
    result_normal = research_team_node(state_normal)
    print(f"Normal mode result: {result_normal}")
    print(f"Expected: goto='planner', Actual: goto='{result_normal.goto}'")
    assert result_normal.goto == "planner", f"Expected 'planner', got '{result_normal.goto}'"
    print("✅ Normal mode test passed\n")
    
    # Test 2: Paper writing mode (should go to outline_writer)
    print("Test 2: Paper writing mode (paper_writing_mode=True)")
    state_paper = {
        "messages": [
            HumanMessage(content="基于AI的写作工具分析"),
        ],
        "locale": "zh-CN",
        "current_plan": plan,
        "paper_writing_mode": True,  # Paper writing mode
        "observations": ["研究观察1", "研究观察2", "研究观察3"]
    }
    
    result_paper = research_team_node(state_paper)
    print(f"Paper writing mode result: {result_paper}")
    print(f"Expected: goto='outline_writer', Actual: goto='{result_paper.goto}'")
    assert result_paper.goto == "outline_writer", f"Expected 'outline_writer', got '{result_paper.goto}'"
    print("✅ Paper writing mode test passed\n")
    
    # Test 3: Incomplete research (should continue research)
    print("Test 3: Incomplete research in paper writing mode")
    incomplete_plan = Plan(
        locale="zh-CN",
        has_enough_context=False,
        title="基于AI的写作工具分析研究计划",
        thought="分析基于AI的写作工具",
        steps=[
            Step(
                need_web_search=True,
                title="AI写作工具历史与市场概况研究",
                description="收集AI写作工具的发展历史",
                step_type=StepType.RESEARCH,
                execution_res="研究结果：已完成的研究"
            ),
            Step(
                need_web_search=True,
                title="主流AI写作工具技术、功能与评价研究",
                description="研究当前市场上主流的AI写作工具",
                step_type=StepType.RESEARCH,
                execution_res=None  # 未完成的研究
            )
        ]
    )
    
    state_incomplete = {
        "messages": [
            HumanMessage(content="基于AI的写作工具分析"),
        ],
        "locale": "zh-CN",
        "current_plan": incomplete_plan,
        "paper_writing_mode": True,
        "observations": ["研究观察1"]
    }
    
    result_incomplete = research_team_node(state_incomplete)
    print(f"Incomplete research result: {result_incomplete}")
    print(f"Expected: goto='researcher', Actual: goto='{result_incomplete.goto}'")
    assert result_incomplete.goto == "researcher", f"Expected 'researcher', got '{result_incomplete.goto}'"
    print("✅ Incomplete research test passed\n")
    
    print("All tests passed! 🎉")
    return True

if __name__ == "__main__":
    try:
        success = test_paper_writing_workflow()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 