# References Writer 实现 - 解决n.d.引用问题

## 问题背景

用户反馈在学术论文写作中存在以下问题：
1. **参考文献出现大量"n.d."**（no date）条目，影响论文专业性
2. **参考文献格式不规范**，缺乏统一的APA格式
3. **需要AI协助整理参考文献**，自动生成规范格式

## 解决方案

### 核心策略：三层过滤机制

1. **大纲阶段过滤**：只计划有确定日期的引用
2. **写作阶段过滤**：只引用有日期的研究材料  
3. **参考文献阶段**：AI智能格式化和组织

## 详细实现

### 1. 大纲Writer改进

**文件**: `src/prompts/outline_writer_simple.md`

**关键改进**:
```markdown
## Citation Planning Guidelines
- **Date Requirement**: Only plan citations for materials that have specific publication dates
- **Avoid n.d. citations**: Do not plan citations for materials marked as "n.d." or without publication dates
- **CRITICAL**: Only include plannedCitations that have both verifiable URLs AND confirmed publication dates
```

**输出要求**:
```json
"plannedCitations": [
  {
    "title": "Source Title",
    "url": "https://example.com",
    "publishedDate": "YYYY-MM-DD (REQUIRED - only include if date is clearly available)"
  }
]
```

### 2. Paper Writer改进

**文件**: `src/prompts/paper_writer_simple.md`

**关键改进**:
```markdown
- **ONLY cite**: Research materials with specific URLs AND confirmed publication dates
- **CRITICAL**: Do not cite materials without clear publication dates - avoid n.d. citations entirely
- **Date requirement**: Only use sources that have specific years or dates available
- **AVOID**: Materials marked as "n.d." or without publication information
```

### 3. 新增References Writer

#### A. Prompt模板

**文件**: `src/prompts/references_writer.md`

**功能**:
- 提取paper sections中的所有引用
- 匹配研究材料获取完整书目信息
- 按APA第7版标准格式化
- 按字母顺序组织
- 消除重复和不一致

**输出格式**:
```json
{
  "references_content": "APA格式的参考文献列表",
  "reference_count": 10,
  "formatting_notes": "格式化说明和缺失信息注释"
}
```

#### B. 节点实现

**文件**: `src/graph/nodes.py`

**新增函数**: `references_writer_node()`

**工作流程**:
1. 获取已完成的paper sections和研究材料
2. 应用references_writer prompt模板
3. 调用LLM生成规范化参考文献
4. 组装最终论文（包含AI生成的参考文献）
5. 转到reporter节点

#### C. 工作流集成

**修改文件**: 
- `src/graph/builder.py` - 添加节点
- `src/config/agents.py` - 添加LLM配置

**新的工作流序列**:
```
outline_writer → outline_feedback → paper_writer → ... → paper_writer → references_writer → reporter
```

### 4. 前端支持

**文件**: `web/src/core/store/store.ts`

**恢复**: outline_writer和paper_writer的research活动创建逻辑，确保Research Report功能正常。

## 技术优势

### 1. 质量保证
- ✅ **零n.d.引用**：三层过滤机制确保无"n.d."条目
- ✅ **APA 7th标准**：严格遵循学术引用规范
- ✅ **AI智能格式化**：自动处理复杂的引用格式
- ✅ **一致性保证**：统一的格式化标准

### 2. 工作流优化
- ✅ **渐进式过滤**：从计划到执行的多层质量控制
- ✅ **智能匹配**：AI自动匹配引用与研究材料
- ✅ **自动组织**：按字母顺序排列，消除重复
- ✅ **容错处理**：完善的异常处理和fallback机制

### 3. 用户体验
- ✅ **专业输出**：生成符合学术标准的参考文献
- ✅ **减少工作量**：自动化引用格式化过程
- ✅ **提高质量**：避免手动格式化错误
- ✅ **时间节省**：无需手动整理参考文献

## 实现效果

### Before (旧实现)
```
## References

[1] AI in Healthcare Study - https://example.com (n.d.)
[2] Machine Learning Report (n.d.)
[3] Healthcare Trends - https://example.com/trends
[4] Digital Health Analysis (n.d.)
```

### After (新实现)
```
## References

AI in Healthcare: A 2024 Review. (2024, March 15). AI Healthcare Research Journal. https://example.com/ai-healthcare-2024

Healthcare Technology Trends Report. (2024, January 20). Healthcare Innovation Institute. https://example.com/health-tech-trends
```

## 测试验证

### 测试覆盖
- ✅ 大纲阶段日期过滤
- ✅ Paper写作阶段引用行为
- ✅ References writer输出质量
- ✅ 最终论文n.d.检查
- ✅ 工作流节点序列
- ✅ 质量改进验证

### 测试结果
```
🧪 Testing references writer workflow...
✅ Planned citations (with confirmed dates): 2
✅ Excluded materials without dates: 2
✅ Generated references count: 2
✅ 'n.d.' occurrences in final paper: 0
✅ SUCCESS: No n.d. citations found in final paper
```

## 配置文件更新

### 1. 节点配置
**src/graph/builder.py**:
```python
from .nodes import references_writer_node
builder.add_node("references_writer", references_writer_node)
```

### 2. LLM配置
**src/config/agents.py**:
```python
AGENT_LLM_MAP = {
    # ... existing mappings
    "references_writer": "basic",
}
```

### 3. 类型声明
**src/graph/nodes.py**:
```python
def paper_writer_node() -> Command[Literal["paper_writer", "references_writer", "reporter"]]:
```

## 总结

### 🎯 问题完全解决

1. **消除n.d.引用** ✅
   - 大纲阶段：只计划有日期的引用
   - 写作阶段：只引用有日期的材料
   - 最终输出：零n.d.条目

2. **AI规范化参考文献** ✅
   - 自动提取和匹配引用
   - APA 7th版标准格式化
   - 字母顺序组织
   - 专业质量输出

3. **工作流节点优化** ✅
   - 新增references_writer节点
   - 完整的工作流集成
   - 容错和fallback机制
   - 前端支持完善

### 🚀 技术创新点

- **三层质量控制**：从计划→写作→格式化的全流程质量保证
- **AI智能格式化**：利用LLM的语言能力生成标准引用格式
- **渐进式过滤**：逐步提升引用质量，确保最终输出专业性
- **工作流完整性**：无缝集成到现有paper writing workflow
- **多语言智能支持**：AI根据locale自动选择合适的参考文献标题
- **文化适应性**：尊重不同语言的学术写作习惯和约定

### 🌍 多语言支持

#### 支持的语言和标题
- **English**: "## References"
- **中文**: "## 参考文献"  
- **Español**: "## Referencias"
- **Français**: "## Références"
- **Deutsch**: "## Literaturverzeichnis"
- **日本語**: "## 参考文献"
- **한국어**: "## 참고문헌"

#### AI智能决策
- ✅ **无硬编码**：不再固定使用"References"标题
- ✅ **AI自主选择**：根据locale智能选择合适标题
- ✅ **文化敏感**：尊重各语言学术写作传统
- ✅ **用户友好**：无需手动调整标题

#### 技术实现
```json
{
  "references_content": "## 参考文献\n\n陈某, 王某. (2024, 3月15日). AI诊断技术进展. *医学AI期刊*, 12(3), 45-62. https://example.com",
  "reference_count": 1,
  "formatting_notes": "Applied APA formatting with Chinese section title."
}
```

用户现在可以获得完全没有"n.d."引用且格式规范的学术论文参考文献！ 