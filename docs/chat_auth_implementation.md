# 聊天认证和历史记录实现指南

## 概述

本文档描述了为 `/api/chat/stream` 接口添加认证和对话历史记录功能的实现。

## 实现的功能

### 1. 聊天接口认证
- `/api/chat/stream` 接口现在需要JWT认证
- 用户必须先登录获取token才能使用聊天功能

### 2. 对话历史记录
- 自动记录每个对话的thread_id和标题
- 标题从请求中第一条消息的前30个字符提取
- 如果thread_id已存在，不会重复创建记录

### 3. 对话线程列表接口
- 新增 `/api/auth/chat-threads` 接口
- 返回用户的所有对话线程和标题
- 支持分页查询

## 数据库修改

### ChatHistory表新增字段
```sql
ALTER TABLE chat_histories ADD COLUMN title VARCHAR(100) NOT NULL DEFAULT '' COMMENT '对话标题';
```

## 新增的API接口

### 获取对话线程列表
```http
GET /api/auth/chat-threads?limit=50&offset=0
Authorization: Bearer <token>
```

**响应示例：**
```json
{
  "threads": [
    {
      "thread_id": "uuid-string",
      "title": "你好，请介绍一下量子计算的基本原理和应用前景",
      "created_at": "2025-01-04T16:00:00",
      "updated_at": "2025-01-04T16:05:00"
    }
  ],
  "total": 1
}
```

## 修改的API接口

### 聊天流接口（新增认证要求）
```http
POST /api/chat/stream
Authorization: Bearer <token>
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下量子计算的基本原理和应用前景"
    }
  ],
  "thread_id": "__default__",
  "resources": [],
  "max_plan_iterations": 3,
  "max_step_num": 20,
  "max_search_results": 10,
  "auto_accepted_plan": true,
  "interrupt_feedback": "",
  "mcp_settings": {},
  "enable_background_investigation": false
}
```

## 工作流程

### 1. 用户认证
1. 用户发送验证码到邮箱：`POST /api/auth/send-verification`
2. 用户使用验证码登录：`POST /api/auth/login/email`
3. 获得JWT token

### 2. 开始对话
1. 用户调用 `/api/chat/stream` 接口，携带JWT token
2. 系统验证token有效性
3. 从第一条消息提取标题（前30个字符）
4. 创建或获取对话历史记录
5. 返回聊天流响应

### 3. 查看历史记录
1. 用户调用 `/api/auth/chat-threads` 获取对话列表
2. 系统返回用户的所有对话线程

## 技术实现细节

### 认证中间件
- 使用 `get_current_user` 依赖注入来验证JWT token
- 在聊天接口中添加用户认证要求

### 数据库操作
- `AuthService.create_or_get_chat_history()` 方法处理对话记录的创建
- 如果thread_id已存在，直接返回现有记录
- 否则创建新的对话记录

### 标题提取逻辑
```python
title = ""
if request.messages and len(request.messages) > 0:
    first_message_content = request.messages[0].content
    title = first_message_content[:30] if len(first_message_content) > 30 else first_message_content
```

## 数据库迁移

### 运行迁移
```bash
# 如果配置了数据库连接
alembic upgrade head

# 或者手动执行SQL
ALTER TABLE chat_histories ADD COLUMN title VARCHAR(100) NOT NULL DEFAULT '' COMMENT '对话标题';
```

## 测试

### 运行测试脚本
```bash
python test_chat_auth.py
```

测试脚本会验证：
1. 验证码发送功能
2. 邮箱登录功能
3. 带认证的聊天接口
4. 对话线程列表获取

## 环境配置

确保以下环境变量已配置：
- `DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME` - 数据库连接
- `REDIS_HOST`, `REDIS_PORT` - Redis连接（用于验证码存储）
- `SMTP_*` - 邮件服务器配置
- `JWT_SECRET_KEY` - JWT密钥

## 注意事项

1. **向后兼容性**：此修改会使现有的聊天接口需要认证，可能影响现有客户端
2. **性能考虑**：每次聊天请求都会查询数据库，建议添加缓存优化
3. **数据隐私**：对话历史包含敏感信息，确保适当的访问控制
4. **错误处理**：认证失败时返回401状态码，客户端需要处理重新登录逻辑

## 后续优化建议

1. **缓存优化**：对用户认证信息和对话记录进行缓存
2. **批量操作**：对于频繁的token使用记录，考虑批量更新
3. **监控告警**：添加认证失败和数据库操作的监控
4. **权限细化**：根据用户类型实现不同的功能权限 