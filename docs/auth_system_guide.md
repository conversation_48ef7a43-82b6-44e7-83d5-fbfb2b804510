# DeerFlow 认证系统使用指南

## 系统概述

DeerFlow 认证系统提供完整的用户管理功能，包括用户注册、登录、积分管理和对话历史记录。支持邮箱验证码登录和Google OAuth登录两种方式。

## 功能特性

### 🔐 用户认证
- **邮箱验证码登录**: 通过邮箱接收验证码进行登录
- **Google OAuth登录**: 使用Google账号快速登录
- **JWT Token认证**: 安全的访问令牌机制
- **自动用户创建**: 首次登录自动创建用户账号

### 💳 积分系统
- **初始积分**: 新用户默认获得1000积分
- **积分扣减**: 根据token使用量自动扣减积分
- **积分管理**: 支持管理员增加用户积分

### 📊 对话历史
- **Token统计**: 分别记录reasoning和非reasoning模型的token使用
- **对话管理**: 按thread_id组织对话历史
- **历史查询**: 支持分页查询用户对话历史

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    total_credits INT DEFAULT 1000,
    remaining_credits INT DEFAULT 1000
);
```

### 对话历史表 (chat_histories)
```sql
CREATE TABLE chat_histories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    thread_id VARCHAR(255) NOT NULL,
    non_reasoning_input_tokens BIGINT DEFAULT 0,
    non_reasoning_output_tokens BIGINT DEFAULT 0,
    reasoning_input_tokens BIGINT DEFAULT 0,
    reasoning_output_tokens BIGINT DEFAULT 0,
    conversation_summary TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 环境配置

### 必需配置
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=deerflow

# Redis配置（验证码存储）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT配置
JWT_SECRET_KEY=your-secure-secret-key
JWT_EXPIRATION_HOURS=24

# 邮箱配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### 可选配置
```bash
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com

# Redis密码
REDIS_PASSWORD=your_redis_password
```

## API接口

### 1. 发送验证码
```http
POST /api/auth/send-verification
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

**响应**:
```json
{
    "success": true,
    "message": "验证码已发送到您的邮箱",
    "expires_in": 300
}
```

### 2. 邮箱登录
```http
POST /api/auth/login/email
Content-Type: application/json

{
    "email": "<EMAIL>",
    "verification_code": "123456"
}
```

**响应**:
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "created_at": "2025-01-04T10:00:00",
        "updated_at": "2025-01-04T10:00:00",
        "total_credits": 1000,
        "remaining_credits": 1000
    }
}
```

### 3. Google登录
```http
POST /api/auth/login/google
Content-Type: application/json

{
    "google_token": "google-id-token"
}
```

### 4. 获取用户信息
```http
GET /api/auth/me
Authorization: Bearer {access_token}
```

**响应**:
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "created_at": "2025-01-04T10:00:00",
    "updated_at": "2025-01-04T10:00:00",
    "total_credits": 1000,
    "remaining_credits": 950
}
```

### 5. 记录Token使用
```http
POST /api/auth/token-usage
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "thread_id": "conversation-123",
    "non_reasoning_input_tokens": 100,
    "non_reasoning_output_tokens": 50,
    "reasoning_input_tokens": 200,
    "reasoning_output_tokens": 75,
    "conversation_summary": "关于AI的对话"
}
```

### 6. 获取对话历史
```http
GET /api/auth/chat-history?limit=20&offset=0
Authorization: Bearer {access_token}
```

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install pymysql sqlalchemy redis jwt pyjwt google-auth

# 启动MySQL和Redis
brew services start mysql
brew services start redis

# 配置环境变量
cp env_example.txt .env
# 编辑 .env 文件填入实际配置
```

### 2. 数据库初始化
```bash
python scripts/init_database.py
```

### 3. 启动服务器
```bash
python -m uvicorn src.server.app:app --reload --host 0.0.0.0 --port 8000
```

### 4. 测试认证系统
```bash
python test_auth_system.py
```

## 客户端集成

### JavaScript示例
```javascript
// 发送验证码
async function sendVerificationCode(email) {
    const response = await fetch('/api/auth/send-verification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
    });
    return response.json();
}

// 邮箱登录
async function loginWithEmail(email, verificationCode) {
    const response = await fetch('/api/auth/login/email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            email, 
            verification_code: verificationCode 
        })
    });
    const data = await response.json();
    
    if (data.access_token) {
        localStorage.setItem('access_token', data.access_token);
    }
    
    return data;
}

// 带认证的API调用
async function apiCall(endpoint, options = {}) {
    const token = localStorage.getItem('access_token');
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (token) {
        headers.Authorization = `Bearer ${token}`;
    }
    
    return fetch(endpoint, {
        ...options,
        headers
    });
}
```

### Python客户端示例
```python
import requests

class DeerFlowClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
    
    def send_verification_code(self, email):
        response = requests.post(
            f"{self.base_url}/api/auth/send-verification",
            json={"email": email}
        )
        return response.json()
    
    def login_with_email(self, email, verification_code):
        response = requests.post(
            f"{self.base_url}/api/auth/login/email",
            json={
                "email": email,
                "verification_code": verification_code
            }
        )
        data = response.json()
        if "access_token" in data:
            self.access_token = data["access_token"]
        return data
    
    def get_user_info(self):
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(
            f"{self.base_url}/api/auth/me",
            headers=headers
        )
        return response.json()
    
    def record_token_usage(self, thread_id, **tokens):
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.post(
            f"{self.base_url}/api/auth/token-usage",
            json={"thread_id": thread_id, **tokens},
            headers=headers
        )
        return response.json()
```

## 安全最佳实践

### 1. 环境配置安全
- 使用强密码作为JWT密钥
- 定期轮换API密钥
- 限制数据库用户权限
- 使用HTTPS传输

### 2. 验证码安全
- 验证码5分钟过期
- 限制发送频率
- 使用Redis存储（内存中）
- 验证后立即删除

### 3. Token安全
- JWT Token 24小时过期
- 使用Bearer Token认证
- 客户端安全存储Token
- 定期刷新Token

### 4. 邮箱配置
- 使用应用专用密码
- 配置邮箱发送限制
- 监控邮件发送状态

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接参数
   - 确认数据库用户权限

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis连接参数
   - 检查防火墙设置

3. **邮件发送失败**
   - 验证SMTP配置
   - 检查邮箱应用密码
   - 确认邮箱服务商设置

4. **Google登录失败**
   - 验证Google Client ID
   - 检查OAuth配置
   - 确认域名白名单

### 调试模式
```bash
# 启用数据库SQL日志
# 在 src/database/database.py 中设置
echo=True

# 启用详细日志
export LOG_LEVEL=DEBUG
```

## 性能优化

### 1. 数据库优化
- 为email字段添加索引
- 为thread_id添加索引
- 定期清理过期验证码
- 使用数据库连接池

### 2. Redis优化
- 设置合适的内存限制
- 配置过期键清理策略
- 使用连接池

### 3. 应用优化
- 缓存用户信息
- 批量处理token记录
- 异步发送邮件

## 监控和维护

### 1. 关键指标
- 用户注册/登录数量
- Token使用统计
- 邮件发送成功率
- API响应时间

### 2. 日志监控
- 认证失败记录
- 数据库错误日志
- 邮件发送日志
- 异常访问模式

### 3. 定期维护
- 清理过期验证码
- 备份用户数据
- 更新安全配置
- 性能优化调整 