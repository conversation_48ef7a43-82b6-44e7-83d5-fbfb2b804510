# LangSmith API 使用指南

DeerFlow 集成了 LangSmith API，允许您查询任务执行历史记录和 token 使用信息。本指南介绍如何使用这些 API 端点。

## 配置

在使用 LangSmith API 之前，确保您的 `.env` 文件包含以下配置：

```bash
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="your_langsmith_api_key"
LANGSMITH_PROJECT="your_project_name"
```

## API 端点

### 1. 检查 LangSmith 状态

**GET** `/api/langsmith/status`

检查 LangSmith 集成状态和配置。

```bash
curl http://localhost:8000/api/langsmith/status
```

**响应示例：**
```json
{
  "available": true,
  "project_name": "deer-flow-project",
  "client_configured": true
}
```

### 2. 获取任务追踪记录

**GET** `/api/langsmith/traces`

获取任务执行的追踪记录。

**参数：**
- `thread_id` (可选): 按特定 thread ID 过滤
- `hours` (可选): 回溯小时数，默认 24 小时，最大 720 小时（30天）
- `limit` (可选): 返回记录数量限制，默认 50，最大 1000

```bash
# 获取最近 24 小时的所有追踪记录
curl http://localhost:8000/api/langsmith/traces

# 获取特定 thread_id 的追踪记录
curl "http://localhost:8000/api/langsmith/traces?thread_id=abc123"

# 获取最近 7 天的追踪记录，限制 100 条
curl "http://localhost:8000/api/langsmith/traces?hours=168&limit=100"
```

**响应示例：**
```json
[
  {
    "trace_id": "trace_123",
    "thread_id": "thread_abc",
    "session_name": "research_task",
    "start_time": "2025-01-08T10:30:00.000Z",
    "end_time": "2025-01-08T10:45:00.000Z",
    "status": "completed",
    "total_runs": 15,
    "total_tokens": {
      "prompt_tokens": 2500,
      "completion_tokens": 1800,
      "total_tokens": 4300,
      "cost_usd": 0.0215
    },
    "total_cost_usd": 0.0215,
    "tags": ["research", "background_task"]
  }
]
```

### 3. 获取任务详细信息

**GET** `/api/langsmith/traces/{thread_id}`

获取特定任务的详细执行信息，包括所有运行步骤。

```bash
curl http://localhost:8000/api/langsmith/traces/thread_abc123
```

**响应示例：**
```json
{
  "trace_id": "trace_123",
  "thread_id": "thread_abc123",
  "session_name": "research_task",
  "start_time": "2025-01-08T10:30:00.000Z",
  "end_time": "2025-01-08T10:45:00.000Z",
  "status": "completed",
  "total_runs": 15,
  "runs": [
    {
      "id": "run_001",
      "name": "planner_agent",
      "run_type": "chain",
      "start_time": "2025-01-08T10:30:00.000Z",
      "end_time": "2025-01-08T10:32:00.000Z",
      "status": "success",
      "token_usage": {
        "prompt_tokens": 500,
        "completion_tokens": 300,
        "total_tokens": 800,
        "cost_usd": 0.004
      },
      "error": null,
      "metadata": {
        "ls_model_name": "gpt-4",
        "agent_type": "planner"
      }
    }
  ],
  "total_tokens": {
    "prompt_tokens": 2500,
    "completion_tokens": 1800,
    "total_tokens": 4300,
    "cost_usd": 0.0215
  },
  "total_cost_usd": 0.0215,
  "tags": ["research", "background_task"]
}
```

### 4. 获取 Token 使用统计

**GET** `/api/langsmith/token-usage/summary`

获取指定时间段的 token 使用统计信息。

**参数：**
- `days` (可选): 分析天数，默认 7 天，最大 90 天
- `start_date` (可选): 开始日期 (YYYY-MM-DD 格式)
- `end_date` (可选): 结束日期 (YYYY-MM-DD 格式)

```bash
# 获取最近 7 天的统计
curl http://localhost:8000/api/langsmith/token-usage/summary

# 获取最近 30 天的统计
curl "http://localhost:8000/api/langsmith/token-usage/summary?days=30"

# 获取指定日期范围的统计
curl "http://localhost:8000/api/langsmith/token-usage/summary?start_date=2025-01-01&end_date=2025-01-07"
```

**响应示例：**
```json
{
  "period": {
    "start_time": "2025-01-01T00:00:00.000Z",
    "end_time": "2025-01-08T00:00:00.000Z",
    "duration_hours": 168
  },
  "summary": {
    "total_traces": 45,
    "completed_traces": 42,
    "failed_traces": 2,
    "success_rate": 0.933,
    "total_prompt_tokens": 125000,
    "total_completion_tokens": 89000,
    "total_tokens": 214000,
    "total_cost_usd": 1.07,
    "avg_tokens_per_trace": 4755.56
  },
  "daily_usage": {
    "2025-01-07": {
      "traces": 8,
      "prompt_tokens": 18000,
      "completion_tokens": 12000,
      "cost_usd": 0.15
    },
    "2025-01-06": {
      "traces": 6,
      "prompt_tokens": 15000,
      "completion_tokens": 10000,
      "cost_usd": 0.125
    }
  },
  "model_usage": {
    "gpt-4": {
      "calls": 85,
      "prompt_tokens": 95000,
      "completion_tokens": 67000,
      "cost_usd": 0.81
    },
    "gpt-3.5-turbo": {
      "calls": 120,
      "prompt_tokens": 30000,
      "completion_tokens": 22000,
      "cost_usd": 0.26
    }
  }
}
```

### 5. 获取追踪详细信息

**GET** `/api/langsmith/trace-details/{trace_id}`

获取 LangSmith 中特定追踪的原始详细信息。

```bash
curl http://localhost:8000/api/langsmith/trace-details/trace_123
```

### 6. 获取最近活动概览

**GET** `/api/langsmith/recent-activity`

获取最近活动的快速概览。

**参数：**
- `hours` (可选): 回溯小时数，默认 24 小时，最大 168 小时（7天）

```bash
# 获取最近 24 小时的活动概览
curl http://localhost:8000/api/langsmith/recent-activity

# 获取最近 48 小时的活动概览
curl "http://localhost:8000/api/langsmith/recent-activity?hours=48"
```

**响应示例：**
```json
{
  "period": {
    "hours": 24,
    "start_time": "2025-01-07T10:00:00.000Z",
    "end_time": "2025-01-08T10:00:00.000Z"
  },
  "summary": {
    "total_traces": 12,
    "completed_traces": 11,
    "failed_traces": 1,
    "running_traces": 0,
    "success_rate": 0.917,
    "total_tokens": 52000,
    "total_cost_usd": 0.26
  },
  "recent_traces": [
    {
      "trace_id": "trace_latest",
      "thread_id": "thread_latest",
      "session_name": "research_task",
      "start_time": "2025-01-08T09:30:00.000Z",
      "end_time": "2025-01-08T09:45:00.000Z",
      "status": "completed",
      "total_runs": 8,
      "total_tokens": {
        "prompt_tokens": 3000,
        "completion_tokens": 2000,
        "total_tokens": 5000,
        "cost_usd": 0.025
      },
      "total_cost_usd": 0.025,
      "tags": ["research"]
    }
  ]
}
```

## 使用示例

### Python 客户端示例

```python
import requests
import json

# API 基础 URL
BASE_URL = "http://localhost:8000/api/langsmith"

def get_langsmith_status():
    """检查 LangSmith 状态"""
    response = requests.get(f"{BASE_URL}/status")
    return response.json()

def get_recent_traces(hours=24, limit=50):
    """获取最近的追踪记录"""
    params = {"hours": hours, "limit": limit}
    response = requests.get(f"{BASE_URL}/traces", params=params)
    return response.json()

def get_task_detail(thread_id):
    """获取特定任务的详细信息"""
    response = requests.get(f"{BASE_URL}/traces/{thread_id}")
    return response.json()

def get_token_usage_summary(days=7):
    """获取 token 使用统计"""
    params = {"days": days}
    response = requests.get(f"{BASE_URL}/token-usage/summary", params=params)
    return response.json()

def get_recent_activity(hours=24):
    """获取最近活动概览"""
    params = {"hours": hours}
    response = requests.get(f"{BASE_URL}/recent-activity", params=params)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 检查状态
    status = get_langsmith_status()
    print("LangSmith Status:", json.dumps(status, indent=2))
    
    # 获取最近 24 小时的任务
    traces = get_recent_traces(hours=24, limit=10)
    print(f"Found {len(traces)} traces in last 24 hours")
    
    # 获取 token 使用统计
    usage = get_token_usage_summary(days=7)
    print(f"Total tokens used in last 7 days: {usage['summary']['total_tokens']}")
    print(f"Total cost: ${usage['summary']['total_cost_usd']:.3f}")
    
    # 获取最近活动
    activity = get_recent_activity()
    print(f"Recent activity summary: {activity['summary']}")
```

### JavaScript/TypeScript 客户端示例

```javascript
const BASE_URL = "http://localhost:8000/api/langsmith";

class LangSmithClient {
  async getStatus() {
    const response = await fetch(`${BASE_URL}/status`);
    return response.json();
  }
  
  async getRecentTraces(hours = 24, limit = 50) {
    const params = new URLSearchParams({ hours: hours.toString(), limit: limit.toString() });
    const response = await fetch(`${BASE_URL}/traces?${params}`);
    return response.json();
  }
  
  async getTaskDetail(threadId) {
    const response = await fetch(`${BASE_URL}/traces/${threadId}`);
    return response.json();
  }
  
  async getTokenUsageSummary(days = 7) {
    const params = new URLSearchParams({ days: days.toString() });
    const response = await fetch(`${BASE_URL}/token-usage/summary?${params}`);
    return response.json();
  }
  
  async getRecentActivity(hours = 24) {
    const params = new URLSearchParams({ hours: hours.toString() });
    const response = await fetch(`${BASE_URL}/recent-activity?${params}`);
    return response.json();
  }
}

// 使用示例
const client = new LangSmithClient();

async function main() {
  try {
    // 检查状态
    const status = await client.getStatus();
    console.log("LangSmith Status:", status);
    
    // 获取最近的任务
    const traces = await client.getRecentTraces(24, 10);
    console.log(`Found ${traces.length} traces in last 24 hours`);
    
    // 获取 token 使用统计
    const usage = await client.getTokenUsageSummary(7);
    console.log(`Total tokens used in last 7 days: ${usage.summary.total_tokens}`);
    console.log(`Total cost: $${usage.summary.total_cost_usd?.toFixed(3) || '0.000'}`);
    
  } catch (error) {
    console.error("Error:", error);
  }
}

main();
```

## 错误处理

API 可能返回以下错误状态码：

- **503 Service Unavailable**: LangSmith 集成不可用（配置问题）
- **404 Not Found**: 请求的资源不存在
- **400 Bad Request**: 请求参数无效
- **500 Internal Server Error**: 服务器内部错误

## 注意事项

1. **配置要求**: 确保 LangSmith API 密钥和项目名称正确配置
2. **速率限制**: LangSmith API 可能有速率限制，大量请求时需要注意
3. **数据延迟**: LangSmith 数据可能有轻微延迟，最新的任务信息可能需要几分钟才能显示
4. **权限**: 确保 API 密钥有访问指定项目的权限
5. **时间范围**: 大时间范围的查询可能需要更长时间，建议使用合理的时间窗口

## 故障排查

### LangSmith 不可用

如果 `/api/langsmith/status` 返回 `available: false`，请检查：

1. `.env` 文件中的 LangSmith 配置
2. API 密钥是否有效
3. 项目名称是否正确
4. 网络连接是否正常

### 找不到数据

如果查询返回空结果：

1. 确认查询的时间范围内有任务执行
2. 检查 thread_id 是否正确
3. 确认 LangSmith tracing 已启用并正常工作

### 性能优化

1. 使用合理的时间范围和限制参数
2. 避免频繁查询大量数据
3. 考虑缓存经常访问的数据 