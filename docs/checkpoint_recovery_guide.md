# Checkpoint恢复功能使用指南

## 概述

DeerFlow现在支持从中断点恢复执行，而不是从第一个节点重新开始。这个功能基于LangGraph的checkpoint机制实现，可以显著提高用户体验，特别是在网络不稳定或长时间执行的任务中。

## 功能特点

1. **自动checkpoint保存**：每个节点执行后都会自动保存checkpoint
2. **智能恢复**：客户端断开连接时，系统会从最后一个成功的checkpoint恢复执行
3. **状态保持**：恢复执行时保持所有状态信息，包括消息历史、计划、观察结果等
4. **错误处理**：如果checkpoint恢复失败，会自动回退到从头开始执行

## 实现原理

### 1. Checkpoint机制

DeerFlow使用LangGraph的`MemorySaver`来实现checkpoint功能：

```python
from langgraph.checkpoint.memory import MemorySaver

def build_graph_with_memory():
    memory = MemorySaver()
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)
```

### 2. 状态获取

当客户端断开连接时，系统会获取当前的checkpoint状态：

```python
current_state = graph.get_state({"configurable": {"thread_id": thread_id}})
```

### 3. 恢复执行

使用checkpoint配置从中断点继续执行：

```python
async for agent, _, event_data in graph.astream(
    None,  # 从checkpoint恢复时传入None
    config=current_state.config,  # 使用checkpoint配置
    stream_mode=["messages", "updates"],
    subgraphs=True,
):
    # 处理恢复执行的事件
```

## 使用场景

### 1. 网络中断恢复

当用户的网络连接不稳定导致客户端断开时，系统会自动从中断点继续执行，用户重新连接后可以看到任务继续进行。

### 2. 长时间任务

对于需要长时间执行的研究任务，即使用户关闭浏览器，任务也会在后台继续执行，用户可以稍后查看结果。

### 3. 系统维护

在系统维护或重启时，可以从checkpoint恢复未完成的任务。

## API变化

### 新增方法

在`BackgroundTaskManager`中新增了以下方法：

1. `start_background_execution_from_checkpoint()` - 从checkpoint启动后台执行
2. `_execute_graph_from_checkpoint()` - 从checkpoint执行graph的内部方法

### 修改的方法

`handle_client_disconnection()`方法现在会：
1. 获取当前checkpoint状态
2. 尝试从checkpoint恢复执行
3. 如果失败，回退到从头开始执行

## 监控和调试

### 日志信息

系统会记录详细的checkpoint相关日志：

```
INFO: 获取到当前checkpoint状态: next=('coordinator',), checkpoint_id=1f051a49-1d20-6914-8000-100aa2dda885
INFO: 后台任务已从checkpoint启动: task_123
INFO: checkpoint恢复后台任务 task_123 进度: 45%, 当前节点: planner
```

### 状态查询

可以通过以下方式查询checkpoint状态：

```python
# 获取当前状态
current_state = graph.get_state(config)

# 获取状态历史
history = list(graph.get_state_history(config))
```

## 配置选项

目前checkpoint功能使用内存存储，适合开发和测试环境。对于生产环境，建议使用持久化存储：

```python
# 生产环境建议使用PostgreSQL或SQLite
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.sqlite import SqliteSaver

# PostgreSQL
checkpointer = PostgresSaver.from_conn_string("postgresql://...")

# SQLite
checkpointer = SqliteSaver.from_conn_string("sqlite:///checkpoints.db")
```

## 错误处理和回退机制

系统实现了智能的错误处理机制：

### 1. Checkpoint验证
- 检查checkpoint配置是否包含必要的字段
- 验证thread_id和checkpoint_id的有效性

### 2. 自动回退
当checkpoint恢复失败时（如遇到 `Received no input for __start__` 错误），系统会：
1. 记录错误信息
2. 自动回退到完整执行模式
3. 从对话历史重新构建执行上下文

### 3. 常见错误及解决方案

#### `Received no input for __start__`
- **原因**：checkpoint配置无效或checkpoint不存在
- **解决**：系统自动回退到完整执行
- **预防**：确保thread_id在传递过程中不被修改

#### Checkpoint配置损坏
- **原因**：配置在传递过程中被意外修改
- **解决**：验证配置完整性，必要时重新获取
- **预防**：使用深拷贝传递配置对象

## 注意事项

1. **内存使用**：当前使用内存存储checkpoint，重启服务会丢失checkpoint数据
2. **并发处理**：每个thread_id对应一个独立的checkpoint链
3. **错误恢复**：如果checkpoint损坏或不可用，系统会自动回退到从头执行
4. **性能影响**：checkpoint保存会有轻微的性能开销，但对用户体验的提升是值得的
5. **配置完整性**：确保checkpoint配置在传递过程中不被修改

## 未来改进

1. **持久化存储**：支持PostgreSQL/SQLite等持久化checkpoint存储
2. **选择性恢复**：允许用户选择从哪个checkpoint恢复
3. **checkpoint清理**：自动清理过期的checkpoint数据
4. **可视化界面**：在Web界面中显示checkpoint状态和恢复选项

## 测试验证

可以使用提供的测试脚本验证checkpoint功能：

```bash
uv run python test_checkpoint_recovery.py
```

测试脚本会模拟执行中断和恢复过程，验证功能的正确性。
