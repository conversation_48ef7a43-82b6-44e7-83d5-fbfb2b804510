# RAGFlow Upload Guide

本指南介绍如何使用DeerFlow的RAGFlow集成功能来管理和上传文档。

## 环境配置

### 1. 设置环境变量

在使用RAGFlow功能之前，需要配置以下环境变量：

```bash
# RAGFlow服务器地址
export RAGFLOW_API_URL="http://your-ragflow-server:9380"

# RAGFlow API密钥
export RAGFLOW_API_KEY="your-api-key-here"

# 可选：每页显示文档数量（默认10）
export RAGFLOW_PAGE_SIZE="20"
```

### 2. 配置DeerFlow

确保在DeerFlow配置中启用了RAGFlow作为RAG提供商：

```yaml
# 在配置文件中设置
selected_rag_provider: "ragflow"
```

## API接口

DeerFlow提供了完整的RAGFlow管理API接口：

### 1. 创建数据集

**POST** `/api/rag/datasets`

```json
{
  "name": "My Dataset",
  "description": "A sample dataset for testing",
  "chunk_method": "naive",
  "embedding_model": "BAAI/bge-large-zh-v1.5@BAAI"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "Dataset created successfully",
  "dataset": {
    "id": "dataset-id-123",
    "name": "My Dataset",
    "description": "A sample dataset for testing",
    "status": "1"
  }
}
```

### 2. 上传文档

**POST** `/api/rag/upload`

```json
{
  "dataset_id": "dataset-id-123",
  "files": [
    {
      "name": "document1.txt",
      "content": "文档内容的字节数据"
    },
    {
      "name": "document2.pdf",
      "content": "PDF文档的字节数据"
    }
  ]
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "Documents uploaded successfully",
  "data": [
    {
      "id": "doc-id-1",
      "name": "document1.txt",
      "status": "uploaded"
    },
    {
      "id": "doc-id-2", 
      "name": "document2.pdf",
      "status": "uploaded"
    }
  ]
}
```

### 3. 列出文档

**GET** `/api/rag/datasets/{dataset_id}/documents`

查询参数：
- `keywords`: 关键词搜索（可选）
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认30）

**响应示例：**
```json
{
  "documents": [
    {
      "id": "doc-id-1",
      "name": "document1.txt",
      "size": 1024,
      "run": "DONE",
      "status": "1",
      "chunk_count": 5,
      "token_count": 150
    }
  ]
}
```

### 4. 删除文档

**DELETE** `/api/rag/documents`

```json
{
  "dataset_id": "dataset-id-123",
  "document_ids": ["doc-id-1", "doc-id-2"]
}
```

### 5. 解析文档

**POST** `/api/rag/documents/parse`

```json
{
  "dataset_id": "dataset-id-123", 
  "document_ids": ["doc-id-1", "doc-id-2"]
}
```

启动文档解析后，RAGFlow会：
1. 将文档分块（chunking）
2. 生成向量嵌入
3. 建立索引
4. 准备用于检索

## 使用示例

### Python示例

```python
import requests
import base64

# 1. 创建数据集
def create_dataset():
    url = "http://localhost:8000/api/rag/datasets"
    payload = {
        "name": "研究文档集",
        "description": "用于研究的文档集合",
        "chunk_method": "naive"
    }
    response = requests.post(url, json=payload)
    return response.json()["dataset"]["id"]

# 2. 上传文档
def upload_document(dataset_id, file_path):
    with open(file_path, 'rb') as f:
        content = f.read()
    
    url = "http://localhost:8000/api/rag/upload"
    payload = {
        "dataset_id": dataset_id,
        "files": [
            {
                "name": "my_document.txt",
                "content": content
            }
        ]
    }
    response = requests.post(url, json=payload)
    return response.json()

# 3. 开始解析
def parse_documents(dataset_id, document_ids):
    url = "http://localhost:8000/api/rag/documents/parse"
    payload = {
        "dataset_id": dataset_id,
        "document_ids": document_ids
    }
    response = requests.post(url, json=payload)
    return response.json()

# 使用示例
dataset_id = create_dataset()
result = upload_document(dataset_id, "my_file.txt")
document_ids = [doc["id"] for doc in result["data"]]
parse_documents(dataset_id, document_ids)
```

### JavaScript示例

```javascript
// 创建数据集
async function createDataset() {
    const response = await fetch('http://localhost:8000/api/rag/datasets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: '研究文档集',
            description: '用于研究的文档集合',
            chunk_method: 'naive'
        })
    });
    const result = await response.json();
    return result.dataset.id;
}

// 上传文档
async function uploadDocument(datasetId, file) {
    const arrayBuffer = await file.arrayBuffer();
    const content = new Uint8Array(arrayBuffer);
    
    const response = await fetch('http://localhost:8000/api/rag/upload', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            dataset_id: datasetId,
            files: [{
                name: file.name,
                content: Array.from(content)
            }]
        })
    });
    return response.json();
}
```

## 支持的文件格式

RAGFlow支持多种文档格式：

- **文本文件**: .txt, .md
- **文档**: .pdf, .docx, .doc
- **表格**: .xlsx, .xls, .csv
- **演示文稿**: .pptx, .ppt
- **代码**: .py, .js, .java, .cpp等
- **网页**: .html, .htm

## 分块方法

RAGFlow提供多种文档分块方法：

- **naive**: 通用分块（默认）
- **manual**: 手动分块
- **qa**: 问答格式
- **table**: 表格处理
- **paper**: 学术论文
- **book**: 书籍章节
- **laws**: 法律文档
- **presentation**: 演示文稿
- **picture**: 图片识别
- **email**: 邮件格式

## 工作流程

1. **创建数据集**: 为特定用途创建数据集
2. **上传文档**: 将文档文件上传到数据集
3. **启动解析**: 开始文档分析和索引
4. **等待处理**: 文档状态从 `UNSTART` → `RUNNING` → `DONE`
5. **开始使用**: 在DeerFlow研究流程中使用该数据集

## 状态说明

文档处理状态：
- `UNSTART`: 未开始处理
- `RUNNING`: 正在处理中
- `CANCEL`: 已取消
- `DONE`: 处理完成
- `FAIL`: 处理失败

## 最佳实践

### 1. 文档准备
- 确保文档内容清晰、结构化
- 避免上传过大的单个文件（建议<50MB）
- 使用描述性的文件名

### 2. 数据集管理
- 按主题或项目组织数据集
- 使用有意义的数据集名称和描述
- 定期清理不需要的文档

### 3. 性能优化
- 批量上传多个小文件而不是单个大文件
- 选择合适的分块方法
- 监控解析进度和状态

### 4. 错误处理
- 检查API响应状态
- 处理网络超时和重试
- 监控文档处理状态

## 故障排除

### 常见问题

**Q: 上传失败，显示"RAGFlow provider not available"**
A: 检查环境变量设置和RAGFlow服务器连接

**Q: 文档一直显示"RUNNING"状态**
A: 等待处理完成，大文档可能需要几分钟时间

**Q: 解析失败**
A: 检查文档格式是否支持，文件是否损坏

**Q: 检索不到文档内容**
A: 确认文档已解析完成且状态为"DONE"

### 调试技巧

1. 检查服务器日志
2. 验证API密钥和URL
3. 测试网络连接
4. 查看文档处理状态

## 进阶功能

### 批量操作

```python
# 批量上传文档
def batch_upload(dataset_id, file_paths):
    files = []
    for path in file_paths:
        with open(path, 'rb') as f:
            files.append({
                "name": os.path.basename(path),
                "content": f.read()
            })
    
    return upload_documents(dataset_id, files)

# 批量删除文档
def batch_delete(dataset_id, document_ids):
    url = "http://localhost:8000/api/rag/documents"
    payload = {
        "dataset_id": dataset_id,
        "document_ids": document_ids
    }
    response = requests.delete(url, json=payload)
    return response.json()
```

### 监控和日志

```python
import time

def wait_for_parsing(dataset_id, document_ids):
    """等待文档解析完成"""
    while True:
        docs = list_documents(dataset_id)
        
        # 检查所有文档状态
        all_done = all(
            doc["run"] in ["DONE", "FAIL"] 
            for doc in docs 
            if doc["id"] in document_ids
        )
        
        if all_done:
            break
            
        print("等待解析完成...")
        time.sleep(5)
    
    print("解析完成！")
```

## 集成到研究流程

在DeerFlow的研究对话中，上传的文档会自动作为可用资源：

1. 在对话界面选择数据集作为资源
2. 提出研究问题
3. DeerFlow会自动检索相关文档
4. 基于文档内容生成答案

这样，你就可以充分利用RAGFlow的文档管理能力来增强DeerFlow的研究功能。 