# DeerFlow RAG功能使用指南

## 概述

DeerFlow集成了RAG (Retrieval-Augmented Generation) 功能，允许用户上传自己的文档并在研究过程中检索相关内容。系统支持多种文档格式，并能够智能地从用户的知识库中检索相关信息。

## 功能特性

### ✨ 核心功能

- **文档上传管理**: 支持多种格式文档的上传和管理
- **智能检索**: 基于语义相似度的文档检索
- **实时集成**: 与聊天界面无缝集成，自动检索相关文档内容
- **多数据集支持**: 支持创建和管理多个数据集
- **文档解析**: 自动解析和分块处理上传的文档

### 🔧 技术特性

- 支持RAGFlow作为后端服务
- RESTful API接口
- 流式响应支持
- 错误处理和重试机制

## 环境配置

### 必需的环境变量

```bash
# RAG服务配置
export RAG_PROVIDER="ragflow"
export RAGFLOW_API_URL="https://demo.ragflow.io"  # 或本地服务器地址
export RAGFLOW_API_KEY="your-api-key-here"
export RAGFLOW_PAGE_SIZE="10"  # 可选，默认10
```

### 配置示例

```yaml
# conf.yaml
BASIC_MODEL:
  base_url: https://openrouter.ai/api/v1
  model: "google/gemini-2.5-flash-preview"
  api_key: your-api-key
```

## API使用指南

### 1. 获取RAG配置

```bash
curl http://localhost:8000/api/rag/config
```

响应：
```json
{
  "provider": "ragflow"
}
```

### 2. 列出可用资源

```bash
curl http://localhost:8000/api/rag/resources
```

响应：
```json
{
  "resources": [
    {
      "uri": "rag://dataset/123",
      "title": "My Research Dataset",
      "description": "Academic papers collection"
    }
  ]
}
```

### 3. 创建新数据集

```bash
curl -X POST http://localhost:8000/api/rag/datasets \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My New Dataset",
    "description": "Description of the dataset",
    "chunk_method": "naive",
    "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
  }'
```

### 4. 上传文档

```bash
curl -X POST http://localhost:8000/api/rag/upload \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_id": "your-dataset-id",
    "files": [
      {
        "name": "document.txt",
        "content": [72, 101, 108, 108, 111]  # "Hello" 的字节数组
      }
    ]
  }'
```

### 5. 列出数据集中的文档

```bash
curl "http://localhost:8000/api/rag/datasets/{dataset_id}/documents?page=1&page_size=10"
```

### 6. 启动文档解析

```bash
curl -X POST http://localhost:8000/api/rag/documents/parse \
  -H "Content-Type: application/json" \
  -d '{
    "dataset_id": "your-dataset-id",
    "document_ids": ["doc1", "doc2"]
  }'
```

## 在聊天中使用RAG

### 通过Web界面

1. 在聊天界面中，点击"资源"按钮
2. 选择要使用的数据集
3. 输入问题，系统会自动检索相关文档并结合回答

### 通过API

```bash
curl -X POST http://localhost:8000/api/chat/stream \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "Tell me about machine learning"}
    ],
    "thread_id": "test-thread",
    "resources": [
      {
        "uri": "rag://dataset/your-dataset-id",
        "title": "ML Papers",
        "description": "Machine learning research papers"
      }
    ],
    "auto_accepted_plan": true
  }'
```

## 测试功能

### 运行测试脚本

项目包含了完整的测试脚本来验证RAG功能：

```bash
# 完整功能测试（推荐）
python test_rag_complete.py

# 仅上传功能测试
python test_rag_upload.py

# 仅检索功能测试
python test_rag_retrieval.py

# 调试连接问题
python debug_rag.py
```

### 使用模拟服务器测试

如果没有可用的RAGFlow服务器，可以使用内置的模拟服务器：

```bash
# 启动模拟服务器
python mock_ragflow_server.py

# 在另一个终端中设置环境变量并测试
export RAGFLOW_API_URL="http://localhost:9380"
export RAGFLOW_API_KEY="mock-api-key-for-testing"
python test_rag_complete.py
```

## 故障排除

### 常见问题

1. **连接错误**: 检查`RAGFLOW_API_URL`和`RAGFLOW_API_KEY`是否正确设置
2. **SSL错误**: 对于demo服务器，系统会自动禁用SSL验证
3. **文档上传失败**: 确保文档内容正确编码为字节数组
4. **检索无结果**: 确保文档已经完成解析状态为"DONE"

### 日志调试

```bash
# 查看详细的API调用日志
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from src.rag.builder import build_retriever
retriever = build_retriever()
print(retriever.list_resources())
"
```

### 检查服务状态

```bash
# 检查DeerFlow服务器状态
curl http://localhost:8000/api/rag/config

# 检查RAGFlow服务器状态（如果是模拟服务器）
curl http://localhost:9380/health
```

## 最佳实践

1. **文档组织**: 将相关文档组织到同一个数据集中
2. **命名规范**: 使用描述性的数据集和文档名称
3. **内容质量**: 上传高质量、结构化的文档以获得更好的检索效果
4. **定期清理**: 删除不再需要的文档和数据集
5. **监控使用**: 定期检查文档解析状态和检索效果

## 进阶配置

### 自定义嵌入模型

```json
{
  "name": "Custom Dataset",
  "embedding_model": "sentence-transformers/all-MiniLM-L6-v2",
  "chunk_method": "recursive"
}
```

### 检索参数优化

```json
{
  "page_size": 20,
  "similarity_threshold": 0.7,
  "max_chunks_per_document": 5
}
```

## 支持的文档格式

- 纯文本 (.txt)
- Markdown (.md)
- PDF文档 (.pdf)
- Word文档 (.docx)
- 其他文本格式

## 下一步计划

- [ ] 支持更多RAG提供商
- [ ] 增强文档预处理功能
- [ ] 实现文档版本管理
- [ ] 添加文档标签和分类功能
- [ ] 优化检索算法性能

---

如有问题或建议，请提交Issue或联系开发团队。 