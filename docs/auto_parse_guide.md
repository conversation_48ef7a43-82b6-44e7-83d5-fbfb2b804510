# RAG文档自动解析功能指南

## 功能概述

RAG文档自动解析功能允许在上传文档到RAGFlow后自动启动文档解析过程，无需手动调用解析API。这大大简化了文档处理流程。

## API变更

### 上传端点更新

**端点**: `POST /api/rag/upload`

**新增参数**:
- `auto_parse` (可选): boolean类型，默认为`true`
  - `true`: 上传后自动启动解析
  - `false`: 仅上传，不自动解析

### 请求格式

```bash
curl -X POST "http://localhost:8000/api/rag/upload" \
  -F "file=@document.pdf" \
  -F "dataset_id=your_dataset_id" \
  -F "auto_parse=true"
```

### 响应格式

启用自动解析时的响应示例：

```json
{
  "success": true,
  "message": "Documents uploaded successfully and parsing started",
  "data": {
    "documents": [
      {
        "id": "document_id_123",
        "name": "document.pdf",
        "size": 1024
      }
    ],
    "parse_started": true,
    "parsed_document_ids": ["document_id_123"]
  }
}
```

禁用自动解析时的响应示例：

```json
{
  "success": true,
  "message": "Documents uploaded successfully",
  "data": {
    "documents": [
      {
        "id": "document_id_123",
        "name": "document.pdf",
        "size": 1024
      }
    ]
  }
}
```

## 使用场景

### 场景1: 自动解析（推荐）

适用于大多数情况，上传后立即开始解析：

```bash
# Postman设置
POST http://localhost:8000/api/rag/upload
Body: form-data
- file: [选择文档文件]
- dataset_id: your_dataset_id  
- auto_parse: true  # 或者不设置（默认true）
```

### 场景2: 手动解析

适用于需要批量上传后统一解析的情况：

```bash
# 1. 上传文档（不自动解析）
POST http://localhost:8000/api/rag/upload
Body: form-data
- file: [选择文档文件]
- dataset_id: your_dataset_id
- auto_parse: false

# 2. 手动启动解析
POST http://localhost:8000/api/rag/documents/parse
Body: application/json
{
  "dataset_id": "your_dataset_id",
  "document_ids": ["document_id_1", "document_id_2"]
}
```

## Postman测试步骤

### 1. 创建数据集

```
POST http://localhost:8000/api/rag/datasets
Content-Type: application/json

{
  "name": "自动解析测试数据集",
  "description": "测试自动解析功能",
  "chunk_method": "naive",
  "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
}
```

### 2. 上传文档（启用自动解析）

```
POST http://localhost:8000/api/rag/upload
Body: form-data
- file: [选择PDF/文档文件]
- dataset_id: [步骤1返回的数据集ID]
- auto_parse: true
```

### 3. 检查解析状态

```
GET http://localhost:8000/api/rag/datasets/{dataset_id}/documents
```

查看返回的文档列表中的`run`字段：
- `"UNSTART"`: 解析未开始
- `"RUNNING"`: 解析进行中
- `"DONE"`: 解析成功  
- `"FAIL"`: 解析失败

### 4. 测试检索功能

```
POST http://localhost:8000/api/chat/stream
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "这个文档的主要内容是什么？"
    }
  ],
  "resources": [
    {
      "uri": "rag://dataset/{dataset_id}",
      "title": "测试数据集",
      "description": "用于测试的数据集"
    }
  ],
  "stream": false
}
```

## 底层实现

### RAGFlow API集成

自动解析功能使用RAGFlow的chunks端点：

```bash
curl --request POST \
     --url http://localhost/api/v1/datasets/{dataset_id}/chunks \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer YOUR_API_KEY' \
     --data '{
          "document_ids": ["document_id_1", "document_id_2"]
     }'
```

### 处理流程

1. **文档上传**: 使用multipart/form-data上传到RAGFlow
2. **获取文档ID**: 从上传响应中提取文档ID列表
3. **自动解析**: 如果`auto_parse=true`，立即调用chunks端点
4. **状态反馈**: 在响应中包含解析启动状态和相关信息

## 错误处理

### 上传成功但解析失败

```json
{
  "success": true,
  "message": "Documents uploaded successfully",
  "data": {
    "documents": [...],
    "parse_started": false,
    "parse_error": "解析启动失败的具体原因"
  }
}
```

### 常见问题

1. **解析超时**: 大文档可能需要较长时间解析，这是正常现象
2. **网络问题**: 确保RAGFlow服务正常运行
3. **权限问题**: 检查API密钥是否正确配置

## 性能考虑

- **自动解析**: 适合小到中等大小的文档（<10MB）
- **手动解析**: 适合大文档或批量处理场景
- **并发限制**: RAGFlow可能对同时解析的文档数量有限制

## 监控和调试

### 查看解析日志

DeerFlow控制台会显示解析状态：

```
🔄 自动解析上传的文档: ['doc_id_123']
✅ 文档解析已启动
```

### 解析状态检查

定期调用文档列表API检查解析进度：

```bash
GET /api/rag/datasets/{dataset_id}/documents
```

## 最佳实践

1. **小文档**: 使用自动解析提高效率
2. **大文档**: 考虑禁用自动解析，手动控制时机
3. **批量操作**: 上传时禁用自动解析，完成后批量解析
4. **错误处理**: 检查响应中的解析状态信息
5. **性能优化**: 避免同时上传过多大文档

## 兼容性说明

- **向后兼容**: 现有API调用无需修改（默认启用自动解析）
- **可选功能**: 可以通过`auto_parse=false`禁用
- **无依赖**: 不影响其他RAG功能的使用 