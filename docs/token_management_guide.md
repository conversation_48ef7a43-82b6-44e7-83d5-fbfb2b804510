# DeerFlow Token管理功能使用指南

## 概述

DeerFlow token管理系统提供了实时的token使用量统计和积分检查功能，确保用户在聊天过程中能够有效控制资源消耗。

## 功能特性

### 🔢 Token统计
- **实时统计**: 每个节点完成后自动统计输入和输出token数量
- **累加计算**: Redis存储累计使用量，支持实时查询
- **分类统计**: 区分非reasoning和reasoning模型的token使用
- **持久化**: 聊天结束后自动同步到数据库

### 💰 积分管理
- **积分换算**: 1积分 = 1000 token
- **阈值检查**: 默认最小积分阈值为10积分
- **实时扣除**: 根据token使用量实时扣除积分
- **不足拦截**: 积分不足时自动停止处理

### 📊 监控报告
- **使用量查询**: 实时查询当前会话和历史使用量
- **积分余额**: 查看用户总积分、剩余积分和已使用积分
- **详细统计**: 提供输入、输出、总计等详细数据

## 配置说明

### Redis配置

Token使用量临时存储在Redis中，需要确保Redis服务正常运行：

```bash
# 检查Redis服务状态
brew services list | grep redis

# 启动Redis服务
brew services start redis
```

环境变量配置：
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=  # 可选
```

### 数据库配置

持久化存储使用MySQL数据库，相关表结构：

- `users`: 用户表，包含积分信息
- `chat_histories`: 聊天历史表，记录token使用量

## API接口

### 1. 获取用户积分信息

```http
GET /api/user/credits
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "user_id": 1,
    "total_credits": 1000,
    "remaining_credits": 850,
    "used_credits": 150,
    "tokens_per_credit": 1000,
    "min_credits_threshold": 10
}
```

### 2. 查询Token使用量

```http
GET /api/token/usage/{thread_id}
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "thread_id": "abc123",
    "current_session": {
        "input_tokens": 100,
        "output_tokens": 50
    },
    "historical": {
        "input_tokens": 500,
        "output_tokens": 300
    },
    "total": {
        "input_tokens": 600,
        "output_tokens": 350
    },
    "total_tokens": 950,
    "credits_used": 0.95,
    "user_remaining_credits": 849
}
```

### 3. 聊天流接口（已集成token管理）

```http
POST /api/chat/stream
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "messages": [...],
    "thread_id": "__default__",
    ...
}
```

**积分不足时的响应:**
```http
HTTP/1.1 403 Forbidden
{
    "detail": "积分不足，当前剩余积分: 5，需要至少 10 积分"
}
```

## 使用流程

### 1. 启动会话前检查
- 系统自动检查用户剩余积分
- 积分低于阈值时拒绝开始聊天
- 返回明确的错误信息

### 2. 会话中监控
- 每个节点完成后提取token使用量
- 累加到Redis中的线程记录
- 实时检查积分余额
- 积分不足时中止处理

### 3. 会话结束同步
- 将Redis中的使用量同步到数据库
- 计算总积分消耗并扣除
- 清理Redis临时记录

## 配置参数

可以通过修改`TokenManager`类来调整配置：

```python
class TokenManager:
    def __init__(self):
        self.tokens_per_credit = 1000      # 每积分对应的token数
        self.min_credits_threshold = 10    # 最小积分阈值
```

## 测试验证

### 1. 使用测试脚本

```bash
python test_token_management.py
```

### 2. 使用HTML演示页面

打开 `examples/token_management_demo.html` 在浏览器中进行交互测试。

### 3. 手动测试步骤

1. **登录用户**
   ```bash
   curl -X POST http://localhost:8000/api/auth/login/email \
        -H "Content-Type: application/json" \
        -d '{"email": "<EMAIL>", "verification_code": "123456"}'
   ```

2. **查询积分**
   ```bash
   curl -X GET http://localhost:8000/api/user/credits \
        -H "Authorization: Bearer {token}"
   ```

3. **发起聊天**
   ```bash
   curl -X POST http://localhost:8000/api/chat/stream \
        -H "Authorization: Bearer {token}" \
        -H "Content-Type: application/json" \
        -d '{"messages": [{"role": "user", "content": "Hello"}], "thread_id": "__default__"}'
   ```

4. **查询使用量**
   ```bash
   curl -X GET http://localhost:8000/api/token/usage/{thread_id} \
        -H "Authorization: Bearer {token}"
   ```

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证连接参数配置
   - 查看防火墙设置

2. **Token统计不准确**
   - 检查LLM响应格式
   - 验证metadata提取逻辑
   - 查看日志中的debug信息

3. **积分扣除异常**
   - 检查数据库连接
   - 验证用户权限
   - 查看事务回滚日志

### 调试模式

启用详细日志：

```python
import logging
logging.getLogger('src.server.token_manager').setLevel(logging.DEBUG)
```

查看Redis数据：

```bash
redis-cli
> KEYS token_usage:*
> HGETALL token_usage:your_thread_id
```

## 最佳实践

### 1. 积分管理
- 为新用户设置合理的初始积分
- 定期监控高消耗用户
- 提供积分充值接口

### 2. 性能优化
- 使用Redis管道批量操作
- 定期清理过期的Redis记录
- 异步处理数据库同步

### 3. 监控告警
- 设置积分不足告警
- 监控异常的token消耗
- 跟踪系统整体使用量

## 扩展功能

### 1. 高级统计
- 按时间段统计使用量
- 按用户分析消耗模式
- 生成使用报表

### 2. 灵活计费
- 支持不同模型的计费标准
- 实现动态价格调整
- 提供套餐和优惠

### 3. 限流控制
- 基于积分的QPS限制
- 单用户并发控制
- 资源使用配额

## 相关文档

- [认证系统指南](auth_system_guide.md)
- [API接口文档](../README.md)
- [数据库设计](../src/database/models.py) 