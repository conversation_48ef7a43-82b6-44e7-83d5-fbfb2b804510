# DeerFlow RAG文件上传 Postman测试指南

## 环境配置

### 1. 设置环境变量
在运行DeerFlow服务器之前，请确保设置了正确的环境变量：

```bash
export RAGFLOW_API_URL="http://localhost"  # 您的本地RAGFlow服务地址
export RAGFLOW_API_KEY="your-actual-api-key"  # 您的RAGFlow API密钥
export RAG_PROVIDER="ragflow"
```

### 2. 启动DeerFlow服务器
```bash
cd /path/to/deer-flow
python -m uvicorn src.server.app:app --reload --host 0.0.0.0 --port 8000
```

## Postman测试步骤

### 步骤1: 创建数据集
**方法**: POST  
**URL**: `http://localhost:8000/api/rag/datasets`  
**Headers**: 
- Content-Type: application/json

**Body** (raw JSON):
```json
{
    "name": "My Test Dataset",
    "description": "测试PDF和其他文件上传",
    "chunk_method": "naive",
    "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
}
```

**期望响应**:
```json
{
    "success": true,
    "message": "Dataset created successfully",
    "dataset": {
        "id": "dataset-uuid-here",
        "name": "My Test Dataset",
        ...
    }
}
```

**重要**: 复制返回的 `dataset.id`，下一步需要使用。

### 步骤2: 上传文件
**方法**: POST  
**URL**: `http://localhost:8000/api/rag/upload`  
**Headers**: 
- 不需要设置Content-Type（Postman会自动设置为multipart/form-data）

**Body** (form-data):
- `file`: [选择文件] - 可以是PDF、TXT、MD等格式
- `dataset_id`: [粘贴上一步获得的dataset ID]

**测试文件建议**:
- PDF文件：研究论文、技术文档等
- 文本文件：README.txt、说明文档等  
- Markdown文件：项目文档、笔记等

**期望响应**:
```json
{
    "success": true,
    "message": "Documents uploaded successfully",
    "data": {
        "documents": [
            {
                "id": "document-uuid-here",
                "name": "your-file.pdf",
                "status": "uploaded",
                "size": 1234567,
                "type": "application/pdf"
            }
        ]
    }
}
```

### 步骤3: 查看已上传文档
**方法**: GET  
**URL**: `http://localhost:8000/api/rag/datasets/{dataset_id}/documents`  
**参数**:
- `page`: 1 (可选)
- `page_size`: 30 (可选)

**期望响应**:
```json
{
    "documents": [
        {
            "id": "document-uuid",
            "name": "your-file.pdf",
            "size": 1234567,
            "run": "UNSTART",  // 处理状态
            "status": "1",
            "chunk_count": 0,
            "token_count": 0,
            "create_time": "2025-01-11T10:30:00"
        }
    ]
}
```

### 步骤4: 启动文档解析
**方法**: POST  
**URL**: `http://localhost:8000/api/rag/documents/parse`  
**Headers**: 
- Content-Type: application/json

**Body** (raw JSON):
```json
{
    "dataset_id": "your-dataset-id",
    "document_ids": ["document-id-1", "document-id-2"]
}
```

**期望响应**:
```json
{
    "success": true,
    "message": "Document parsing started successfully"
}
```

### 步骤5: 检查解析状态
重复步骤3，查看文档的 `run` 状态：
- `UNSTART`: 未开始
- `RUNNING`: 处理中
- `DONE`: 完成
- `FAIL`: 失败

## 测试用例示例

### 用例1: PDF文件上传
1. 选择一个PDF文件（如研究论文、技术手册）
2. 上传到新创建的数据集
3. 验证文件信息正确显示
4. 启动解析并等待完成

### 用例2: 多格式文件测试
分别测试以下文件类型：
- ✅ PDF (.pdf) - application/pdf
- ✅ 文本 (.txt) - text/plain  
- ✅ Markdown (.md) - text/markdown
- ✅ Word文档 (.docx) - application/vnd.openxml...

### 用例3: 大文件上传
测试较大的文件（如10MB+的PDF）来验证：
- 上传超时处理
- 进度显示
- 内存使用

## 常见问题排查

### 1. 上传失败
- **检查**: 文件大小是否超限
- **检查**: dataset_id是否有效
- **检查**: RAGFlow服务是否正常运行

### 2. 解析失败
- **检查**: 文件格式是否支持
- **检查**: 文件是否损坏
- **查看**: DeerFlow服务器日志

### 3. API密钥错误
- **检查**: RAGFLOW_API_KEY是否正确设置
- **检查**: RAGFlow服务器认证配置

## 自动化测试

您也可以运行自动化测试脚本：

```bash
# 确保环境变量已设置
export RAGFLOW_API_URL="http://localhost"
export RAGFLOW_API_KEY="your-api-key"

# 运行测试
python test_file_upload.py
```

## 后续测试

文件上传成功后，您可以测试：

1. **文档检索**: 通过聊天界面搜索上传的文档内容
2. **集成测试**: 在研究工作流中使用上传的文档
3. **性能测试**: 大量文档的上传和检索性能

---

## 技术细节

### 支持的文件格式
- **文本类**: .txt, .md, .rst
- **文档类**: .pdf, .doc, .docx  
- **其他**: 根据RAGFlow配置

### API认证
- 使用Bearer Token认证
- API密钥在请求头中：`Authorization: Bearer {api_key}`

### 错误处理
- 网络超时重试
- SSL验证降级（demo服务器）
- 详细错误日志记录 