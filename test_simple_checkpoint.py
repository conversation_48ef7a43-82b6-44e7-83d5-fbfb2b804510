#!/usr/bin/env python3
"""
简单的checkpoint测试
"""

import asyncio
import logging
import sys
import os
from uuid import uuid4

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.graph.builder import build_graph_with_memory
from langchain_core.messages import HumanMessage

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_empty_input_error():
    """测试EmptyInputError的具体情况"""
    
    graph = build_graph_with_memory()
    thread_id = str(uuid4())
    
    logger.info(f"测试thread_id: {thread_id}")
    
    # 1. 先正常执行一些步骤
    config = {"configurable": {"thread_id": thread_id}}
    input_ = {
        "messages": [HumanMessage(content="测试消息")],
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": True,
        "enable_background_investigation": True,
        "user_id": 1,
        "thread_id": thread_id,
    }
    
    logger.info("1. 执行初始步骤...")
    step_count = 0
    try:
        async for agent, _, event_data in graph.astream(
            input_,
            config=config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            step_count += 1
            logger.info(f"步骤 {step_count}: {agent}")
            if step_count >= 2:
                break
    except Exception as e:
        logger.error(f"初始执行失败: {e}")
        return
    
    # 2. 获取checkpoint状态
    logger.info("2. 获取checkpoint状态...")
    current_state = graph.get_state(config)
    logger.info(f"当前状态: next={current_state.next}")
    logger.info(f"checkpoint_id: {current_state.config.get('configurable', {}).get('checkpoint_id')}")
    
    if not current_state.next:
        logger.info("任务已完成，无法测试恢复")
        return
    
    # 3. 测试不同的恢复方式
    checkpoint_config = current_state.config
    
    # 测试1: 使用None
    logger.info("3. 测试使用None恢复...")
    try:
        count = 0
        async for agent, _, event_data in graph.astream(
            None,
            config=checkpoint_config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            count += 1
            logger.info(f"None恢复步骤 {count}: {agent}")
            if count >= 2:
                break
        logger.info("✅ 使用None恢复成功")
    except Exception as e:
        logger.error(f"❌ 使用None恢复失败: {e}")
        
        # 测试2: 使用空字典
        logger.info("4. 测试使用空字典恢复...")
        try:
            count = 0
            async for agent, _, event_data in graph.astream(
                {},
                config=checkpoint_config,
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                count += 1
                logger.info(f"空字典恢复步骤 {count}: {agent}")
                if count >= 2:
                    break
            logger.info("✅ 使用空字典恢复成功")
        except Exception as e2:
            logger.error(f"❌ 使用空字典恢复也失败: {e2}")
            
            # 测试3: 使用原始输入
            logger.info("5. 测试使用原始输入恢复...")
            try:
                count = 0
                async for agent, _, event_data in graph.astream(
                    input_,
                    config=checkpoint_config,
                    stream_mode=["messages", "updates"],
                    subgraphs=True,
                ):
                    count += 1
                    logger.info(f"原始输入恢复步骤 {count}: {agent}")
                    if count >= 2:
                        break
                logger.info("✅ 使用原始输入恢复成功")
            except Exception as e3:
                logger.error(f"❌ 使用原始输入恢复也失败: {e3}")

async def test_invalid_checkpoint():
    """测试无效checkpoint的情况"""
    
    graph = build_graph_with_memory()
    
    # 使用一个无效的checkpoint配置
    invalid_config = {
        "configurable": {
            "thread_id": "invalid-thread",
            "checkpoint_ns": "",
            "checkpoint_id": "invalid-checkpoint"
        }
    }
    
    logger.info("6. 测试无效checkpoint...")
    try:
        count = 0
        async for agent, _, event_data in graph.astream(
            None,
            config=invalid_config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            count += 1
            logger.info(f"无效checkpoint步骤 {count}: {agent}")
            if count >= 2:
                break
        logger.info("⚠️ 无效checkpoint竟然成功了")
    except Exception as e:
        logger.error(f"❌ 无效checkpoint失败（预期）: {e}")
        if "Received no input for __start__" in str(e):
            logger.info("✅ 这就是我们在后台任务中遇到的错误")

if __name__ == "__main__":
    print("简单checkpoint测试...")
    
    # 运行测试
    asyncio.run(test_empty_input_error())
    
    print("\n" + "="*30 + "\n")
    
    # 测试无效checkpoint
    asyncio.run(test_invalid_checkpoint())
    
    print("测试完成!")
