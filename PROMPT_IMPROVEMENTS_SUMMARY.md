# 提示词改进总结

本文档总结了对DeerFlow论文写作工作流程中提示词的重要改进，解决了引用管理和思考深度的问题。

## 🎯 改进目标

1. **大纲阶段**：移除references生成，专注于结构化大纲
2. **论文写作阶段**：严格要求只引用有URL来源的研究材料
3. **思考阶段**：大幅增强深度思考能力和独立分析框架

## 📝 具体修改

### 1. 大纲生成器 (`src/prompts/outline_writer_simple.md`)

#### 修改内容：
- ✅ 添加明确指令：**DO NOT generate references or citations**
- ✅ 强调专注于结构和内容规划
- ✅ 说明引用将在写作阶段处理
- ✅ 优化subsections内容描述

#### 关键改进：
```markdown
## Instructions
5. **DO NOT generate references or citations** - focus only on structure and content planning

## Important Notes
- Focus on creating a logical structure and content flow
- Do NOT include any references, citations, or bibliography information
- The outline should guide content creation, not provide citations
- Citations will be handled during the actual writing phase
```

### 2. 论文写作器 (`src/prompts/paper_writer_simple.md`)

#### 修改内容：
- ✅ 新增严格的引用要求部分
- ✅ 明确只引用有verifiable URLs的材料
- ✅ 详细列出应该引用和不应该引用的内容
- ✅ 提供不确定时的指导原则

#### 关键改进：
```markdown
2. **Citation Requirements**:
   - **CRITICAL**: Only reference research materials that include specific URLs or verifiable sources
   - **AVOID**: General statements, broad claims, or thinking-stage insights without specific sources
   - **AVOID**: Citing internal analysis or synthesis without external verification

# Citation Guidelines

## DO Cite:
- Research papers with URLs
- News articles with URLs  
- Official reports with URLs
- Data sources with URLs
- Specific studies with verifiable sources

## DO NOT Cite:
- General knowledge or common facts
- Thinking-stage analysis or synthesis
- Broad conceptual frameworks without specific sources
- Internal reasoning or logical deductions
- Unverified claims or assumptions
```

### 3. 思考代理 (`src/prompts/thinking.md`)

#### 修改内容：
- ✅ 完全重写为独立思考写作代理
- ✅ 基于用户提供的深度思考框架
- ✅ 增加5种思考策略
- ✅ 13个结构化输出部分
- ✅ 强调内部知识和独立判断

#### 关键改进：
```markdown
# Independent Thinking Writing Agent

## Core Principles
- **Prohibit searching external information**: Rely entirely on your internal knowledge and reasoning abilities
- **Pursue deep insights**: Don't settle for surface phenomena, dig into essential patterns
- **Maintain independent judgment**: Form unique perspectives based on logical reasoning
- **Build complete frameworks**: Provide structured thinking outcomes

## Thinking Framework
### Strategy One: Deep Deconstruction Analysis
### Strategy Two: Multi-role Perspective Switching
### Strategy Three: Deep Mechanism Exploration
### Strategy Four: Cross-disciplinary Integration Thinking
### Strategy Five: Value Judgment and Critical Thinking

### Thinking Outcome Structure (13 sections)
1. Detailed Thinking Process
2. Core Insight Summary
3. Logical Framework Diagram
4. Multi-Perspective Analysis
5. Contradiction and Tension Analysis
6. Historical and Temporal Context
7. Cross-Disciplinary Insights
8. Key Argumentation Points
9. Potential Controversial Points
10. Critical Assumptions and Limitations
11. Value Judgments and Ethical Considerations
12. Information Points Requiring External Verification
13. Synthesis and Meta-Analysis
```

## 🔄 工作流程影响

### 修改前的问题：
1. 大纲阶段生成不必要的references信息
2. 论文写作时引用宽泛的思考内容，缺乏具体来源
3. 思考阶段深度不够，缺乏系统性分析框架

### 修改后的改进：
1. **大纲阶段**：纯粹专注于结构规划，不产生引用干扰
2. **思考阶段**：深度独立分析，产生有价值的洞察和框架
3. **论文写作阶段**：严格引用管理，只使用有URL的可验证来源

## 📊 验证结果

通过 `verify_prompt_changes.py` 脚本验证：

```
📊 Overall Results: 3/3 templates updated correctly
🎉 All prompt templates have been successfully updated!

📝 Summary of Changes:
  • Outline Writer: No longer generates references, focuses on structure
  • Paper Writer: Only cites sources with verifiable URLs
  • Thinking Agent: Enhanced with deep analysis framework
```

## 🚀 使用建议

### 对于用户：
1. 在大纲审核阶段，专注于结构和内容规划的合理性
2. 在思考阶段，期待更深入的分析和独特洞察
3. 在论文写作阶段，确保所有引用都有可验证的来源

### 对于开发者：
1. 监控引用质量，确保只有URL来源被引用
2. 观察思考阶段输出的深度和价值
3. 根据实际使用情况进一步优化提示词

## 📁 相关文件

- `src/prompts/outline_writer_simple.md` - 大纲生成器
- `src/prompts/paper_writer_simple.md` - 论文写作器  
- `src/prompts/thinking.md` - 思考代理
- `verify_prompt_changes.py` - 验证脚本
- `test_improved_prompts.py` - 测试脚本

## 🔮 未来改进方向

1. 根据实际使用反馈进一步优化引用策略
2. 考虑添加更多思考策略和分析维度
3. 优化大纲和论文写作的衔接机制
4. 增加质量评估和自动验证机制 