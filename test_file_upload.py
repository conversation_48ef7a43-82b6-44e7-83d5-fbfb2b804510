#!/usr/bin/env python3
"""
Test File Upload with Multiple Formats

This script tests the new file upload functionality in DeerFlow 
that supports different file formats including PDF, text files, etc.
"""

import requests
import json
import time
import os
import io

# Set environment variables for local RAGFlow server
os.environ["RAGFLOW_API_URL"] = "http://localhost"  # 用户本地的RAGFlow服务
os.environ["RAGFLOW_API_KEY"] = "ragflow-dhYjQ4NGY2NDBmMDExZjA4MmM0NTI5NW"  # 请替换为您的实际API密钥

# Test configuration
DEERFLOW_API_URL = "http://localhost:8000"


def create_test_files():
    """Create test files in different formats."""
    
    # Create a simple text file
    text_content = """
    This is a sample text file for testing RAG upload functionality.
    
    It contains information about artificial intelligence and machine learning.
    Machine learning is a subset of AI that enables computers to learn from data.
    
    Key concepts include:
    - Neural networks
    - Deep learning
    - Natural language processing
    - Computer vision
    
    This content should be indexable and searchable through the RAG system.
    """.strip()
    
    # Create a simple PDF content (we'll simulate this as bytes)
    # In a real scenario, this would be actual PDF binary data
    pdf_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n' \
                  b'This is simulated PDF content for testing purposes. ' \
                  b'In a real implementation, this would contain actual PDF structure and text content.'
    
    # Create markdown content
    markdown_content = """
# RAG System Test Document

This is a **markdown** document for testing the RAG upload system.

## Features

- Document upload and parsing
- Semantic search capabilities  
- Integration with chat interface
- Support for multiple file formats

## Technical Details

The system uses:
1. FastAPI for the backend
2. RAGFlow for document processing
3. Vector embeddings for search

> This document should be processed and made searchable.
    """.strip()
    
    return {
        "test_document.txt": ("text/plain", text_content.encode('utf-8')),
        "sample_research.pdf": ("application/pdf", pdf_content),
        "readme.md": ("text/markdown", markdown_content.encode('utf-8')),
    }


def create_test_dataset():
    """Create a test dataset for file upload."""
    print("🆕 Creating test dataset...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/datasets"
    payload = {
        "name": f"File Upload Test Dataset {int(time.time())}",
        "description": "Testing file upload with multiple formats",
        "chunk_method": "naive",
        "embedding_model": "BAAI/bge-large-en-v1.5@BAAI"
    }
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            dataset = result.get("dataset", {})
            dataset_id = dataset.get("id")
            print(f"✅ Dataset created: {dataset.get('name')} (ID: {dataset_id})")
            return dataset_id
    
    print(f"❌ Failed to create dataset: {response.status_code} - {response.text}")
    return None


def test_file_upload(dataset_id, filename, content_type, content_bytes):
    """Test uploading a single file."""
    print(f"\n📄 Testing upload: {filename} ({content_type})")
    
    url = f"{DEERFLOW_API_URL}/api/rag/upload"
    
    # Prepare multipart form data
    files = {
        'file': (filename, io.BytesIO(content_bytes), content_type)
    }
    data = {
        'dataset_id': dataset_id
    }
    
    try:
        response = requests.post(url, files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Upload successful!")
                data = result.get("data", {})
                documents = data.get("documents", []) if isinstance(data, dict) else data
                
                for doc in documents:
                    if isinstance(doc, dict):
                        print(f"   📄 Document ID: {doc.get('id')}")
                        print(f"   📝 Name: {doc.get('name')}")
                        print(f"   📏 Size: {doc.get('size')} bytes")
                        print(f"   🏷️ Type: {doc.get('type')}")
                        return doc.get('id')
                return True
            else:
                print(f"❌ Upload failed: {result.get('message')}")
                return None
        else:
            print(f"❌ HTTP Error: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("⏰ Upload timed out")
        return None
    except Exception as e:
        print(f"❌ Upload error: {str(e)}")
        return None


def test_document_listing(dataset_id):
    """Test listing uploaded documents."""
    print(f"\n📋 Listing documents in dataset {dataset_id}...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/datasets/{dataset_id}/documents"
    response = requests.get(url)
    
    if response.status_code == 200:
        result = response.json()
        documents = result.get("documents", [])
        print(f"✅ Found {len(documents)} documents:")
        
        for doc in documents:
            print(f"   📄 {doc.get('name')} - Status: {doc.get('run')} - Size: {doc.get('size')} bytes")
        
        return documents
    else:
        print(f"❌ Failed to list documents: {response.status_code}")
        return []


def test_document_parsing(dataset_id, document_ids):
    """Test parsing uploaded documents."""
    print(f"\n⚙️ Starting document parsing...")
    
    url = f"{DEERFLOW_API_URL}/api/rag/documents/parse"
    payload = {
        "dataset_id": dataset_id,
        "document_ids": document_ids
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print("✅ Document parsing started!")
            return True
    
    print(f"❌ Failed to start parsing: {response.status_code}")
    return False


def main():
    """Main test function."""
    print("🚀 File Upload Test with Multiple Formats")
    print("=" * 60)
    
    # Step 1: Create test dataset
    dataset_id = create_test_dataset()
    if not dataset_id:
        print("❌ Failed to create dataset. Exiting.")
        return
    
    # Step 2: Create test files
    test_files = create_test_files()
    print(f"\n📁 Created {len(test_files)} test files:")
    for filename, (content_type, _) in test_files.items():
        print(f"   📄 {filename} ({content_type})")
    
    # Step 3: Upload each file
    document_ids = []
    for filename, (content_type, content_bytes) in test_files.items():
        doc_id = test_file_upload(dataset_id, filename, content_type, content_bytes)
        if doc_id:
            document_ids.append(doc_id)
    
    # Step 4: List uploaded documents
    documents = test_document_listing(dataset_id)
    
    # Step 5: Start parsing
    if document_ids:
        test_document_parsing(dataset_id, document_ids)
        
        # Wait a moment and check status again
        print("\n⏳ Waiting for parsing to complete...")
        time.sleep(2)
        test_document_listing(dataset_id)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 File Upload Test Summary:")
    print(f"   📋 Dataset ID: {dataset_id}")
    print(f"   📄 Files uploaded: {len(document_ids)}/{len(test_files)}")
    print(f"   ✅ Upload success rate: {len(document_ids)/len(test_files)*100:.1f}%")
    
    if len(document_ids) == len(test_files):
        print("\n🎉 All file uploads successful!")
        print("\n📋 Next steps:")
        print("1. Verify documents are parsed correctly")
        print("2. Test retrieval with various queries")
        print("3. Test in Postman with real files")
    else:
        print("\n⚠️ Some uploads failed. Check the logs above.")
    
    print(f"\n🔧 Postman Test Instructions:")
    print(f"   URL: POST {DEERFLOW_API_URL}/api/rag/upload")
    print(f"   Form Data:")
    print(f"     - file: [Select your file]")
    print(f"     - dataset_id: {dataset_id}")
    print(f"   Expected: 200 OK with success: true")


if __name__ == "__main__":
    main() 