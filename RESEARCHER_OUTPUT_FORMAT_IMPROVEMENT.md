# Researcher节点输出格式改进

## 改进概述

为了提供更详细和透明的研究过程，我们在researcher节点的输出格式中添加了一个新的"Source Analysis"部分，该部分会在Problem Statement之前展示对每个搜索网站的逐一分析。

## 新增的Source Analysis部分

### 位置
- 在原有的**Problem Statement**部分之前
- 作为输出的第一个主要部分

### 内容要求
对于每个在研究过程中访问的网站/来源，提供：

1. **源链接格式**: `[Source Title](https://example.com/page1)`
2. **网站内容总结**: 对该网站内容的大致总结及其与研究主题的相关性
3. **关键信息提取**: 从该来源提取的关键信息
4. **可信度评估**: 对来源的可信度和有用性的评估

### 示例格式

```markdown
## Source Analysis

[OpenAI官方博客 - GPT-4在医疗诊断中的应用](https://openai.com/blog/gpt-4-medical-diagnosis)

这是OpenAI的官方博客文章，详细介绍了GPT-4在医疗诊断领域的最新应用。文章内容权威可靠，提供了具体的应用案例和技术细节。从该来源获得了关于AI模型在影像诊断、病理分析等方面的最新进展信息。该来源具有很高的可信度，是AI医疗应用领域的权威信息源。

[Nature Medicine期刊 - 深度学习在放射学诊断中的突破](https://nature.com/articles/s41591-2023-medical-ai)

这是Nature Medicine期刊发表的同行评议学术论文，研究了深度学习技术在放射学诊断中的应用效果。文章提供了严格的实验数据和统计分析，展示了AI技术在医学影像诊断中的准确性提升。该来源是高质量的学术文献，具有极高的可信度和科学价值。

[医疗AI公司Zebra Medical官网](https://zebra-med.com/solutions)

这是专业医疗AI公司的官方网站，展示了其AI诊断解决方案的商业应用情况。网站提供了实际的产品信息和临床应用案例，但作为商业网站可能存在一定的营销倾向。从该来源获得了AI医疗诊断技术的实际商业化应用信息。
```

## 完整的新输出格式结构

```markdown
## Source Analysis
[对每个访问的网站进行逐一分析]

## Problem Statement
[重述问题以确保清晰]

## Research Findings
[按主题组织研究发现]

## Conclusion
[基于收集信息的综合回应]

## References
[所有使用来源的完整URL列表]
```

## 实现细节

### 模板修改
在`src/prompts/researcher.md`的Output Format部分添加了新的Source Analysis要求：

```markdown
- **Source Analysis**: For each website/source you accessed during research, provide:
    - The source link in format: [Source Title](https://example.com/page1)
    - A brief summary of the website's content and its relevance to the research topic
    - Key information extracted from this source
    - Assessment of the source's credibility and usefulness
```

### 验证测试
- ✅ 新的Source Analysis部分正确添加到模板中
- ✅ 部分顺序正确（Source Analysis → Problem Statement → Research Findings → Conclusion → References）
- ✅ 支持多语言（中文和英文）
- ✅ 保持原有功能完整性

## 改进效果

### 1. 提高透明度
- 用户可以清楚地看到researcher访问了哪些网站
- 了解每个来源的内容和相关性
- 评估信息来源的质量和可信度

### 2. 增强可信度
- 明确展示研究过程和信息来源
- 提供对每个来源的客观评估
- 帮助用户判断研究结果的可靠性

### 3. 改善用户体验
- 更详细的研究过程展示
- 更好的信息组织结构
- 更容易理解和验证的输出

### 4. 支持后续分析
- 为其他节点（如thinking节点）提供更丰富的信息
- 便于进行源头追溯和验证
- 支持更深入的分析和综合

## 使用场景

### 学术研究
- 展示文献来源的权威性和相关性
- 提供同行评议期刊和学术机构的信息
- 评估研究方法和数据质量

### 商业分析
- 区分官方信息和第三方报告
- 评估商业来源的客观性
- 提供市场数据的可信度分析

### 技术调研
- 展示技术文档和官方资源
- 评估开源项目和社区信息
- 提供实际应用案例的可靠性

## 总结

这次改进显著提升了researcher节点输出的质量和透明度：

1. **更详细的过程展示**: 用户可以清楚了解研究过程
2. **更好的信息组织**: 逻辑清晰的输出结构
3. **更高的可信度**: 明确的来源评估和质量判断
4. **更强的实用性**: 便于后续分析和验证

researcher节点现在能够提供更专业、更透明、更有价值的研究输出，为整个工作流提供更可靠的信息基础。 