# Upstream for your API backend
upstream backend_api {
    server 49.51.230.37:9071;
    keepalive 64; # Optional: for performance
}

# Upstream for your Frontend
upstream frontend_app {
    server ************:3000;
    # keepalive 32; # Optional: for performance
}

# Server block for HTTP to HTTPS redirection
server {
    listen 80;
    listen [::]:80;
    server_name thinkwriter.ai www.thinkwriter.ai; # 替换为你的域名

    # For Let's Encrypt ACME challenge (if you use it)
    location /.well-known/acme-challenge/ {
        root /var/www/certbot; # 确保这个目录存在或被正确处理
    }

    location / {
        return 301 https://$host$request_uri;
    }
}

# Main server block for HTTPS
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name thinkwriter.ai www.thinkwriter.ai; # 替换为你的域名

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/thinkwriter.ai.pem;       # 替换为你的证书文件名
    ssl_certificate_key /etc/nginx/ssl/thinkwriter.ai.key; # 替换为你的私钥文件名
    # ssl_dhparam /etc/nginx/ssl/dhparam.pem; # Optional: for stronger DHE ciphers

    # SSL hardening (recommended)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s; # Google DNS, for OCSP stapling, or use Cloudflare's *******
    resolver_timeout 5s;

    # Security Headers (recommended)
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    # add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; ..."; # Customize carefully

    # Logging
    access_log /var/log/nginx/access.log main_ext; # Using a custom log format (see below)
    error_log /var/log/nginx/error.log warn;

    # Max body size (e.g., for file uploads)
    client_max_body_size 100M; # Adjust as needed

    # API Backend Proxy
    location /api/ {
        # Important: The trailing slash in /api/ means that if a request is /api/users,
        # Nginx will request /api/users from the backend.
        # If you wanted to strip /api/ and request just /users from backend,
        # you would use: proxy_pass http://backend_api/; (note trailing slash)
        # or a rewrite rule. For now, we assume backend_api handles /api/... paths.

        proxy_pass http://backend_api; # Requests to backend_api will include /api/ prefix

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade; # For WebSockets if API uses them
        proxy_set_header Connection 'upgrade';  # For WebSockets
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 关键：增加读取超时时间
        proxy_read_timeout 720s; # 12 分钟 (10 分钟 + 缓冲) 或更长
        proxy_send_timeout 60s;
        proxy_buffering off; # 关闭对 SSE 的代理缓冲
        proxy_cache off; # 明确关闭 SSE 的缓存
        # proxy_buffering on; # Default is on, adjust if needed
    }

    # Frontend Application Proxy
    location / {
        proxy_pass http://frontend_app;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade; # For WebSockets (e.g., Hot Module Replacement in dev)
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Custom error pages (optional)
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html; # Nginx's default error page location
    }
}

# It's good practice to define log formats in the http block of nginx.conf
# but for simplicity, if you only have one conf file or map the whole conf.d,
# you might not have direct access to edit the main nginx.conf http block.
# If you are mapping the entire /etc/nginx/conf.d, you can create a separate file
# e.g., log_formats.conf in that directory with:
#
# log_format main_ext '$remote_addr - $remote_user [$time_local] "$request" '
#                     '$status $body_bytes_sent "$http_referer" '
#                     '"$http_user_agent" "$http_x_forwarded_for" '
#                     'rt=$request_time uct="$upstream_connect_time" uht="$upstream_header_time" urt="$upstream_response_time"';
#
# If you don't do this, you can just use the default 'combined' log format or remove 'main_ext'.
# For now, I'll assume you might use the default log format or just 'main'.
# So, change access_log lines to:
# access_log /var/log/nginx/access.log;
# or
# access_log /var/log/nginx/access.log combined;