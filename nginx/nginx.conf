user  nginx;
worker_processes  auto; # 或 1

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    # 这是你定义的 main_ext 日志格式可以放在这里
    log_format main_ext '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       'rt=$request_time uct="$upstream_connect_time" uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log  /var/log/nginx/access.log  main; # 你可以全局使用 main_ext

    sendfile        on;
    #tcp_nopush     on;

    # 在这里添加你的全局 HTTP 配置
    keepalive_timeout  600; # Nginx 与客户端的 keepalive 超时
    # proxy_connect_timeout 60s; # 全局的代理连接超时 (也可以在 server/location 中覆盖)
    # proxy_send_timeout 60s;    # 全局的代理发送超时
    # proxy_read_timeout 60s;    # 全局的代理读取超时
    # client_max_body_size 100M; # 全局的最大请求体大小

    # Gzip Settings (示例)
    # gzip on;
    # gzip_vary on;
    # gzip_proxied any;
    # gzip_comp_level 6;
    # gzip_buffers 16 8k;
    # gzip_http_version 1.1;
    # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 确保这行或类似的行存在，以加载你的 server 配置
    include /etc/nginx/conf.d/*.conf;
    # 有些配置可能是 include /etc/nginx/sites-enabled/*;
}