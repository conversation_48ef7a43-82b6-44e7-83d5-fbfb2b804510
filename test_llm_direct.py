#!/usr/bin/env python3

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.llms.llm import get_llm_by_type

def test_llm_direct():
    """Test the LLM directly with a simple prompt."""
    
    try:
        llm = get_llm_by_type("basic")
        print(f"LLM created successfully: {llm}")
        
        # Test with a simple message
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Please respond in JSON format."},
            {"role": "user", "content": "Create a simple outline for a paper about AI. Respond with JSON containing title and sections."}
        ]
        
        print("Sending test message to LLM...")
        response = llm.invoke(messages)
        print(f"LLM response: {response}")
        print(f"Response content: {response.content}")
        
        # Test streaming
        print("\nTesting streaming...")
        full_response = ""
        stream = llm.stream(messages)
        for chunk in stream:
            full_response += chunk.content
            print(f"Chunk: {chunk.content}")
        
        print(f"\nFull streaming response: {full_response}")
        
        return True
        
    except Exception as e:
        print(f"LLM test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_llm_direct()
    sys.exit(0 if success else 1) 