#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证节点中的messages更新功能
"""

import os
import sys
import json
import logging
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from langchain_core.messages import HumanMessage, AIMessage
from src.graph.nodes import update_messages_to_database
from src.database.database import SessionLocal
from src.database.models import ChatHistory, User

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_data():
    """
    创建测试数据
    """
    db = SessionLocal()
    try:
        # 创建测试用户（如果不存在）
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not test_user:
            test_user = User(
                email="<EMAIL>",
                total_credits=1000,
                remaining_credits=1000
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
        
        # 创建测试聊天历史（如果不存在）
        test_thread_id = "test-thread-123"
        test_chat = db.query(ChatHistory).filter(ChatHistory.thread_id == test_thread_id).first()
        if not test_chat:
            test_chat = ChatHistory(
                user_id=test_user.id,
                thread_id=test_thread_id,
                title="测试对话",
                non_reasoning_input_tokens=0,
                non_reasoning_output_tokens=0,
                reasoning_input_tokens=0,
                reasoning_output_tokens=0
            )
            db.add(test_chat)
            db.commit()
            db.refresh(test_chat)
        
        return test_thread_id, test_user.id
        
    except Exception as e:
        logger.error(f"创建测试数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def test_messages_update():
    """
    测试messages更新功能
    """
    try:
        # 创建测试数据
        thread_id, user_id = create_test_data()
        
        # 创建测试状态，包含各种类型的消息
        test_state = {
            "thread_id": thread_id,
            "user_id": user_id,
            "messages": [
                HumanMessage(content="用户的第一条消息", name="user"),
                AIMessage(content="AI的回复消息", name="assistant"),
                HumanMessage(content="用户的第二条消息"),
                {"role": "system", "content": "系统消息"},
                {"role": "user", "content": "字典格式的用户消息"},
            ]
        }
        
        # 测试更新messages到数据库
        logger.info("开始测试messages更新...")
        
        # 测试多条消息的追加
        test_messages = [
            ("test_agent", "第一条测试消息", "human"),
            ("test_agent", "第二条测试消息", "ai"),  
            ("system", "系统消息", "system"),
        ]
        
        for agent, content, msg_type in test_messages:
            update_messages_to_database(thread_id, agent, content, msg_type)
        
        # 验证更新结果
        db = SessionLocal()
        try:
            chat_history = db.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).first()
            if chat_history and chat_history.messages:
                logger.info("✅ messages更新成功!")
                logger.info(f"存储的messages数量: {len(chat_history.messages)}")
                logger.info("存储的messages内容:")
                for i, msg in enumerate(chat_history.messages):
                    logger.info(f"  消息 {i+1}: {msg}")
                
                # 验证消息格式和内容
                expected_messages = [
                    ("test_agent", "第一条测试消息", "human"),
                    ("test_agent", "第二条测试消息", "ai"),
                    ("system", "系统消息", "system"),
                ]
                
                if len(chat_history.messages) >= len(expected_messages):
                    for i, (expected_agent, expected_content, expected_type) in enumerate(expected_messages):
                        msg = chat_history.messages[i]
                        if (msg.get("agent") == expected_agent and 
                            msg.get("content") == expected_content and 
                            msg.get("type") == expected_type):
                            logger.info(f"  ✅ 消息 {i+1} 格式和内容正确")
                        else:
                            logger.warning(f"  ⚠️  消息 {i+1} 不匹配，期望: {expected_agent}, {expected_content}, {expected_type}")
                            logger.warning(f"  实际: {msg.get('agent')}, {msg.get('content')}, {msg.get('type')}")
                else:
                    logger.warning(f"  ⚠️  消息数量不足，期望至少 {len(expected_messages)} 条，实际 {len(chat_history.messages)} 条")
                
            else:
                logger.error("❌ messages更新失败，数据库中没有找到messages数据")
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise


def test_empty_state():
    """
    测试空状态的处理
    """
    logger.info("测试空状态处理...")
    
    # 测试没有thread_id的情况
    update_messages_to_database("", "test_agent", "测试内容", "human")
    
    # 测试空内容的情况
    update_messages_to_database("test-thread-456", "test_agent", "", "human")
    
    # 测试不存在的thread_id
    update_messages_to_database("nonexistent-thread", "test_agent", "测试内容", "human")
    
    logger.info("✅ 空状态测试完成")


def cleanup_test_data():
    """
    清理测试数据
    """
    try:
        db = SessionLocal()
        
        # 删除测试聊天历史
        test_chat = db.query(ChatHistory).filter(ChatHistory.thread_id == "test-thread-123").first()
        if test_chat:
            db.delete(test_chat)
        
        # 删除测试用户
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if test_user:
            db.delete(test_user)
        
        db.commit()
        logger.info("✅ 测试数据清理完成")
        
    except Exception as e:
        logger.error(f"清理测试数据失败: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("开始测试messages更新功能")
    
    try:
        # 运行测试
        test_messages_update()
        test_empty_state()
        
        logger.info("🎉 所有测试通过!")
        
        # 询问是否清理测试数据
        cleanup = input("是否清理测试数据? (y/n): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_data()
        else:
            logger.info("保留测试数据以供进一步检查")
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1) 