#!/usr/bin/env python3

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.prompts.template import apply_prompt_template
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP
from src.prompts.planner_model import Plan, Step, StepType
from langchain_core.messages import HumanMessage

def test_outline_full():
    """Test the complete outline writer workflow."""
    
    # Create a realistic plan with research results
    plan = Plan(
        locale="zh-CN",
        has_enough_context=True,
        title="人工智能在医疗诊断中的应用研究",
        thought="需要深入研究AI技术在医疗诊断领域的应用现状、技术挑战和发展前景",
        steps=[
            Step(
                need_web_search=True,
                title="AI医疗诊断技术调研",
                description="调研当前AI在医疗诊断中的主要技术和应用",
                step_type=StepType.RESEARCH,
                execution_res="AI在医疗诊断中主要应用包括医学影像分析、病理诊断、药物发现等领域。深度学习技术在医学影像识别方面取得了显著进展，准确率已接近甚至超过专业医生水平。主要技术包括卷积神经网络(CNN)用于图像识别，循环神经网络(RNN)用于序列数据分析，以及Transformer架构用于多模态数据融合。"
            ),
            Step(
                need_web_search=False,
                title="技术挑战分析", 
                description="分析AI医疗诊断面临的技术挑战",
                step_type=StepType.PROCESSING,
                execution_res="AI医疗诊断面临的主要挑战包括：1)数据质量和标注问题，医疗数据往往存在噪声和不一致性；2)模型可解释性，医生需要理解AI的决策过程；3)数据隐私和安全，医疗数据涉及患者隐私；4)监管和伦理问题，需要符合医疗行业的严格标准；5)跨域泛化能力，模型在不同医院和设备上的表现可能不一致。"
            )
        ]
    )
    
    # Create state similar to what outline_writer_node receives
    state = {
        "messages": [HumanMessage(content="撰写一篇关于人工智能在医疗诊断中应用的学术论文")],
        "current_plan": plan,
        "locale": "zh-CN"
    }
    
    try:
        # Prepare research results and planning information (same as outline_writer_node)
        current_plan = state.get("current_plan")
        research_results = ""
        research_thinking = ""
        
        # Collect research results from completed steps
        if current_plan and current_plan.steps:
            research_results_list = []
            for step in current_plan.steps:
                if step.execution_res:
                    research_results_list.append({
                        "title": step.title,
                        "description": step.description,
                        "result": step.execution_res
                    })
            research_results = json.dumps(research_results_list, ensure_ascii=False)
        
        # Get user query from messages - use the original user query, not the latest message
        user_query = ""
        if state["messages"]:
            # Find the first user message (original query)
            for msg in state["messages"]:
                if hasattr(msg, 'role') and msg.role == "user":
                    user_query = msg.content
                    break
                elif hasattr(msg, 'content'):
                    user_query = msg.content
                    break
            # Fallback to first message if no user role found
            if not user_query:
                user_query = state["messages"][0].content if state["messages"] else ""
        
        print(f"User query: {user_query}")
        print(f"Research results length: {len(research_results)}")
        
        # Apply outline writer prompt template
        template_state = {
            **state,
            "user_query": user_query,
            "current_plan": json.dumps(current_plan.model_dump() if current_plan else {}, ensure_ascii=False),
            "research_thinking": research_thinking,
            "research_results": research_results,
            "locale": state.get("locale", "en-US")
        }
        
        print(f"Template state keys: {list(template_state.keys())}")
        
        messages = apply_prompt_template("outline_writer", template_state, Configuration())
        
        print(f"Generated {len(messages)} messages")
        print(f"System prompt length: {len(messages[0]['content'])}")
        print(f"First 200 chars of system prompt: {messages[0]['content'][:200]}...")
        
        # Get LLM for outline generation (same as outline_writer_node)
        if AGENT_LLM_MAP.get("outline_writer", "basic") == "basic":
            llm = get_llm_by_type("basic")
        else:
            llm = get_llm_by_type(AGENT_LLM_MAP.get("outline_writer", "basic"))
        
        print(f"Using LLM: {llm}")
        
        # Generate outline (same as outline_writer_node)
        print("Calling LLM stream...")
        full_response = ""
        response = llm.stream(messages)
        for i, chunk in enumerate(response):
            full_response += chunk.content
            if i < 5:  # Print first few chunks
                print(f"Chunk {i}: {chunk.content[:50]}...")
        
        print(f"Full response length: {len(full_response)}")
        print(f"Full response: {full_response}")
        
        if not full_response.strip():
            print("ERROR: Response is empty!")
            return False
        
        # Try to parse JSON
        from src.utils.json_utils import repair_json_output
        try:
            outline_data = json.loads(repair_json_output(full_response))
            print("JSON parsing successful!")
            print(f"Outline title: {outline_data.get('title', 'N/A')}")
            return True
        except json.JSONDecodeError as e:
            print(f"JSON parsing failed: {e}")
            print(f"Repaired JSON: {repair_json_output(full_response)}")
            return False
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_outline_full()
    sys.exit(0 if success else 1) 