#!/usr/bin/env python3

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP

def test_llm_config():
    """Test LLM configuration with bind() parameters."""
    
    try:
        # Get LLM same as outline_writer_node
        if AGENT_LLM_MAP.get("outline_writer", "basic") == "basic":
            llm = get_llm_by_type("basic")
        else:
            llm = get_llm_by_type(AGENT_LLM_MAP.get("outline_writer", "basic"))
        
        print(f"Original LLM: {llm}")
        
        # Test without bind first
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Respond with a simple JSON object."},
            {"role": "user", "content": "Create a simple outline for a paper about AI. Respond with JSON containing title and sections."}
        ]
        
        print("Testing LLM without bind()...")
        response = llm.invoke(messages)
        print(f"Response without bind: {response.content[:200]}...")
        
        # Now test with bind
        print("\nTesting LLM with bind()...")
        llm_bound = llm.bind(
            max_tokens=4000,
            temperature=0.7,
        )
        print(f"Bound LLM: {llm_bound}")
        
        response_bound = llm_bound.invoke(messages)
        print(f"Response with bind: {response_bound.content[:200]}...")
        
        # Test if bind parameters are causing issues
        print("\nTesting with different bind parameters...")
        llm_bound2 = llm.bind(max_tokens=2000)
        response_bound2 = llm_bound2.invoke(messages)
        print(f"Response with max_tokens only: {response_bound2.content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_llm_config()
    sys.exit(0 if success else 1) 