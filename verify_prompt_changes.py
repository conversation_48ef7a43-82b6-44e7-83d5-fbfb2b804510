#!/usr/bin/env python3
"""
Simple verification script for prompt changes.
"""

def check_outline_writer():
    """Check if outline writer template has been updated correctly."""
    print("🔍 Checking Outline Writer Template...")
    
    try:
        with open("src/prompts/outline_writer_simple.md", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("ONLY plan citations from research materials that have specific URLs", "✅ Plans citations only for sources with URLs"),
            ("plannedCitations", "✅ Includes plannedCitations in output format"),
            ("Citation Planning Guidelines", "✅ Has citation planning guidelines"),
        ]
        
        passed = 0
        for check, message in checks:
            if check in content:
                print(f"  {message}")
                passed += 1
            else:
                print(f"  ❌ Missing: {check}")
        
        print(f"  📊 Outline Writer: {passed}/{len(checks)} checks passed\n")
        return passed == len(checks)
        
    except Exception as e:
        print(f"  ❌ Error reading outline writer template: {e}\n")
        return False

def check_paper_writer():
    """Check if paper writer template has been updated correctly."""
    print("🔍 Checking Paper Writer Template...")
    
    try:
        with open("src/prompts/paper_writer_simple.md", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("ONLY cite research materials that have verifiable URLs", "✅ Enforces URL requirement"),
            ("AVOID**: General statements", "✅ Lists what to avoid citing"),
            ("DO NOT Cite:", "✅ Has clear 'do not cite' section"),
            ("When in doubt about citing", "✅ Provides guidance for uncertainty"),
        ]
        
        passed = 0
        for check, message in checks:
            if check in content:
                print(f"  {message}")
                passed += 1
            else:
                print(f"  ❌ Missing: {check}")
        
        print(f"  📊 Paper Writer: {passed}/{len(checks)} checks passed\n")
        return passed == len(checks)
        
    except Exception as e:
        print(f"  ❌ Error reading paper writer template: {e}\n")
        return False

def check_thinking_agent():
    """Check if thinking agent template has been updated correctly."""
    print("🔍 Checking Thinking Agent Template...")
    
    try:
        with open("src/prompts/thinking.md", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("Independent Thinking Writing Agent", "✅ Has independent thinking role"),
            ("Deep Deconstruction Analysis", "✅ Includes deep analysis strategy"),
            ("Multi-role Perspective Switching", "✅ Includes multi-perspective analysis"),
            ("Cross-disciplinary Integration", "✅ Includes cross-disciplinary thinking"),
            ("Value Judgment and Critical Thinking", "✅ Includes critical thinking"),
            ("Prohibit searching external information", "✅ Emphasizes internal knowledge"),
            ("Focus on \"why\" rather than just \"what\"", "✅ Emphasizes deep reasoning"),
        ]
        
        passed = 0
        for check, message in checks:
            if check in content:
                print(f"  {message}")
                passed += 1
            else:
                print(f"  ❌ Missing: {check}")
        
        print(f"  📊 Thinking Agent: {passed}/{len(checks)} checks passed\n")
        return passed == len(checks)
        
    except Exception as e:
        print(f"  ❌ Error reading thinking agent template: {e}\n")
        return False

def main():
    """Run all verification checks."""
    print("🚀 Verifying Prompt Template Changes\n")
    
    results = []
    results.append(check_outline_writer())
    results.append(check_paper_writer())
    results.append(check_thinking_agent())
    
    total_passed = sum(results)
    total_tests = len(results)
    
    print(f"📊 Overall Results: {total_passed}/{total_tests} templates updated correctly")
    
    if all(results):
        print("🎉 All prompt templates have been successfully updated!")
        print("\n📝 Summary of Changes:")
        print("  • Outline Writer: No longer generates references, focuses on structure")
        print("  • Paper Writer: Only cites sources with verifiable URLs")
        print("  • Thinking Agent: Enhanced with deep analysis framework")
    else:
        print("⚠️  Some templates may need additional updates.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 